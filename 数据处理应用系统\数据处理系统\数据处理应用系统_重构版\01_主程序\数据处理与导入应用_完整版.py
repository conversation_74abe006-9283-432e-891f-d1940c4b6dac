# -*- coding: utf-8 -*-
"""
数据处理与导入应用 - 完整版
集成了自定义异常处理系统的完整应用程序

版本: 2.0
作者: AI Assistant
日期: 2025-06-11
"""

import os
import sys
import locale

# 🔧 强化编码设置 - 解决乱码问题
try:
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'

    # 设置locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, '')
            except:
                pass

    # 重新配置标准输出
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')

    # 设置默认编码
    if hasattr(sys, 'setdefaultencoding'):
        sys.setdefaultencoding('utf-8')

except Exception:
    pass  # 静默处理编码设置失败

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import subprocess
import configparser
from datetime import datetime
import logging
import queue
import json
import functools
import traceback
import shutil
from typing import Dict, List, Optional, Callable, Any

# ==================== 自定义异常处理系统 ====================

class DataAppError(Exception):
    """数据应用基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录"""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat()
        }
    
    def get_user_message(self) -> str:
        """获取用户友好的错误信息"""
        return self.message

# 文件相关异常
class FileError(DataAppError):
    """文件操作基础异常"""
    pass

class FileNotFoundError(FileError):
    """文件不存在异常"""
    
    def __init__(self, file_path: str, operation: str = "访问"):
        self.file_path = file_path
        self.operation = operation
        message = f"无法{operation}文件: {file_path}"
        super().__init__(message, details={'file_path': file_path, 'operation': operation})
    
    def get_user_message(self) -> str:
        return f"文件不存在: {self.file_path}\n请检查文件路径是否正确。"

class FileValidationError(FileError):
    """文件验证异常"""
    
    def __init__(self, file_path: str, validation_issue: str, expected: str = None):
        self.file_path = file_path
        self.validation_issue = validation_issue
        self.expected = expected
        message = f"文件验证失败: {file_path} - {validation_issue}"
        super().__init__(message, details={
            'file_path': file_path,
            'issue': validation_issue,
            'expected': expected
        })
    
    def get_user_message(self) -> str:
        msg = f"文件格式错误: {self.file_path}\n问题: {self.validation_issue}"
        if self.expected:
            msg += f"\n期望: {self.expected}"
        return msg

class FileProcessingError(FileError):
    """文件处理异常"""
    
    def __init__(self, file_path: str, processing_stage: str, original_error: Exception = None):
        self.file_path = file_path
        self.processing_stage = processing_stage
        self.original_error = original_error
        message = f"文件处理失败: {file_path} 在 {processing_stage} 阶段"
        super().__init__(message, details={
            'file_path': file_path,
            'stage': processing_stage,
            'original_error': str(original_error) if original_error else None
        })
    
    def get_user_message(self) -> str:
        return f"文件处理失败: {self.file_path}\n失败阶段: {self.processing_stage}\n建议检查文件内容和格式。"

# 配置相关异常
class ConfigError(DataAppError):
    """配置相关基础异常"""
    pass

class ConfigValidationError(ConfigError):
    """配置验证异常"""
    
    def __init__(self, config_key: str, issue: str, current_value: Any = None):
        self.config_key = config_key
        self.issue = issue
        self.current_value = current_value
        message = f"配置验证失败: {config_key} - {issue}"
        super().__init__(message, details={
            'config_key': config_key,
            'issue': issue,
            'current_value': current_value
        })
    
    def get_user_message(self) -> str:
        return f"配置错误: {self.config_key}\n问题: {self.issue}\n请检查配置文件设置。"

class MissingConfigError(ConfigError):
    """缺少配置异常"""
    
    def __init__(self, config_key: str, config_section: str = None):
        self.config_key = config_key
        self.config_section = config_section
        message = f"缺少必要配置: {config_key}"
        if config_section:
            message += f" (节: {config_section})"
        super().__init__(message, details={
            'config_key': config_key,
            'config_section': config_section
        })
    
    def get_user_message(self) -> str:
        msg = f"缺少必要配置: {self.config_key}"
        if self.config_section:
            msg += f"\n配置节: {self.config_section}"
        msg += "\n请检查配置文件是否完整。"
        return msg

# 进程相关异常
class ProcessError(DataAppError):
    """进程执行基础异常"""
    pass

class ScriptNotFoundError(ProcessError):
    """脚本文件不存在异常"""
    
    def __init__(self, script_path: str, script_type: str = "处理脚本"):
        self.script_path = script_path
        self.script_type = script_type
        message = f"{script_type}不存在: {script_path}"
        super().__init__(message, details={
            'script_path': script_path,
            'script_type': script_type
        })
    
    def get_user_message(self) -> str:
        return f"{self.script_type}不存在: {self.script_path}\n请检查脚本文件路径配置。"

class ProcessExecutionError(ProcessError):
    """进程执行异常"""
    
    def __init__(self, command: str, return_code: int, stderr: str = None):
        self.command = command
        self.return_code = return_code
        self.stderr = stderr
        message = f"进程执行失败: {command} (返回码: {return_code})"
        super().__init__(message, details={
            'command': command,
            'return_code': return_code,
            'stderr': stderr
        })
    
    def get_user_message(self) -> str:
        msg = f"进程执行失败: {self.command}\n返回码: {self.return_code}"
        if self.stderr:
            msg += f"\n错误信息: {self.stderr}"
        return msg

# 异常处理器
class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, gui_updater=None, show_user_dialog=True):
        self.gui_updater = gui_updater
        self.show_user_dialog = show_user_dialog
    
    def handle_exception(self, exc: Exception, operation_name: str = "操作") -> bool:
        """处理异常"""
        if isinstance(exc, DataAppError):
            self._handle_custom_exception(exc, operation_name)
            return True
        else:
            self._handle_unknown_exception(exc, operation_name)
            return False
    
    def _handle_custom_exception(self, exc: DataAppError, operation_name: str):
        """处理自定义异常"""
        if self.gui_updater:
            log_message = f"{operation_name}失败: {exc.message}"
            self.gui_updater.safe_log(log_message, "error")
        
        if self.show_user_dialog:
            messagebox.showerror(f"{operation_name}失败", exc.get_user_message())
    
    def _handle_unknown_exception(self, exc: Exception, operation_name: str):
        """处理未知异常"""
        error_msg = f"{operation_name}时发生未知错误: {str(exc)}"
        
        if self.gui_updater:
            self.gui_updater.safe_log(error_msg, "error")
            self.gui_updater.safe_log(traceback.format_exc(), "error")
        
        if self.show_user_dialog:
            messagebox.showerror(
                f"{operation_name}失败",
                f"发生未知错误，请查看日志获取详细信息。\n\n错误: {str(exc)}"
            )

# 类方法异常处理装饰器
def class_exception_handler(operation_name: str = "操作", tab_type: str = "general", show_dialog=True):
    """类方法异常处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                # 自动检测gui_updater
                gui_updater = getattr(self, 'gui_updater', None)
                handler = ExceptionHandler(gui_updater, show_dialog)
                
                # 记录到特定选项卡
                if gui_updater:
                    actual_tab_type = getattr(self, 'tab_type', tab_type)
                    gui_updater.safe_log(f"异常发生: {str(e)}", actual_tab_type)
                
                handler.handle_exception(e, operation_name)
                
                # 自动恢复UI状态
                if hasattr(self, '_auto_restore_ui_state'):
                    self._auto_restore_ui_state()
                
                return None
        return wrapper
    return decorator

# ==================== 配置日志 ====================

log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"data_app_{datetime.now().strftime('%Y%m%d')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# ==================== 线程安全的GUI更新器 ====================

class SafeGUIUpdater:
    """线程安全的GUI更新器"""

    def __init__(self, root: tk.Tk):
        self.root = root
        self.message_queue = queue.Queue()
        self.log_widgets: Dict[str, tk.Text] = {}
        self.status_var: Optional[tk.StringVar] = None
        self._start_queue_processor()

    def _start_queue_processor(self):
        """启动队列处理器"""
        self.root.after(100, self._process_queue)

    def _process_queue(self):
        """处理消息队列"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()

                if message_type == "log":
                    self._update_log_widget(data["tab_type"], data["message"])
                elif message_type == "status":
                    self._update_status(data["status"])
                elif message_type == "button_state":
                    self._update_button_state(data["button"], data["state"])

        except queue.Empty:
            pass
        finally:
            self.root.after(100, self._process_queue)

    def _update_log_widget(self, tab_type: str, message: str):
        """更新日志控件"""
        widget = self.log_widgets.get(tab_type)
        if widget:
            widget.config(state=tk.NORMAL)
            timestamp = datetime.now().strftime('%H:%M:%S')
            widget.insert(tk.END, f"[{timestamp}] {message}\n")
            widget.see(tk.END)
            widget.config(state=tk.DISABLED)

    def _update_status(self, status: str):
        """更新状态栏"""
        if self.status_var:
            self.status_var.set(status)

    def _update_button_state(self, button: tk.Button, state: str):
        """更新按钮状态"""
        if button:
            button.config(state=state)

    def register_log_widget(self, tab_type: str, widget: tk.Text):
        """注册日志控件"""
        self.log_widgets[tab_type] = widget

    def register_status_var(self, status_var: tk.StringVar):
        """注册状态变量"""
        self.status_var = status_var

    def safe_log(self, message: str, tab_type: str = "general"):
        """线程安全的日志记录"""
        # 调试信息：检查日志控件是否已注册
        if tab_type not in self.log_widgets:
            logger.warning(f"日志控件未注册: {tab_type}, 已注册的控件: {list(self.log_widgets.keys())}")

        self.message_queue.put(("log", {"tab_type": tab_type, "message": message}))
        logger.info(f"[{tab_type}] {message}")

    def safe_status_update(self, status: str):
        """线程安全的状态更新"""
        self.message_queue.put(("status", {"status": status}))

    def safe_button_update(self, button: tk.Button, state: str):
        """线程安全的按钮状态更新"""
        self.message_queue.put(("button_state", {"button": button, "state": state}))

# ==================== 双数据库支持 ====================

# 添加父目录到路径以便导入双数据库模块
import sys
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入双数据库管理器
try:
    from database.dual_database_manager import get_dual_database_manager
    from ui.database_config_dialog import DatabaseConfigDialog
    DUAL_DATABASE_AVAILABLE = True
    logger.info("双数据库模块加载成功")
except ImportError as e:
    DUAL_DATABASE_AVAILABLE = False
    logger.warning(f"双数据库模块不可用，将使用单数据库模式: {e}")

# ==================== 配置管理器 ====================

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: str = "config.ini"):
        # 配置文件在当前目录中
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_file)
        self.config = configparser.ConfigParser()
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self._create_default_config()

    def _create_default_config(self):
        """创建默认配置"""
        self.config['Database'] = {
            'db_path': os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
        }

        self.config['Scripts'] = {
            'intelligent_processor': 'report 脚本 3.0.py',
            'modular_processor': 'report 模块化设计 7.0.py',
            'refund_script': 'Refund_process_修复版.py',
            'refund_script_optimized': 'scripts/refund_process_optimized.py',
            'data_import_script': '数据导入脚本.py',
            'data_import_script_optimized': '../scripts/data_import_optimized.py'
        }

        self.config['UI'] = {
            'window_width': '900',
            'window_height': '700',
            'theme': 'iphone_style'
        }

        self.config['Files'] = {
            'file_separator': '||',
            'temp_dir': 'temp_data',
            'backup_dir': 'backups'
        }

        self.config['Backup'] = {
            'auto_backup': 'true',
            'backup_before_import': 'true',
            'backup_before_refund': 'true',
            'max_backup_files': '10'
        }

        self.save_config()

    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)

    def get(self, section: str, key: str, fallback: str = ""):
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)

    def set(self, section: str, key: str, value: str):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value

    def get_db_path(self) -> str:
        """获取数据库路径"""
        return self.get('Database', 'db_path',
                       os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db"))

    def get_script_path(self, script_key: str) -> str:
        """获取脚本路径"""
        script_name = self.get('Scripts', script_key)
        if not script_name:
            raise MissingConfigError(script_key, 'Scripts')

        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), script_name)
        if not os.path.exists(script_path):
            raise ScriptNotFoundError(script_path, f"{script_key}脚本")

        return script_path

    def get_scripts_list(self) -> List[str]:
        """获取脚本列表"""
        return [
            self.get('Scripts', 'intelligent_processor')
        ]

# ==================== 文件管理器 ====================

class FileManager:
    """文件管理器"""

    def __init__(self, config: ConfigManager):
        self.config = config
        self.separator = config.get('Files', 'file_separator', '||')

    def join_file_paths(self, file_paths: List[str]) -> str:
        """连接文件路径"""
        return self.separator.join(file_paths)

    def split_file_paths(self, file_paths_str: str) -> List[str]:
        """分割文件路径"""
        if not file_paths_str:
            return []
        return [f.strip() for f in file_paths_str.split(self.separator) if f.strip()]

    def validate_files(self, file_paths: List[str]) -> tuple[bool, str]:
        """验证文件是否存在"""
        try:
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    raise FileNotFoundError(file_path, "验证")
            return True, ""
        except FileNotFoundError as e:
            return False, e.get_user_message()

    def get_temp_dir(self) -> str:
        """获取临时目录"""
        temp_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            self.config.get('Files', 'temp_dir', 'temp_data')
        )
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir

    def create_temp_file(self, suffix: str = '.xlsx') -> str:
        """创建临时文件"""
        temp_dir = self.get_temp_dir()
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return os.path.join(temp_dir, f"temp_{timestamp}{suffix}")

    def cleanup_temp_file(self, file_path: str):
        """清理临时文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {file_path}, 错误: {e}")

# ==================== 数据库备份管理器 ====================

class DatabaseBackupManager:
    """数据库备份管理器"""

    def __init__(self, config: ConfigManager, gui_updater: SafeGUIUpdater):
        self.config = config
        self.gui_updater = gui_updater
        self.db_path = config.get_db_path()
        self.backup_dir = self._get_backup_dir()
        self.auto_backup = config.get('Backup', 'auto_backup', 'true').lower() == 'true'
        self.max_backup_files = int(config.get('Backup', 'max_backup_files', '10'))

    def _get_backup_dir(self) -> str:
        """获取备份目录"""
        backup_dir = os.path.join(
            os.path.dirname(self.db_path),
            self.config.get('Files', 'backup_dir', 'backups')
        )
        os.makedirs(backup_dir, exist_ok=True)
        return backup_dir

    def backup_database(self, operation_name: str = "数据库操作") -> Optional[str]:
        """备份数据库

        Args:
            operation_name: 操作名称，用于日志记录

        Returns:
            str: 备份文件路径，失败时返回None
        """
        try:
            # 检查数据库文件是否存在
            if not os.path.exists(self.db_path):
                self.gui_updater.safe_log(f"数据库文件不存在，无法备份: {self.db_path}", "error")
                return None

            # 创建备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"sales_reports_backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, backup_filename)

            # 复制数据库文件
            shutil.copy2(self.db_path, backup_path)

            # 清理旧备份文件
            self._cleanup_old_backups()

            # 记录日志
            message = f"{operation_name}前已自动备份数据库: {backup_filename}"
            self.gui_updater.safe_log(message, "general")

            return backup_path

        except Exception as e:
            error_msg = f"备份数据库失败: {str(e)}"
            self.gui_updater.safe_log(error_msg, "error")
            return None

    def _cleanup_old_backups(self):
        """清理旧的备份文件，保留最新的几个"""
        try:
            # 获取所有备份文件
            backup_files = [
                f for f in os.listdir(self.backup_dir)
                if f.startswith("sales_reports_backup_") and f.endswith(".db")
            ]

            # 按时间排序，最新的在前面
            backup_files.sort(reverse=True)

            # 删除超出限制的备份文件
            if len(backup_files) > self.max_backup_files:
                files_to_delete = backup_files[self.max_backup_files:]
                for file_to_delete in files_to_delete:
                    file_path = os.path.join(self.backup_dir, file_to_delete)
                    os.remove(file_path)
                    self.gui_updater.safe_log(f"已删除旧备份文件: {file_to_delete}", "general")

        except Exception as e:
            self.gui_updater.safe_log(f"清理旧备份文件时出错: {str(e)}", "error")

    def get_backup_files(self) -> List[str]:
        """获取所有备份文件列表"""
        try:
            backup_files = [
                f for f in os.listdir(self.backup_dir)
                if f.startswith("sales_reports_backup_") and f.endswith(".db")
            ]
            # 按时间排序，最新的在前面
            backup_files.sort(reverse=True)
            return backup_files
        except Exception:
            return []

    def restore_backup(self, backup_filename: str) -> bool:
        """从备份恢复数据库

        Args:
            backup_filename: 备份文件名

        Returns:
            bool: 恢复是否成功
        """
        try:
            # 规范化路径，确保使用正确的路径分隔符
            backup_path = os.path.normpath(os.path.join(self.backup_dir, backup_filename))

            # 如果传入的是完整路径，直接使用
            if os.path.isabs(backup_filename):
                backup_path = os.path.normpath(backup_filename)

            if not os.path.exists(backup_path):
                raise FileNotFoundError(backup_path, "恢复")

            # 先备份当前数据库
            current_backup = None
            if os.path.exists(self.db_path):
                current_backup = self.backup_database("恢复操作")

            # 复制备份文件到数据库位置
            shutil.copy2(backup_path, self.db_path)

            message = f"数据库已从备份恢复: {backup_filename}"
            self.gui_updater.safe_log(message, "general")

            return True

        except FileNotFoundError as e:
            error_msg = f"恢复数据库失败: {e.get_user_message()}"
            self.gui_updater.safe_log(error_msg, "import")  # 使用import而不是error
            return False
        except Exception as e:
            error_msg = f"恢复数据库失败: {str(e)}"
            self.gui_updater.safe_log(error_msg, "import")  # 使用import而不是error
            return False

# ==================== 日志处理工具 ====================

class LogProcessor:
    """日志处理工具类"""

    @staticmethod
    def decode_unicode_message(message: str) -> str:
        """解码Unicode编码的中文消息

        Args:
            message: 包含Unicode编码的消息

        Returns:
            str: 解码后的中文消息
        """
        try:
            # 尝试解码Unicode转义序列
            decoded = message.encode().decode('unicode_escape')
            return decoded
        except:
            # 如果解码失败，返回原始消息
            return message

    @staticmethod
    def format_log_message(line: str, prefix: str = "") -> str:
        """格式化日志消息

        Args:
            line: 原始日志行
            prefix: 消息前缀

        Returns:
            str: 格式化后的消息，过滤掉不重要的信息
        """
        clean_line = line.strip()

        # 过滤掉不重要的调试信息，但保留重要的修改日志
        skip_patterns = [
            "🔍 调试",
            "插入记录统计",
            "前5条插入记录",
            "插入记录数:",
            "插入记录总金额:",
            "平均每条记录金额:",
            "🔍 Transaction同步插入:",
            "📝 执行标准插入:",
            "🔍 插入新记录:",
            "🔍 插入前标记数量:",
            "🔍 插入后标记数量:",
            "✅ 插入操作正常:",
            "🚨 插入操作影响了现有标记:",
            "执行导入命令:",
            "执行退款处理命令:",
            "执行双数据库",
            "📤 导入脚本:",
            "📤 退款脚本:",
            "📤 双数据库"
        ]

        # 🔧 修复：移除过于激进的过滤模式，保留重要的修改日志
        # 移除了 "Transaction ID:" 和 "Order ID:" 和 "Amount: RM" 过滤
        # 这些可能会过滤掉重要的修改日志

        # 检查是否包含需要跳过的模式
        for pattern in skip_patterns:
            if pattern in clean_line:
                return None  # 返回None表示跳过这条日志

        # 检查日志级别并添加相应的图标
        if "INFO" in clean_line:
            try:
                decoded_line = LogProcessor.decode_unicode_message(clean_line)
                # 提取INFO后的内容
                if " - INFO - " in decoded_line:
                    info_content = decoded_line.split(" - INFO - ", 1)[1]
                    return f"ℹ️ {prefix}{info_content}"
                else:
                    return f"ℹ️ {prefix}{decoded_line}"
            except:
                return f"ℹ️ {prefix}信息: {clean_line}"
        elif "ERROR" in clean_line or "CRITICAL" in clean_line:
            decoded_line = LogProcessor.decode_unicode_message(clean_line)
            return f"❌ {prefix}错误: {decoded_line}"
        elif "WARNING" in clean_line or "WARN" in clean_line:
            decoded_line = LogProcessor.decode_unicode_message(clean_line)
            return f"⚠️ {prefix}警告: {decoded_line}"
        else:
            # 其他信息默认为普通输出
            decoded_line = LogProcessor.decode_unicode_message(clean_line)
            return f"📋 {prefix}输出: {decoded_line}"

# ==================== 进程运行器 ====================

class ProcessRunner:
    """进程运行器"""

    def __init__(self, gui_updater: SafeGUIUpdater):
        self.gui_updater = gui_updater

    def run_process(self, cmd: List[str], tab_type: str,
                   success_callback: Optional[Callable] = None,
                   error_callback: Optional[Callable] = None) -> bool:
        """运行进程并实时更新GUI"""
        try:
            # 🔧 强化编码环境变量设置
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
            # 添加更多编码相关环境变量
            env['LANG'] = 'zh_CN.UTF-8'
            env['LC_ALL'] = 'zh_CN.UTF-8'

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace',  # 🔧 添加错误处理策略
                bufsize=1,
                env=env
            )

            # 创建线程读取输出
            def read_output(pipe, is_error=False):
                try:
                    for line in pipe:
                        # 🔧 强化编码处理
                        try:
                            # 确保line是正确的UTF-8字符串
                            if isinstance(line, bytes):
                                line = line.decode('utf-8', errors='replace')
                            line = line.strip()
                        except (UnicodeDecodeError, UnicodeEncodeError) as e:
                            # 如果编码失败，尝试其他编码
                            try:
                                if isinstance(line, bytes):
                                    line = line.decode('gbk', errors='replace')
                                else:
                                    line = str(line).encode('utf-8', errors='replace').decode('utf-8')
                                line = line.strip()
                            except:
                                line = f"[编码错误的输出行]"

                        if line:
                            if is_error:
                                self.gui_updater.safe_log(f"错误: {line}", tab_type)
                            else:
                                # 检查是否是进度信息
                                if "[" in line and "]" in line and "%" in line:
                                    self.gui_updater.safe_status_update(line)

                                # 使用LogProcessor过滤日志
                                formatted_message = LogProcessor.format_log_message(line)
                                if formatted_message is not None:  # 只记录非过滤的日志
                                    self.gui_updater.safe_log(formatted_message, tab_type)
                except Exception as e:
                    self.gui_updater.safe_log(f"读取输出时出错: {str(e)}", tab_type)

            # 启动读取线程
            stdout_thread = threading.Thread(target=read_output, args=(process.stdout,))
            stderr_thread = threading.Thread(target=read_output, args=(process.stderr, True))
            stdout_thread.daemon = True
            stderr_thread.daemon = True
            stdout_thread.start()
            stderr_thread.start()

            # 等待完成
            process.wait()
            stdout_thread.join()
            stderr_thread.join()

            # 处理结果
            if process.returncode == 0:
                if success_callback:
                    success_callback()
                return True
            else:
                if error_callback:
                    error_callback(process.returncode)
                return False

        except Exception as e:
            self.gui_updater.safe_log(f"运行进程时出错: {str(e)}", tab_type)
            if error_callback:
                error_callback(-1)
            return False

# ==================== 基础选项卡类 ====================

class BaseTab:
    """选项卡基类"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner):
        self.parent = parent
        self.gui_updater = gui_updater
        self.config = config
        self.file_manager = file_manager
        self.process_runner = process_runner
        self.tab_type = "general"

        # 创建主框架
        self.frame = ttk.Frame(parent, padding="10")
        self.frame.pack(fill=tk.BOTH, expand=True)

        # 创建UI
        self.create_ui()

        # 注册日志控件 - 确保在create_ui之后执行
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)

    def create_ui(self):
        """创建UI - 子类需要实现"""
        pass

    def log_message(self, message: str):
        """记录日志消息"""
        self.gui_updater.safe_log(message, self.tab_type)

    def create_log_area(self, title: str = "日志") -> tk.Text:
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(self.frame, text=title, padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 创建文本框和滚动条
        log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=log_text.yview)
        log_text.configure(yscrollcommand=scrollbar.set)

        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置只读
        log_text.config(state=tk.DISABLED)

        return log_text

    def _auto_restore_ui_state(self):
        """自动恢复UI状态"""
        try:
            # 重新启用可能被禁用的按钮
            button_attrs = [attr for attr in dir(self) if attr.endswith('_button')]
            for attr in button_attrs:
                button = getattr(self, attr, None)
                if button and hasattr(button, 'config'):
                    self.gui_updater.safe_button_update(button, 'normal')

            # 恢复状态栏
            self.gui_updater.safe_status_update("就绪")
        except Exception:
            pass  # 静默处理恢复失败

# ==================== 数据处理选项卡 ====================

class ProcessingTab(BaseTab):
    """数据处理选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner):
        # 先调用父类构造函数
        super().__init__(parent, gui_updater, config, file_manager, process_runner)

        # 然后设置正确的tab_type
        self.tab_type = "processing"

        # 重新注册日志控件以使用正确的tab_type
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)
            # 重新发送初始日志消息以使用正确的tab_type
            self.log_message("请使用浏览按钮选择文件")

    def create_ui(self):
        """创建数据处理UI"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.frame, text="文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)

        # 第一文件（SETTLEMENT文件）
        ttk.Label(file_frame, text="第一文件 (SETTLEMENT):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.file1_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file1_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file1).grid(row=0, column=2, padx=5, pady=5)

        # 第一文件Sheet名称
        ttk.Label(file_frame, text="第一文件Sheet名称:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sheet_name_var = tk.StringVar(value="TRANSACTION_LIST")
        ttk.Entry(file_frame, textvariable=self.sheet_name_var, width=50).grid(row=1, column=1, padx=5, pady=5)

        # 第二文件（CHINA文件）
        ttk.Label(file_frame, text="第二文件 (CHINA):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.file2_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file2_var, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file2).grid(row=2, column=2, padx=5, pady=5)

        # 处理脚本信息显示
        script_info_frame = ttk.Frame(file_frame)
        script_info_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(script_info_frame, text="处理脚本:",
                 font=('Arial', 8), foreground='#666666').pack(side=tk.LEFT)

        # 脚本选择下拉框
        script_frame = ttk.Frame(script_info_frame)
        script_frame.pack(side=tk.LEFT, padx=(5, 0))

        self.script_var = tk.StringVar(value="report 模块化设计 7.0.py")
        script_combo = ttk.Combobox(script_frame, textvariable=self.script_var,
                                   values=[
                                       "report 模块化设计 7.0.py",
                                       "report 脚本 3.0.py"
                                   ],
                                   state="readonly", width=35, font=('Arial', 8))
        script_combo.pack(side=tk.LEFT)

        # 脚本说明
        script_desc_frame = ttk.Frame(script_info_frame)
        script_desc_frame.pack(side=tk.LEFT, padx=(10, 0))

        self.script_desc_label = ttk.Label(script_desc_frame, text="模块化设计 + 智能检测 + 日期分组",
                                          font=('Arial', 7), foreground='#2E7D32')
        self.script_desc_label.pack(side=tk.LEFT)

        # 绑定脚本选择变化事件
        script_combo.bind('<<ComboboxSelected>>', self._on_script_changed)

        # 操作按钮区域
        button_frame = ttk.Frame(self.frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)

        # 处理按钮
        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.process_files)
        self.process_button.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=tk.LEFT, padx=5)

        # 创建日志区域
        self.log_text = self.create_log_area("处理日志")

        # 显示初始提示信息（注意：此时tab_type可能还是默认值）
        self.log_message("请使用浏览按钮选择文件")

    def _on_script_changed(self, event=None):
        """处理脚本选择变化"""
        selected_script = self.script_var.get()

        if "模块化设计 7.0" in selected_script:
            self.script_desc_label.config(text="模块化设计 + 智能检测 + 日期分组", foreground='#2E7D32')
        elif "脚本 3.0" in selected_script:
            self.script_desc_label.config(text="原始版本 + 智能检测", foreground='#1976D2')
        else:
            self.script_desc_label.config(text="", foreground='#666666')

    def browse_file1(self):
        """浏览选择第一文件"""
        filename = filedialog.askopenfilename(
            title="选择第一文件 (SETTLEMENT)",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file1_var.set(filename)
            basename = os.path.basename(filename)
            self.log_message(f"已选择第一文件: {basename}")

    def browse_file2(self):
        """浏览选择第二文件"""
        filename = filedialog.askopenfilename(
            title="选择第二文件 (CHINA)",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file2_var.set(filename)
            basename = os.path.basename(filename)
            self.log_message(f"已选择第二文件: {basename}")

    def clear_selection(self):
        """清空文件选择"""
        self.file1_var.set("")
        self.file2_var.set("")
        self.log_message("已清空文件选择")

    @class_exception_handler("文件处理", "processing")
    def process_files(self):
        """处理文件"""
        file1_path = self.file1_var.get().strip()
        file2_path = self.file2_var.get().strip()
        script = self.script_var.get()  # 始终使用智能脚本

        # 验证输入
        if not file1_path:
            raise FileValidationError("", "请选择第一文件 (SETTLEMENT)")
        if not file2_path:
            raise FileValidationError("", "请选择第二文件 (CHINA)")

        # 验证文件
        valid, error_msg = self.file_manager.validate_files([file1_path, file2_path])
        if not valid:
            return

        # 禁用按钮
        self.gui_updater.safe_button_update(self.process_button, tk.DISABLED)
        self.gui_updater.safe_status_update("处理中...")

        # 在新线程中处理
        thread = threading.Thread(target=self._run_processing, args=(file1_path, file2_path, script))
        thread.daemon = True
        thread.start()

    def _run_processing(self, file1_path: str, file2_path: str, script: str):
        """在新线程中运行处理"""
        try:
            self.log_message("开始处理文件...")

            # 构建命令
            try:
                # 根据用户选择的脚本动态选择
                selected_script = self.script_var.get()

                if "模块化设计" in selected_script:
                    script_key = 'modular_processor'
                    self.log_message(f"使用模块化设计脚本: {selected_script}")
                else:
                    script_key = 'intelligent_processor'
                    self.log_message(f"使用智能处理脚本: {selected_script}")

                script_path = self.config.get_script_path(script_key)
                self.log_message(f"脚本路径: {script_path}")

            except (ScriptNotFoundError, MissingConfigError) as e:
                self.log_message(f"脚本配置错误: {e.get_user_message()}")
                self._on_error(-1)
                return

            cmd = [sys.executable, script_path, "--file1", file1_path, "--file2", file2_path]

            # 添加sheet名称参数
            sheet_name = self.sheet_name_var.get().strip()
            if sheet_name:
                cmd.extend(["--sheet_name", sheet_name])
                self.log_message(f"使用指定的Sheet名称: {sheet_name}")

            # 运行进程
            self.process_runner.run_process(
                cmd,
                self.tab_type,
                success_callback=lambda: self._on_success(),
                error_callback=lambda code: self._on_error(code)
            )

        except Exception as e:
            self.log_message(f"处理文件时出错: {str(e)}")
            self._on_error(-1)

    def _on_success(self):
        """处理成功回调"""
        self.log_message("文件处理成功完成")
        self.gui_updater.safe_status_update("处理完成")
        self.gui_updater.safe_button_update(self.process_button, tk.NORMAL)

    def _on_error(self, return_code: int):
        """处理失败回调"""
        self.log_message(f"文件处理失败，返回码: {return_code}")
        self.gui_updater.safe_status_update("处理失败")
        self.gui_updater.safe_button_update(self.process_button, tk.NORMAL)

# ==================== 数据导入选项卡 ====================

class ImportTab(BaseTab):
    """数据导入选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner, backup_manager: DatabaseBackupManager):
        # 先调用父类构造函数
        super().__init__(parent, gui_updater, config, file_manager, process_runner)
        self.backup_manager = backup_manager

        # 然后设置正确的tab_type
        self.tab_type = "import"

        # 重新注册日志控件以使用正确的tab_type
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)
            # 重新发送初始日志消息以使用正确的tab_type
            self.log_message("请使用浏览按钮选择文件")

    def create_ui(self):
        """创建数据导入UI"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.frame, text="导入文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)

        ttk.Label(file_frame, text="导入文件(可多选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.import_files_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.import_files_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_files).grid(row=0, column=2, padx=5, pady=5)

        # 已选择文件列表框
        ttk.Label(file_frame, text="已选择的文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.selected_files_text = tk.Text(file_frame, wrap=tk.WORD, width=50, height=5)
        self.selected_files_text.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.selected_files_text.yview)
        scrollbar.grid(row=1, column=2, sticky=tk.N+tk.S)
        self.selected_files_text.configure(yscrollcommand=scrollbar.set)
        self.selected_files_text.config(state=tk.DISABLED)

        # 平台类型选择
        platform_frame = ttk.LabelFrame(self.frame, text="平台类型", padding="10")
        platform_frame.pack(fill=tk.X, pady=10)

        self.platform_var = tk.StringVar(value="IOT")
        ttk.Radiobutton(platform_frame, text="IOT", value="IOT", variable=self.platform_var).grid(row=0, column=0, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(platform_frame, text="ZERO", value="ZERO", variable=self.platform_var).grid(row=0, column=1, padx=20, pady=5, sticky=tk.W)

        # 订单类型选择
        order_type_frame = ttk.LabelFrame(self.frame, text="订单类型", padding="10")
        order_type_frame.pack(fill=tk.X, pady=10)

        self.order_type_var = tk.StringVar(value="all")
        ttk.Radiobutton(order_type_frame, text="所有订单", value="all", variable=self.order_type_var).grid(row=0, column=0, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(order_type_frame, text="仅API订单", value="api", variable=self.order_type_var).grid(row=0, column=1, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(order_type_frame, text="仅普通订单", value="normal", variable=self.order_type_var).grid(row=0, column=2, padx=20, pady=5, sticky=tk.W)

        # 数据库选择（简化版）
        if DUAL_DATABASE_AVAILABLE:
            db_frame = ttk.LabelFrame(self.frame, text="📊 数据库选择", padding="10")
            db_frame.pack(fill=tk.X, pady=10)

            # 简化的数据库选择
            ttk.Label(db_frame, text="导入目标:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

            self.db_target_var = tk.StringVar(value="仅SQLite")
            self.db_target_combo = ttk.Combobox(db_frame, textvariable=self.db_target_var,
                                               values=["仅SQLite", "仅PostgreSQL", "同时导入两个数据库"],
                                               state="readonly", width=20)
            self.db_target_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
            self.db_target_combo.bind('<<ComboboxSelected>>', self.on_database_selection_changed)

            # 状态显示（简化）
            self.db_status_label = ttk.Label(db_frame, text="✅ SQLite数据库（本地文件）",
                                           font=("Arial", 8), foreground="#2E7D32")
            self.db_status_label.grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

            # 初始化数据库状态
            self.refresh_database_options()

        # 操作按钮区域
        button_frame = ttk.Frame(self.frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)

        # 导入按钮
        self.import_button = ttk.Button(button_frame, text="开始导入", command=self.import_data)
        self.import_button.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=tk.LEFT, padx=5)

        # 创建日志区域
        self.log_text = self.create_log_area("导入日志")

        # 显示初始提示信息
        self.log_message("请使用浏览按钮选择文件")

    def browse_files(self):
        """浏览选择导入文件"""
        filenames = filedialog.askopenfilenames(
            title="选择导入文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filenames:
            # 获取当前已选择的文件
            current_files = self.file_manager.split_file_paths(self.import_files_var.get())

            # 合并文件
            all_files = current_files + list(filenames)

            # 更新变量
            self.import_files_var.set(self.file_manager.join_file_paths(all_files))

            # 更新显示
            self.update_selected_files_display(all_files)

            # 记录日志
            basenames = [os.path.basename(f) for f in filenames]
            self.log_message(f"已选择{len(filenames)}个导入文件: {', '.join(basenames)}")

            # 自动识别平台类型
            if filenames:
                first_file = filenames[0]
                if "IOT" in first_file.upper():
                    self.platform_var.set("IOT")
                    self.log_message("自动识别为IOT平台")
                elif "ZERO" in first_file.upper():
                    self.platform_var.set("ZERO")
                    self.log_message("自动识别为ZERO平台")

    def clear_selection(self):
        """清空文件选择"""
        self.import_files_var.set("")
        self.update_selected_files_display([])
        self.log_message("已清空文件选择")

    def update_selected_files_display(self, file_paths: List[str]):
        """更新已选择文件的显示"""
        self.selected_files_text.config(state=tk.NORMAL)
        self.selected_files_text.delete(1.0, tk.END)
        for file_path in file_paths:
            self.selected_files_text.insert(tk.END, f"{os.path.basename(file_path)}\n")
        self.selected_files_text.config(state=tk.DISABLED)

    def refresh_database_options(self):
        """刷新数据库选项（简化版）"""
        if not DUAL_DATABASE_AVAILABLE:
            return

        try:
            from database.dual_database_manager import get_dual_database_manager
            db_manager = get_dual_database_manager()

            # 更新可用数据库列表
            available_dbs = db_manager.get_available_databases()
            if available_dbs:
                self.log_message(f"可用数据库: {', '.join(available_dbs)}")

                # 更新状态显示
                if "SQLite" in available_dbs and "PostgreSQL" in available_dbs:
                    self.db_status_label.configure(text="✅ SQLite + PostgreSQL 可用", foreground="#2E7D32")
                elif "SQLite" in available_dbs:
                    self.db_status_label.configure(text="✅ SQLite 可用", foreground="#2E7D32")
                elif "PostgreSQL" in available_dbs:
                    self.db_status_label.configure(text="✅ PostgreSQL 可用", foreground="#2E7D32")
            else:
                self.db_status_label.configure(text="❌ 无可用数据库", foreground="red")
                self.log_message("警告: 没有可用的数据库连接")

        except Exception as e:
            self.db_status_label.configure(text="❌ 检查失败", foreground="red")
            self.log_message(f"检查数据库状态失败: {str(e)}")



    def on_database_selection_changed(self, event=None):
        """数据库选择改变时的处理（简化版）"""
        if not DUAL_DATABASE_AVAILABLE:
            return

        selection = self.db_target_var.get()

        if selection == "仅SQLite":
            self.db_status_label.configure(text="✅ SQLite数据库（本地文件）", foreground="#2E7D32")
        elif selection == "仅PostgreSQL":
            self.db_status_label.configure(text="✅ PostgreSQL数据库（企业级）", foreground="#2E7D32")
        elif selection == "同时导入两个数据库":
            self.db_status_label.configure(text="✅ SQLite + PostgreSQL（双重保障）", foreground="#2E7D32")

        self.log_message(f"数据库导入目标已更改为: {selection}")





    def get_target_databases(self):
        """获取目标数据库列表"""
        if not DUAL_DATABASE_AVAILABLE:
            return None

        target = self.db_target_var.get()

        if target == "仅SQLite":
            return ["SQLite"]
        elif target == "仅PostgreSQL":
            return ["PostgreSQL"]
        elif target == "同时导入两个数据库":
            try:
                from database.dual_database_manager import get_dual_database_manager
                db_manager = get_dual_database_manager()
                return db_manager.get_available_databases()
            except:
                return None
        elif target == "用户自定义选择":
            # 弹出对话框让用户选择
            return self._show_database_selection_dialog()
        else:
            return None

    def _show_database_selection_dialog(self):
        """显示数据库选择对话框"""
        try:
            from database.dual_database_manager import get_dual_database_manager
            db_manager = get_dual_database_manager()
            available_dbs = db_manager.get_available_databases()

            if not available_dbs:
                messagebox.showwarning("警告", "没有可用的数据库")
                return None

            # 创建选择对话框
            dialog = tk.Toplevel(self.frame)
            dialog.title("选择目标数据库")
            dialog.geometry("300x200")
            dialog.transient(self.frame)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            ttk.Label(dialog, text="请选择要导入的数据库:").pack(pady=10)

            selected_dbs = []
            vars_dict = {}

            for db in available_dbs:
                var = tk.BooleanVar(value=True)
                vars_dict[db] = var
                ttk.Checkbutton(dialog, text=db, variable=var).pack(pady=5)

            def on_ok():
                nonlocal selected_dbs
                selected_dbs = [db for db, var in vars_dict.items() if var.get()]
                dialog.destroy()

            def on_cancel():
                nonlocal selected_dbs
                selected_dbs = None
                dialog.destroy()

            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.LEFT, padx=5)

            dialog.wait_window()
            return selected_dbs

        except Exception as e:
            messagebox.showerror("错误", f"显示数据库选择对话框失败: {str(e)}")
            return None

    @class_exception_handler("数据导入", "import")
    def import_data(self):
        """导入数据"""
        files_str = self.import_files_var.get()
        if not files_str:
            raise FileValidationError("", "请选择至少一个导入文件")

        file_paths = self.file_manager.split_file_paths(files_str)
        if not file_paths:
            raise FileValidationError("", "请选择至少一个导入文件")

        # 验证文件
        valid, error_msg = self.file_manager.validate_files(file_paths)
        if not valid:
            return

        platform_type = self.platform_var.get()
        order_type = self.order_type_var.get()

        # 获取目标数据库
        target_databases = None
        if DUAL_DATABASE_AVAILABLE:
            target_databases = self.get_target_databases()
            if target_databases is None:
                self.log_message("未选择目标数据库，使用传统导入方式")
            elif not target_databases:
                messagebox.showwarning("警告", "没有选择任何数据库")
                return
            else:
                self.log_message(f"选择的目标数据库: {', '.join(target_databases)}")

        # 禁用按钮
        self.gui_updater.safe_button_update(self.import_button, tk.DISABLED)
        self.gui_updater.safe_status_update("导入中...")

        # 在新线程中导入
        thread = threading.Thread(target=self._run_import, args=(file_paths, platform_type, order_type, target_databases))
        thread.daemon = True
        thread.start()

    def _run_import(self, file_paths: List[str], platform_type: str, order_type: str, target_databases: List[str] = None):
        """在新线程中运行导入"""
        backup_files = {}
        backup_file = None  # 为传统备份保留这个变量
        try:
            self.log_message(f"开始导入数据，平台类型: {platform_type}, 订单类型: {order_type}")

            if target_databases:
                self.log_message(f"目标数据库: {', '.join(target_databases)}")

            # 检查是否需要自动备份
            if self.config.get('Backup', 'backup_before_import', 'true').lower() == 'true':
                self.log_message("正在备份数据库...")

                if target_databases and DUAL_DATABASE_AVAILABLE:
                    # 备份所有目标数据库
                    try:
                        from database.dual_database_manager import get_dual_database_manager
                        db_manager = get_dual_database_manager()
                        backup_files = db_manager.backup_databases(target_databases)
                        for db_type, backup_path in backup_files.items():
                            self.log_message(f"{db_type}数据库备份成功: {os.path.basename(backup_path)}")
                    except Exception as e:
                        self.log_message(f"双数据库备份失败: {str(e)}，使用传统备份")
                        backup_file = self.backup_manager.backup_database("数据导入")
                        if backup_file:
                            backup_files['SQLite'] = backup_file
                            self.log_message(f"数据库备份成功: {os.path.basename(backup_file)}")
                else:
                    # 传统备份
                    backup_file = self.backup_manager.backup_database("数据导入")
                    if backup_file:
                        backup_files['SQLite'] = backup_file
                        self.log_message(f"数据库备份成功: {os.path.basename(backup_file)}")
                    else:
                        self.log_message("警告: 数据库备份失败，但将继续导入")

            all_success = True
            for file_path in file_paths:
                try:
                    self.log_message(f"处理文件: {os.path.basename(file_path)}")

                    # 根据是否有目标数据库选择导入方式
                    if target_databases and DUAL_DATABASE_AVAILABLE:
                        # 如果只选择了SQLite，使用传统导入方式（更稳定）
                        if target_databases == ["SQLite"]:
                            success = self._import_file_to_database(file_path, platform_type, order_type)
                        else:
                            # 多数据库或包含PostgreSQL时使用双数据库导入
                            success = self._import_file_to_dual_database(file_path, platform_type, order_type, target_databases)
                    else:
                        success = self._import_file_to_database(file_path, platform_type, order_type)

                    if success:
                        self.log_message(f"文件 {os.path.basename(file_path)} 导入成功")
                    else:
                        self.log_message(f"文件 {os.path.basename(file_path)} 导入失败")
                        all_success = False

                except Exception as e:
                    self.log_message(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                    all_success = False

            # 处理结果
            if all_success:
                self.log_message("所有文件导入成功")
                self.gui_updater.safe_status_update("导入完成")
            else:
                self.log_message("部分或全部文件导入失败")
                self.gui_updater.safe_status_update("导入部分失败")

                # 如果导入失败且有备份，询问是否恢复
                # 检查backup_files字典中是否有SQLite备份
                sqlite_backup = backup_files.get('SQLite')
                if sqlite_backup and os.path.exists(sqlite_backup):
                    self.log_message("检测到导入失败，可以恢复到备份状态")
                    # 注意：在线程中不能直接显示messagebox，需要通过主线程处理
                    self.gui_updater.root.after(0, lambda: self._ask_restore_backup(sqlite_backup))
                elif backup_file and os.path.exists(backup_file):
                    # 兼容旧的backup_file变量
                    self.log_message("检测到导入失败，可以恢复到备份状态")
                    self.gui_updater.root.after(0, lambda: self._ask_restore_backup(backup_file))

        except Exception as e:
            self.log_message(f"导入数据时出错: {str(e)}")
            self.gui_updater.safe_status_update("导入出错")

            # 如果导入出错且有备份，询问是否恢复
            # 检查backup_files字典中是否有SQLite备份
            sqlite_backup = backup_files.get('SQLite')
            if sqlite_backup and os.path.exists(sqlite_backup):
                self.log_message("检测到导入出错，可以恢复到备份状态")
                self.gui_updater.root.after(0, lambda: self._ask_restore_backup(sqlite_backup))
            elif backup_file and os.path.exists(backup_file):
                # 兼容旧的backup_file变量
                self.log_message("检测到导入出错，可以恢复到备份状态")
                self.gui_updater.root.after(0, lambda: self._ask_restore_backup(backup_file))

        finally:
            self.gui_updater.safe_button_update(self.import_button, tk.NORMAL)

    def _import_file_to_dual_database(self, file_path: str, platform_type: str, order_type: str, target_databases: List[str]) -> bool:
        """将文件导入到双数据库系统"""
        try:
            # 使用双数据库导入脚本
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "scripts", "dual_database_import.py")

            if not os.path.exists(script_path):
                self.log_message("双数据库导入脚本不存在，使用传统导入方式")
                return self._import_file_to_database(file_path, platform_type, order_type)

            # 构建命令行参数
            cmd = [
                sys.executable,
                script_path,
                '--file', file_path,
                '--platform', platform_type,
                '--databases'] + target_databases

            if order_type != 'all':
                cmd.extend(['--order-type', order_type])

            self.log_message(f"执行双数据库导入命令: {' '.join(cmd)}")

            # 🔧 设置编码环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

            # 执行导入脚本
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                env=env,
                timeout=600  # 10分钟超时
            )

            # 处理输出
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        self.log_message(f"📤 双数据库导入: {line}")

            if result.stderr:
                for line in result.stderr.strip().split('\n'):
                    if line.strip():
                        self.log_message(f"⚠️ 双数据库导入警告: {line}")

            # 检查返回码
            if result.returncode == 0:
                self.log_message(f"双数据库导入成功，返回码: {result.returncode}")
                return True
            else:
                self.log_message(f"双数据库导入失败，返回码: {result.returncode}")
                return False

        except subprocess.TimeoutExpired:
            self.log_message("双数据库导入超时（超过10分钟）")
            return False
        except Exception as e:
            self.log_message(f"执行双数据库导入时出错: {str(e)}")
            return False

    def _import_file_to_database(self, file_path: str, platform_type: str, order_type: str) -> bool:
        """将文件导入到数据库

        Args:
            file_path: 要导入的文件路径
            platform_type: 平台类型 (IOT/ZERO)
            order_type: 订单类型 (Normal/Refund)

        Returns:
            bool: 导入是否成功
        """
        try:
            # 获取优化版数据导入脚本路径
            try:
                script_path = self.config.get_script_path('data_import_script_optimized')
            except (ScriptNotFoundError, MissingConfigError) as e:
                self.log_message(f"数据导入脚本配置错误: {e.get_user_message()}")
                return False

            # 获取数据库路径
            db_path = self.config.get_db_path()

            # 构建命令行参数（匹配数据导入脚本的参数格式）
            cmd = [
                sys.executable,  # Python解释器
                script_path,     # 导入脚本
                '--file', file_path,        # 要导入的文件
                '--db_path', db_path,       # 数据库路径
                '--platform', platform_type # 平台类型
            ]

            self.log_message(f"执行导入命令: {' '.join(cmd)}")

            # 🔧 设置编码环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

            # 执行导入脚本
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                env=env,
                timeout=300  # 5分钟超时
            )

            # 处理输出
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        self.log_message(f"📤 导入脚本: {line}")

            if result.stderr:
                for line in result.stderr.strip().split('\n'):
                    if line.strip():
                        # 使用LogProcessor智能处理日志
                        formatted_message = LogProcessor.format_log_message(line, "导入 - ")
                        self.log_message(formatted_message)

            # 检查返回码
            if result.returncode == 0:
                self.log_message(f"数据导入成功，返回码: {result.returncode}")
                return True
            else:
                self.log_message(f"数据导入失败，返回码: {result.returncode}")
                return False

        except subprocess.TimeoutExpired:
            self.log_message("数据导入超时（超过5分钟）")
            return False
        except Exception as e:
            self.log_message(f"执行数据导入时出错: {str(e)}")
            return False

    def _ask_restore_backup(self, backup_file: str):
        """询问是否恢复备份（在主线程中调用）"""
        try:
            if messagebox.askyesno("恢复备份",
                                 f"导入过程中出现问题，是否恢复到备份状态？\n\n备份文件: {os.path.basename(backup_file)}"):
                if self.backup_manager.restore_backup(os.path.basename(backup_file)):
                    self.log_message("数据库已恢复到备份状态")
                    messagebox.showinfo("恢复成功", "数据库已恢复到备份状态")
                else:
                    self.log_message("数据库恢复失败")
                    messagebox.showerror("恢复失败", "数据库恢复失败，请查看日志了解详情")
        except Exception as e:
            self.log_message(f"恢复备份时出错: {str(e)}")

# ==================== 退款处理选项卡 ====================

class RefundTab(BaseTab):
    """退款处理选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner, backup_manager: DatabaseBackupManager):
        # 先调用父类构造函数
        super().__init__(parent, gui_updater, config, file_manager, process_runner)
        self.backup_manager = backup_manager

        # 然后设置正确的tab_type
        self.tab_type = "refund"

        # 重新注册日志控件以使用正确的tab_type
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)
            # 重新发送初始日志消息以使用正确的tab_type
            self.log_message("请选择包含REFUND_LIST工作表的Excel文件进行退款处理")

    def create_ui(self):
        """创建退款处理UI"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.frame, text="退款文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)

        ttk.Label(file_frame, text="退款文件(可多选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.refund_files_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.refund_files_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_files).grid(row=0, column=2, padx=5, pady=5)

        # 已选择文件列表框
        ttk.Label(file_frame, text="已选择的文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.selected_files_text = tk.Text(file_frame, wrap=tk.WORD, width=50, height=5)
        self.selected_files_text.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.selected_files_text.yview)
        scrollbar.grid(row=1, column=2, sticky=tk.N+tk.S)
        self.selected_files_text.configure(yscrollcommand=scrollbar.set)
        self.selected_files_text.config(state=tk.DISABLED)

        # 操作按钮区域
        button_frame = ttk.Frame(self.frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)

        # 处理按钮
        self.refund_button = ttk.Button(button_frame, text="开始处理退款", command=self.process_refunds)
        self.refund_button.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=tk.LEFT, padx=5)

        # 数据库选择（如果支持双数据库）
        if DUAL_DATABASE_AVAILABLE:
            db_frame = ttk.LabelFrame(self.frame, text="📊 数据库退款选择", padding="15")
            db_frame.pack(fill=tk.X, pady=10)

            # 说明文字
            info_label = ttk.Label(db_frame, text="选择退款处理的目标数据库。您可以选择单独处理或同时处理多个数据库。",
                                 font=("Arial", 8), foreground="#666666")
            info_label.grid(row=0, column=0, columnspan=4, sticky=tk.W, pady=(0, 10))

            # 退款选项
            ttk.Label(db_frame, text="退款目标:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W, padx=(0, 10))

            self.refund_db_target_var = tk.StringVar(value="仅SQLite")
            self.refund_db_target_combo = ttk.Combobox(db_frame, textvariable=self.refund_db_target_var,
                                                      values=["仅SQLite", "仅PostgreSQL", "同时处理两个数据库", "用户自定义选择"],
                                                      state="readonly", width=25)
            self.refund_db_target_combo.grid(row=1, column=1, sticky=tk.W, padx=(0, 10))
            self.refund_db_target_combo.bind('<<ComboboxSelected>>', self.on_refund_database_selection_changed)

            # 刷新可用数据库按钮
            refresh_refund_db_btn = ttk.Button(db_frame, text="🔄 刷新状态", command=self.refresh_refund_database_options)
            refresh_refund_db_btn.grid(row=1, column=2, padx=(10, 0))

            # 测试连接按钮
            test_refund_db_btn = ttk.Button(db_frame, text="🔗 测试连接", command=self.test_refund_database_connections)
            test_refund_db_btn.grid(row=1, column=3, padx=(10, 0))

            # 状态显示
            self.refund_db_status_label = ttk.Label(db_frame, text="正在检查数据库状态...", foreground="orange")
            self.refund_db_status_label.grid(row=2, column=0, columnspan=4, sticky=tk.W, pady=(10, 0))

            # 详细说明
            self.refund_db_detail_label = ttk.Label(db_frame, text="✅ 退款将仅在SQLite数据库中处理（本地文件数据库）",
                                                   font=("Arial", 8), foreground="#2E7D32")
            self.refund_db_detail_label.grid(row=3, column=0, columnspan=4, sticky=tk.W, pady=(5, 0))

            # 初始化数据库状态
            self.refresh_refund_database_options()

        # 创建日志区域
        self.log_text = self.create_log_area("退款处理日志")

        # 显示初始提示信息
        self.log_message("请选择包含REFUND_LIST工作表的Excel文件进行退款处理")

    def browse_files(self):
        """浏览选择退款文件"""
        filenames = filedialog.askopenfilenames(
            title="选择退款文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filenames:
            # 获取当前已选择的文件
            current_files = self.file_manager.split_file_paths(self.refund_files_var.get())

            # 合并文件
            all_files = current_files + list(filenames)

            # 更新变量
            self.refund_files_var.set(self.file_manager.join_file_paths(all_files))

            # 更新显示
            self.update_selected_files_display(all_files)

            # 记录日志
            basenames = [os.path.basename(f) for f in filenames]
            self.log_message(f"已选择{len(filenames)}个退款文件: {', '.join(basenames)}")

    def clear_selection(self):
        """清空文件选择"""
        self.refund_files_var.set("")
        self.update_selected_files_display([])
        self.log_message("已清空文件选择")

    def update_selected_files_display(self, file_paths: List[str]):
        """更新已选择文件的显示"""
        self.selected_files_text.config(state=tk.NORMAL)
        self.selected_files_text.delete(1.0, tk.END)
        for file_path in file_paths:
            self.selected_files_text.insert(tk.END, f"{os.path.basename(file_path)}\n")
        self.selected_files_text.config(state=tk.DISABLED)

    def refresh_refund_database_options(self):
        """刷新退款数据库选项"""
        if not DUAL_DATABASE_AVAILABLE:
            return

        try:
            from database.dual_database_manager import get_dual_database_manager
            db_manager = get_dual_database_manager()
            available_dbs = db_manager.get_available_databases()

            if available_dbs:
                status_text = f"可用数据库: {', '.join(available_dbs)}"
                self.refund_db_status_label.configure(text=status_text, foreground="green")
            else:
                self.refund_db_status_label.configure(text="❌ 没有可用的数据库", foreground="red")
                self.log_message("警告: 没有可用的数据库连接")
        except Exception as e:
            self.refund_db_status_label.configure(text=f"❌ 检查数据库失败: {str(e)}", foreground="red")
            self.log_message(f"检查数据库状态失败: {str(e)}")

    def on_refund_database_selection_changed(self, event=None):
        """退款数据库选择改变时的处理"""
        if not DUAL_DATABASE_AVAILABLE:
            return

        selection = self.refund_db_target_var.get()

        if selection == "仅SQLite":
            self.refund_db_detail_label.configure(text="✅ 退款将仅在SQLite数据库中处理（本地文件数据库）")
        elif selection == "仅PostgreSQL":
            self.refund_db_detail_label.configure(text="✅ 退款将仅在PostgreSQL数据库中处理（企业级数据库）")
        elif selection == "同时处理两个数据库":
            self.refund_db_detail_label.configure(text="✅ 退款将同时在SQLite和PostgreSQL数据库中处理（双重保障）")
        elif selection == "用户自定义选择":
            self.refund_db_detail_label.configure(text="✅ 退款时将弹出对话框让您选择具体的数据库")

        self.log_message(f"退款数据库目标已更改为: {selection}")

    def test_refund_database_connections(self):
        """测试退款数据库连接"""
        if not DUAL_DATABASE_AVAILABLE:
            return

        try:
            from database.dual_database_manager import get_dual_database_manager
            db_manager = get_dual_database_manager()

            self.log_message("开始测试退款数据库连接...")

            # 测试SQLite
            if db_manager.sqlite_config['enabled']:
                if db_manager.test_sqlite_connection():
                    self.log_message("✅ SQLite连接测试成功")
                else:
                    self.log_message("❌ SQLite连接测试失败")
            else:
                self.log_message("⚪ SQLite未启用")

            # 测试PostgreSQL
            if db_manager.postgresql_config['enabled']:
                if db_manager.test_postgresql_connection():
                    self.log_message("✅ PostgreSQL连接测试成功")
                else:
                    self.log_message("❌ PostgreSQL连接测试失败")
            else:
                self.log_message("⚪ PostgreSQL未启用")

            # 刷新状态
            self.refresh_refund_database_options()

        except Exception as e:
            self.log_message(f"测试连接失败: {str(e)}")

    def get_refund_target_databases(self):
        """获取退款目标数据库列表"""
        if not DUAL_DATABASE_AVAILABLE:
            return None

        target = self.refund_db_target_var.get()

        if target == "仅SQLite":
            return ["SQLite"]
        elif target == "仅PostgreSQL":
            return ["PostgreSQL"]
        elif target == "同时处理两个数据库":
            try:
                from database.dual_database_manager import get_dual_database_manager
                db_manager = get_dual_database_manager()
                return db_manager.get_available_databases()
            except:
                return None
        elif target == "用户自定义选择":
            # 弹出对话框让用户选择
            return self._show_refund_database_selection_dialog()
        else:
            return None

    def _show_refund_database_selection_dialog(self):
        """显示退款数据库选择对话框"""
        try:
            from database.dual_database_manager import get_dual_database_manager
            db_manager = get_dual_database_manager()
            available_dbs = db_manager.get_available_databases()

            if not available_dbs:
                messagebox.showwarning("警告", "没有可用的数据库")
                return None

            # 创建选择对话框
            dialog = tk.Toplevel(self.frame)
            dialog.title("选择退款数据库")
            dialog.geometry("400x300")
            dialog.transient(self.frame.winfo_toplevel())
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            selected_dbs = []

            # 标题
            title_label = ttk.Label(dialog, text="选择要进行退款处理的数据库", font=("Arial", 12, "bold"))
            title_label.pack(pady=10)

            # 数据库选择框架
            db_frame = ttk.Frame(dialog)
            db_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # 数据库选择变量
            db_vars = {}
            for db_type in available_dbs:
                var = tk.BooleanVar(value=True)  # 默认选中所有可用数据库
                db_vars[db_type] = var
                ttk.Checkbutton(db_frame, text=f"{db_type} 数据库", variable=var).pack(anchor=tk.W, pady=5)

            # 按钮框架
            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill=tk.X, padx=20, pady=10)

            def on_ok():
                nonlocal selected_dbs
                selected_dbs = [db_type for db_type, var in db_vars.items() if var.get()]
                dialog.destroy()

            def on_cancel():
                nonlocal selected_dbs
                selected_dbs = None
                dialog.destroy()

            ttk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.RIGHT, padx=5)

            dialog.wait_window()
            return selected_dbs

        except Exception as e:
            messagebox.showerror("错误", f"显示数据库选择对话框失败: {str(e)}")
            return None

    @class_exception_handler("退款处理", "refund")
    def process_refunds(self):
        """处理退款文件"""
        files_str = self.refund_files_var.get()
        if not files_str:
            raise FileValidationError("", "请选择至少一个退款文件")

        file_paths = self.file_manager.split_file_paths(files_str)
        if not file_paths:
            raise FileValidationError("", "请选择至少一个退款文件")

        # 验证文件
        valid, error_msg = self.file_manager.validate_files(file_paths)
        if not valid:
            return

        # 禁用按钮
        self.gui_updater.safe_button_update(self.refund_button, tk.DISABLED)
        self.gui_updater.safe_status_update("退款处理中...")

        # 获取目标数据库
        target_databases = None
        if DUAL_DATABASE_AVAILABLE:
            target_databases = self.get_refund_target_databases()
            if target_databases is None:
                self.log_message("未选择目标数据库，使用传统退款方式")
            elif not target_databases:
                messagebox.showwarning("警告", "没有选择任何数据库")
                self.gui_updater.safe_button_update(self.refund_button, tk.NORMAL)
                return

        # 在新线程中处理
        thread = threading.Thread(target=self._run_refund_processing, args=(file_paths, target_databases))
        thread.daemon = True
        thread.start()

    def _run_refund_processing(self, file_paths: List[str], target_databases: Optional[List[str]] = None):
        """在新线程中运行退款处理"""
        backup_files = {}
        try:
            self.log_message("开始处理退款文件...")

            # 检查是否需要自动备份
            if self.config.get('Backup', 'backup_before_refund', 'true').lower() == 'true':
                self.log_message("正在备份数据库...")

                if target_databases and DUAL_DATABASE_AVAILABLE:
                    # 备份所有目标数据库
                    try:
                        from database.dual_database_manager import get_dual_database_manager
                        db_manager = get_dual_database_manager()
                        backup_files = db_manager.backup_databases(target_databases)
                        for db_type, backup_path in backup_files.items():
                            self.log_message(f"{db_type}数据库备份成功: {os.path.basename(backup_path)}")
                    except Exception as e:
                        self.log_message(f"双数据库备份失败: {str(e)}，使用传统备份")
                        backup_file = self.backup_manager.backup_database("退款处理")
                        if backup_file:
                            backup_files['SQLite'] = backup_file
                            self.log_message(f"数据库备份成功: {os.path.basename(backup_file)}")
                else:
                    # 传统备份
                    backup_file = self.backup_manager.backup_database("退款处理")
                    if backup_file:
                        backup_files['SQLite'] = backup_file
                        self.log_message(f"数据库备份成功: {os.path.basename(backup_file)}")
                    else:
                        self.log_message("警告: 数据库备份失败，但将继续处理")

            all_success = True
            for file_path in file_paths:
                try:
                    self.log_message(f"处理文件: {os.path.basename(file_path)}")

                    # 检测平台类型
                    platform_type = self._detect_platform_type(file_path)
                    self.log_message(f"检测到平台类型: {platform_type}")

                    # 根据是否有目标数据库选择退款方式
                    if target_databases and DUAL_DATABASE_AVAILABLE:
                        # 如果只选择了SQLite，使用传统退款方式（更稳定）
                        if target_databases == ["SQLite"]:
                            success = self._process_refund_file(file_path, platform_type)
                        else:
                            # 多数据库或包含PostgreSQL时使用双数据库退款
                            success = self._process_refund_file_to_dual_database(file_path, platform_type, target_databases)
                    else:
                        success = self._process_refund_file(file_path, platform_type)

                    if success:
                        self.log_message(f"文件 {os.path.basename(file_path)} 处理成功")
                    else:
                        self.log_message(f"文件 {os.path.basename(file_path)} 处理失败")
                        all_success = False

                except Exception as e:
                    self.log_message(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                    all_success = False

            if all_success:
                self.log_message("退款处理完成")
                self.gui_updater.safe_status_update("退款处理完成")
            else:
                self.log_message("退款处理完成，但部分文件处理失败")
                self.gui_updater.safe_status_update("退款处理部分失败")

        except Exception as e:
            self.log_message(f"退款处理过程中出错: {str(e)}")
            self.gui_updater.safe_status_update("退款处理出错")

            # 如果处理失败且有备份，询问是否恢复
            if backup_files and messagebox.askyesno("恢复备份", "退款处理失败，是否恢复备份？"):
                for db_type, backup_file in backup_files.items():
                    self.gui_updater.root.after(0, lambda bf=backup_file: self._ask_restore_backup(bf))

        finally:
            self.gui_updater.safe_button_update(self.refund_button, tk.NORMAL)

    def _process_refund_file_to_dual_database(self, file_path: str, platform_type: str, target_databases: List[str]) -> bool:
        """将文件退款处理到双数据库系统"""
        try:
            # 使用双数据库退款脚本
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "scripts", "dual_database_refund.py")

            if not os.path.exists(script_path):
                self.log_message("双数据库退款脚本不存在，使用传统退款方式")
                return self._process_refund_file(file_path, platform_type)

            # 构建命令行参数
            cmd = [
                sys.executable,
                script_path,
                '--file', file_path,
                '--platform', platform_type,
                '--databases', ','.join(target_databases)
            ]

            self.log_message(f"执行双数据库退款命令: {' '.join(cmd)}")

            # 🔧 设置编码环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

            # 执行双数据库退款脚本
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                env=env,
                timeout=600  # 10分钟超时
            )

            # 处理输出
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        self.log_message(f"📤 双数据库退款: {line}")

            if result.stderr:
                for line in result.stderr.strip().split('\n'):
                    if line.strip():
                        # 使用LogProcessor智能处理日志
                        formatted_message = LogProcessor.format_log_message(line, "双数据库退款 - ")
                        self.log_message(formatted_message)

            # 检查返回码
            if result.returncode == 0:
                self.log_message(f"双数据库退款处理成功，返回码: {result.returncode}")
                return True
            else:
                self.log_message(f"双数据库退款处理失败，返回码: {result.returncode}")
                return False

        except subprocess.TimeoutExpired:
            self.log_message("双数据库退款处理超时（超过10分钟）")
            return False
        except Exception as e:
            self.log_message(f"执行双数据库退款处理时出错: {str(e)}")
            return False

    def _process_refund_file(self, file_path: str, platform_type: str) -> bool:
        """处理退款文件

        Args:
            file_path: 要处理的退款文件路径
            platform_type: 平台类型 (IOT/ZERO)

        Returns:
            bool: 处理是否成功
        """
        try:
            # 获取优化版退款处理脚本路径
            try:
                script_path = self.config.get_script_path('refund_script_optimized')
            except (ScriptNotFoundError, MissingConfigError) as e:
                self.log_message(f"退款处理脚本配置错误: {e.get_user_message()}")
                return False

            # 获取数据库路径
            db_path = self.config.get_db_path()

            # 构建命令行参数（匹配退款处理脚本的参数格式）
            cmd = [
                sys.executable,  # Python解释器
                script_path,     # 退款处理脚本
                '--file', file_path,        # 要处理的文件
                '--db_path', db_path,       # 数据库路径
                '--platform', platform_type # 平台类型
            ]

            self.log_message(f"执行退款处理命令: {' '.join(cmd)}")

            # 🔧 设置编码环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

            # 执行退款处理脚本
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                env=env,
                timeout=300  # 5分钟超时
            )

            # 处理输出
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        self.log_message(f"📤 退款脚本: {line}")

            if result.stderr:
                for line in result.stderr.strip().split('\n'):
                    if line.strip():
                        # 使用LogProcessor智能处理日志
                        formatted_message = LogProcessor.format_log_message(line, "退款 - ")
                        self.log_message(formatted_message)

            # 检查返回码
            if result.returncode == 0:
                self.log_message(f"退款处理成功，返回码: {result.returncode}")
                return True
            else:
                self.log_message(f"退款处理失败，返回码: {result.returncode}")
                return False

        except subprocess.TimeoutExpired:
            self.log_message("退款处理超时（超过5分钟）")
            return False
        except Exception as e:
            self.log_message(f"执行退款处理时出错: {str(e)}")
            return False

    def _detect_platform_type(self, file_path: str) -> str:
        """检测平台类型 - 修复版"""
        try:
            filename = os.path.basename(file_path).upper()

            # 更精确的平台类型检测逻辑
            # 检查文件名中的关键词
            if 'ZEROIOT' in filename:
                return "IOT"  # zeroiot 文件属于 IOT 平台
            elif 'ZERO' in filename and 'IOT' not in filename:
                return "ZERO"  # 只有 ZERO 没有 IOT 的文件属于 ZERO 平台
            elif 'IOT' in filename:
                return "IOT"  # 包含 IOT 的文件属于 IOT 平台
            else:
                # 如果没有明确的平台标识，根据文件名模式判断
                if 'SETTLEMENT_REPORT' in filename:
                    # SETTLEMENT_REPORT 文件通常是 IOT 平台的
                    return "IOT"
                else:
                    return "IOT"  # 默认返回IOT
        except Exception:
            return "IOT"  # 默认返回IOT

# ==================== 数据库设置选项卡 ====================

class SettingsTab(BaseTab):
    """数据库设置选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner, backup_manager: DatabaseBackupManager):
        # 先调用父类构造函数
        super().__init__(parent, gui_updater, config, file_manager, process_runner)
        self.backup_manager = backup_manager

        # 然后设置正确的tab_type
        self.tab_type = "settings"

        # 重新注册日志控件以使用正确的tab_type
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)
            # 重新发送初始日志消息以使用正确的tab_type
            self.log_message("数据库设置已加载")
            self.log_message(f"当前数据库路径: {self.config.get_db_path()}")

    def create_ui(self):
        """创建数据库设置UI"""
        # 数据库路径设置
        db_frame = ttk.LabelFrame(self.frame, text="数据库设置", padding="10")
        db_frame.pack(fill=tk.X, pady=10)

        ttk.Label(db_frame, text="数据库路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.db_path_var = tk.StringVar(value=self.config.get_db_path())
        ttk.Entry(db_frame, textvariable=self.db_path_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(db_frame, text="浏览...", command=self.browse_db_path).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(db_frame, text="保存路径", command=self.save_db_path).grid(row=0, column=3, padx=5, pady=5)

        # 数据库操作
        operation_frame = ttk.LabelFrame(self.frame, text="数据库操作", padding="10")
        operation_frame.pack(fill=tk.X, pady=10)

        ttk.Button(operation_frame, text="备份数据库", command=self.backup_database).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(operation_frame, text="恢复数据库", command=self.restore_database).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(operation_frame, text="检查数据库", command=self.check_database).pack(side=tk.LEFT, padx=5, pady=5)

        # 备份管理区域
        backup_frame = ttk.LabelFrame(self.frame, text="备份管理", padding="10")
        backup_frame.pack(fill=tk.X, pady=10)

        # 备份列表
        ttk.Label(backup_frame, text="可用备份:").grid(row=0, column=0, sticky=tk.W, pady=5)

        # 创建备份列表框和滚动条
        list_frame = ttk.Frame(backup_frame)
        list_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)

        self.backup_listbox = tk.Listbox(list_frame, width=70, height=5)
        self.backup_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.backup_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.backup_listbox.configure(yscrollcommand=scrollbar.set)

        # 备份操作按钮
        button_frame = ttk.Frame(backup_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)

        ttk.Button(button_frame, text="刷新备份列表", command=self.refresh_backup_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="从选中备份恢复", command=self.restore_selected_backup).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除选中备份", command=self.delete_selected_backup).pack(side=tk.LEFT, padx=5)

        # 配置管理
        config_frame = ttk.LabelFrame(self.frame, text="配置管理", padding="10")
        config_frame.pack(fill=tk.X, pady=10)

        ttk.Button(config_frame, text="重新加载配置", command=self.reload_config).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(config_frame, text="重置为默认", command=self.reset_config).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(config_frame, text="打开配置文件", command=self.open_config_file).pack(side=tk.LEFT, padx=5, pady=5)

        # 创建日志区域
        self.log_text = self.create_log_area("设置日志")

        # 显示初始信息
        self.log_message("数据库设置已加载")
        self.log_message(f"当前数据库路径: {self.config.get_db_path()}")

        # 延迟刷新备份列表，确保所有组件都已初始化
        self.frame.after(100, self.refresh_backup_list)

    def browse_db_path(self):
        """浏览选择数据库路径"""
        # 获取当前数据库路径作为初始目录
        current_db_path = self.config.get_db_path()
        initial_dir = os.path.dirname(current_db_path) if current_db_path else os.getcwd()
        initial_file = os.path.basename(current_db_path) if current_db_path else "sales_reports.db"

        filename = filedialog.asksaveasfilename(
            title="选择数据库文件",
            initialdir=initial_dir,
            initialfile=initial_file,
            defaultextension=".db",
            filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
        )
        if filename:
            self.db_path_var.set(filename)
            self.log_message(f"已选择数据库路径: {filename}")

    @class_exception_handler("保存数据库路径", "settings")
    def save_db_path(self):
        """保存数据库路径"""
        new_path = self.db_path_var.get().strip()
        if not new_path:
            raise ConfigValidationError("db_path", "数据库路径不能为空")

        # 确保目录存在
        db_dir = os.path.dirname(new_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            self.log_message(f"已创建数据库目录: {db_dir}")

        # 保存配置
        self.config.set('Database', 'db_path', new_path)
        self.config.save_config()

        self.log_message(f"数据库路径已保存: {new_path}")

    @class_exception_handler("备份数据库", "settings")
    def backup_database(self):
        """备份数据库"""
        db_path = self.config.get_db_path()
        if not os.path.exists(db_path):
            raise FileNotFoundError(db_path, "备份")

        # 创建备份目录
        backup_dir = os.path.join(os.path.dirname(db_path), "backups")
        os.makedirs(backup_dir, exist_ok=True)

        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f"sales_reports_backup_{timestamp}.db")

        # 复制数据库文件
        import shutil
        shutil.copy2(db_path, backup_file)

        self.log_message(f"数据库备份成功: {backup_file}")

    @class_exception_handler("恢复数据库", "settings")
    def restore_database(self):
        """恢复数据库"""
        backup_file = filedialog.askopenfilename(
            title="选择备份文件",
            filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
        )

        if not backup_file:
            return

        if not os.path.exists(backup_file):
            raise FileNotFoundError(backup_file, "恢复")

        # 确认操作
        if not messagebox.askyesno("确认恢复", "恢复数据库将覆盖当前数据库，是否继续？"):
            return

        db_path = self.config.get_db_path()

        # 备份当前数据库
        if os.path.exists(db_path):
            backup_current = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(db_path, backup_current)
            self.log_message(f"当前数据库已备份为: {backup_current}")

        # 恢复数据库
        import shutil
        shutil.copy2(backup_file, db_path)

        self.log_message(f"数据库恢复成功: {backup_file}")

    @class_exception_handler("检查数据库", "settings")
    def check_database(self):
        """检查数据库"""
        db_path = self.config.get_db_path()

        if not os.path.exists(db_path):
            self.log_message(f"数据库文件不存在: {db_path}")
            return

        try:
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            self.log_message(f"数据库连接成功: {db_path}")
            self.log_message(f"数据库包含 {len(tables)} 个表:")
            for table in tables:
                self.log_message(f"  - {table[0]}")

            conn.close()

        except Exception as e:
            self.log_message(f"数据库检查失败: {str(e)}")

    @class_exception_handler("重新加载配置", "settings")
    def reload_config(self):
        """重新加载配置"""
        self.config._load_config()
        self.db_path_var.set(self.config.get_db_path())
        self.log_message("配置文件已重新加载")

    @class_exception_handler("重置配置", "settings")
    def reset_config(self):
        """重置为默认配置"""
        if not messagebox.askyesno("确认重置", "重置配置将恢复所有默认设置，是否继续？"):
            return

        self.config._create_default_config()
        self.db_path_var.set(self.config.get_db_path())
        self.log_message("配置已重置为默认设置")

    def open_config_file(self):
        """打开配置文件"""
        config_file = self.config.config_file
        if os.path.exists(config_file):
            try:
                os.startfile(config_file)  # Windows
                self.log_message(f"已打开配置文件: {config_file}")
            except:
                self.log_message(f"无法打开配置文件: {config_file}")
        else:
            self.log_message("配置文件不存在")

    @class_exception_handler("刷新备份列表", "settings")
    def refresh_backup_list(self):
        """刷新备份列表"""
        # 检查backup_manager是否已初始化
        if not hasattr(self, 'backup_manager') or self.backup_manager is None:
            self.log_message("备份管理器未初始化，跳过备份列表刷新")
            return

        # 清空列表
        self.backup_listbox.delete(0, tk.END)

        # 获取备份文件列表
        backup_files = self.backup_manager.get_backup_files()

        if not backup_files:
            self.log_message("没有找到备份文件")
            return

        # 填充备份文件列表
        for backup_file in backup_files:
            try:
                # 从文件名提取日期时间
                date_str = backup_file.replace("sales_reports_backup_", "").replace(".db", "")
                date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")

                # 获取文件大小
                file_path = os.path.join(self.backup_manager.backup_dir, backup_file)
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    display_text = f"{formatted_date} - {file_size:.2f} KB"
                else:
                    display_text = f"{formatted_date} - 文件不存在"

                self.backup_listbox.insert(tk.END, display_text)
            except Exception:
                self.backup_listbox.insert(tk.END, backup_file)

        # 如果有备份文件，默认选择第一个（最新的）
        if backup_files:
            self.backup_listbox.selection_set(0)
            self.log_message(f"已刷新备份列表，共{len(backup_files)}个备份文件")

        # 保存备份文件列表供其他方法使用
        self.backup_files = backup_files

    @class_exception_handler("恢复选中备份", "settings")
    def restore_selected_backup(self):
        """从选中的备份恢复数据库"""
        selection = self.backup_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个备份文件")
            return

        if not hasattr(self, 'backup_files') or not self.backup_files:
            messagebox.showwarning("警告", "备份文件列表为空，请刷新备份列表")
            return

        selected_index = selection[0]
        if selected_index >= len(self.backup_files):
            messagebox.showerror("错误", "选择的备份文件索引无效")
            return

        selected_backup = self.backup_files[selected_index]

        # 确认恢复
        if messagebox.askyesno("确认恢复",
                             f"确定要从以下备份恢复数据库吗？\n{selected_backup}\n\n这将覆盖当前的数据库！"):
            if self.backup_manager.restore_backup(selected_backup):
                self.log_message(f"数据库已从备份恢复: {selected_backup}")
                messagebox.showinfo("恢复成功", f"数据库已从备份恢复: {selected_backup}")
            else:
                self.log_message(f"数据库恢复失败: {selected_backup}")
                messagebox.showerror("恢复失败", "数据库恢复失败，请查看日志了解详情")

    @class_exception_handler("删除选中备份", "settings")
    def delete_selected_backup(self):
        """删除选中的备份文件"""
        selection = self.backup_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个备份文件")
            return

        if not hasattr(self, 'backup_files') or not self.backup_files:
            messagebox.showwarning("警告", "备份文件列表为空，请刷新备份列表")
            return

        selected_index = selection[0]
        if selected_index >= len(self.backup_files):
            messagebox.showerror("错误", "选择的备份文件索引无效")
            return

        selected_backup = self.backup_files[selected_index]

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除以下备份文件吗？\n{selected_backup}"):
            try:
                backup_path = os.path.join(self.backup_manager.backup_dir, selected_backup)
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                    self.log_message(f"已删除备份文件: {selected_backup}")
                    messagebox.showinfo("删除成功", f"已删除备份文件: {selected_backup}")
                    # 刷新备份列表
                    self.refresh_backup_list()
                else:
                    messagebox.showerror("删除失败", "备份文件不存在")
            except Exception as e:
                error_msg = f"删除备份文件失败: {str(e)}"
                self.log_message(error_msg)
                messagebox.showerror("删除失败", error_msg)

# ==================== 数据库配置选项卡 ====================

class DatabaseConfigTab:
    """数据库配置选项卡"""

    def __init__(self, parent, gui_updater):
        self.parent = parent
        self.gui_updater = gui_updater
        self.tab_type = "database_config"

        # 初始化双数据库管理器
        if DUAL_DATABASE_AVAILABLE:
            self.db_manager = get_dual_database_manager()
        else:
            self.db_manager = None

        # 创建界面
        self.create_widgets()

        # 注册日志控件
        self.gui_updater.register_log_widget(self.tab_type, self.log_text)

        # 初始化界面
        self.refresh_database_status()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.parent, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="数据库配置管理", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 数据库选择框架
        db_frame = ttk.LabelFrame(main_frame, text="数据库选择", padding="10")
        db_frame.pack(fill=tk.X, pady=(0, 10))

        # 数据库选择说明
        info_label = ttk.Label(db_frame, text="选择要使用的数据库类型。可以同时启用多个数据库。")
        info_label.pack(anchor=tk.W, pady=(0, 10))

        # 数据库配置按钮
        config_btn = ttk.Button(db_frame, text="配置数据库连接", command=self.open_database_config)
        config_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 刷新状态按钮
        refresh_btn = ttk.Button(db_frame, text="刷新状态", command=self.refresh_database_status)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 测试连接按钮
        test_btn = ttk.Button(db_frame, text="测试所有连接", command=self.test_all_connections)
        test_btn.pack(side=tk.LEFT)

        # 数据库状态框架
        status_frame = ttk.LabelFrame(main_frame, text="数据库状态", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 状态显示文本框
        self.status_text = tk.Text(status_frame, height=15, wrap=tk.WORD)
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)

        # 导入选项框架
        import_frame = ttk.LabelFrame(main_frame, text="数据导入选项", padding="10")
        import_frame.pack(fill=tk.X, pady=(0, 10))

        # 导入目标选择
        target_label = ttk.Label(import_frame, text="默认导入目标:")
        target_label.pack(side=tk.LEFT, padx=(0, 10))

        self.import_target_var = tk.StringVar(value="所有可用数据库")
        target_combo = ttk.Combobox(import_frame, textvariable=self.import_target_var,
                                   values=["所有可用数据库", "仅SQLite", "仅PostgreSQL", "用户选择"])
        target_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 日志滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def log_message(self, message: str):
        """记录日志消息"""
        self.gui_updater.safe_log(message, self.tab_type)

    @class_exception_handler("打开数据库配置", "database_config")
    def open_database_config(self):
        """打开数据库配置对话框"""
        if not DUAL_DATABASE_AVAILABLE:
            messagebox.showerror("错误", "双数据库模块不可用")
            return

        try:
            dialog = DatabaseConfigDialog(self.parent)
            result = dialog.show()

            if result == "saved":
                self.log_message("数据库配置已保存")
                self.refresh_database_status()
            else:
                self.log_message("数据库配置已取消")

        except Exception as e:
            self.log_message(f"打开配置对话框失败: {str(e)}")
            messagebox.showerror("错误", f"打开配置对话框失败: {str(e)}")

    @class_exception_handler("刷新数据库状态", "database_config")
    def refresh_database_status(self):
        """刷新数据库状态"""
        if not DUAL_DATABASE_AVAILABLE or not self.db_manager:
            self.status_text.delete(1.0, tk.END)
            self.status_text.insert(tk.END, "双数据库模块不可用\n")
            return

        try:
            status = self.db_manager.get_database_status()

            self.status_text.delete(1.0, tk.END)

            for db_type, info in status.items():
                self.status_text.insert(tk.END, f"=== {db_type} 数据库 ===\n")
                self.status_text.insert(tk.END, f"启用状态: {'✅ 已启用' if info['enabled'] else '❌ 未启用'}\n")
                self.status_text.insert(tk.END, f"连接状态: {'✅ 连接成功' if info['connected'] else '❌ 连接失败'}\n")

                if db_type == 'SQLite' and 'path' in info:
                    self.status_text.insert(tk.END, f"数据库路径: {info['path']}\n")
                elif db_type == 'PostgreSQL' and 'config' in info:
                    config = info['config']
                    self.status_text.insert(tk.END, f"主机: {config.get('host', 'N/A')}\n")
                    self.status_text.insert(tk.END, f"端口: {config.get('port', 'N/A')}\n")
                    self.status_text.insert(tk.END, f"数据库: {config.get('database', 'N/A')}\n")
                    self.status_text.insert(tk.END, f"用户: {config.get('user', 'N/A')}\n")

                if info['connected'] and info['tables']:
                    self.status_text.insert(tk.END, f"表数量: {len(info['tables'])}\n")
                    total_rows = sum(table['rows'] for table in info['tables'])
                    self.status_text.insert(tk.END, f"总行数: {total_rows:,}\n")

                    # 显示主要表的行数
                    main_tables = ['IOT_Sales', 'ZERO_Sales', 'APP_Sales', 'Combined_Sales']
                    for table_info in info['tables']:
                        if table_info['name'] in main_tables:
                            self.status_text.insert(tk.END, f"  {table_info['name']}: {table_info['rows']:,} 行\n")

                self.status_text.insert(tk.END, "\n")

            # 显示可用数据库
            available = self.db_manager.get_available_databases()
            self.status_text.insert(tk.END, f"可用数据库: {', '.join(available) if available else '无'}\n")

            self.log_message("数据库状态已刷新")

        except Exception as e:
            self.status_text.delete(1.0, tk.END)
            self.status_text.insert(tk.END, f"获取数据库状态失败: {str(e)}\n")
            self.log_message(f"刷新状态失败: {str(e)}")

    @class_exception_handler("测试数据库连接", "database_config")
    def test_all_connections(self):
        """测试所有数据库连接"""
        if not DUAL_DATABASE_AVAILABLE or not self.db_manager:
            messagebox.showerror("错误", "双数据库模块不可用")
            return

        try:
            self.log_message("开始测试数据库连接...")

            # 测试SQLite
            if self.db_manager.sqlite_config['enabled']:
                if self.db_manager.test_sqlite_connection():
                    self.log_message("✅ SQLite连接测试成功")
                else:
                    self.log_message("❌ SQLite连接测试失败")
            else:
                self.log_message("⚪ SQLite未启用")

            # 测试PostgreSQL
            if self.db_manager.postgresql_config['enabled']:
                if self.db_manager.test_postgresql_connection():
                    self.log_message("✅ PostgreSQL连接测试成功")
                else:
                    self.log_message("❌ PostgreSQL连接测试失败")
            else:
                self.log_message("⚪ PostgreSQL未启用")

            # 刷新状态
            self.refresh_database_status()

            messagebox.showinfo("测试完成", "数据库连接测试完成，请查看日志了解详情")

        except Exception as e:
            self.log_message(f"测试连接失败: {str(e)}")
            messagebox.showerror("测试失败", f"测试连接失败: {str(e)}")

# ==================== 主应用程序类 ====================

class DataProcessingApp(tk.Tk):
    """数据处理与导入应用 - 完整版"""

    def __init__(self):
        super().__init__()

        # 初始化组件
        self.config = ConfigManager()
        self.gui_updater = SafeGUIUpdater(self)
        self.file_manager = FileManager(self.config)
        self.backup_manager = DatabaseBackupManager(self.config, self.gui_updater)
        self.process_runner = ProcessRunner(self.gui_updater)

        # 设置窗口
        self.setup_window()

        # 创建UI
        self.create_ui()

        # 设置样式
        self.set_style()

        logger.info("数据处理与导入应用已启动")

    def setup_window(self):
        """设置窗口属性"""
        self.title("数据处理与导入应用 - 完整版 v2.0")

        # 从配置获取窗口大小
        width = int(self.config.get('UI', 'window_width', '900'))
        height = int(self.config.get('UI', 'window_height', '700'))
        self.geometry(f"{width}x{height}")
        self.resizable(True, True)

        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "kibdh-cj84b-001.ico")
        if os.path.exists(icon_path):
            self.iconbitmap(icon_path)

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题栏
        self.create_title_bar()

        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建各个选项卡
        self.create_tabs()

        # 创建状态栏
        self.create_status_bar()

    def create_title_bar(self):
        """创建标题栏"""
        title_frame = ttk.Frame(self.main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        # 应用标题
        title_label = ttk.Label(title_frame, text="数据处理与导入应用 - 完整版 v2.0", font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT, padx=10)

        # 当前日期
        date_label = ttk.Label(title_frame, text=datetime.now().strftime("%Y-%m-%d"))
        date_label.pack(side=tk.RIGHT, padx=10)

        # 版本信息
        version_label = ttk.Label(title_frame, text="集成自定义异常处理系统", font=("Arial", 10))
        version_label.pack(side=tk.RIGHT, padx=10)

    def create_tabs(self):
        """创建选项卡"""
        # 数据处理选项卡
        processing_frame = ttk.Frame(self.notebook)
        self.notebook.add(processing_frame, text="数据处理")
        self.processing_tab = ProcessingTab(processing_frame, self.gui_updater, self.config,
                                          self.file_manager, self.process_runner)

        # 数据导入选项卡
        import_frame = ttk.Frame(self.notebook)
        self.notebook.add(import_frame, text="数据导入")
        self.import_tab = ImportTab(import_frame, self.gui_updater, self.config,
                                  self.file_manager, self.process_runner, self.backup_manager)

        # 退款处理选项卡
        refund_frame = ttk.Frame(self.notebook)
        self.notebook.add(refund_frame, text="退款处理")
        self.refund_tab = RefundTab(refund_frame, self.gui_updater, self.config,
                                  self.file_manager, self.process_runner, self.backup_manager)

        # 数据库设置选项卡
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="数据库设置")
        self.settings_tab = SettingsTab(settings_frame, self.gui_updater, self.config,
                                      self.file_manager, self.process_runner, self.backup_manager)

        # 如果支持双数据库，添加数据库配置选项卡
        if DUAL_DATABASE_AVAILABLE:
            db_config_frame = ttk.Frame(self.notebook)
            self.notebook.add(db_config_frame, text="数据库配置")
            self.db_config_tab = DatabaseConfigTab(db_config_frame, self.gui_updater)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 集成自定义异常处理系统")
        self.status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 注册状态变量到GUI更新器
        self.gui_updater.register_status_var(self.status_var)

    def set_style(self):
        """设置iPhone风格的UI"""
        self.style = ttk.Style()

        # 设置整体主题
        if "clam" in self.style.theme_names():
            self.style.theme_use("clam")

        # 设置颜色
        bg_color = "#f5f5f7"  # 浅灰色背景
        accent_color = "#0071e3"  # 蓝色强调色
        text_color = "#1d1d1f"  # 深灰色文本

        # 配置各种元素样式
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=text_color)
        self.style.configure("TButton", background=accent_color, foreground="white", padding=6)
        self.style.map("TButton", background=[("active", "#0077ed")])
        self.style.configure("TNotebook", background=bg_color, tabmargins=[2, 5, 2, 0])
        self.style.configure("TNotebook.Tab", background=bg_color, padding=[10, 5], font=("Arial", 10))
        self.style.map("TNotebook.Tab", background=[("selected", accent_color)], foreground=[("selected", "white")])

        # 设置窗口背景色
        self.configure(background=bg_color)
        self.main_frame.configure(style="TFrame")

# ==================== 主程序入口 ====================

def main():
    """主函数"""
    try:
        print("🚀 启动数据处理与导入应用 - 完整版...")
        print("✅ 集成自定义异常处理系统")
        print("✅ 线程安全的GUI更新")
        print("✅ 模块化架构设计")
        print("✅ 配置文件管理")

        # 创建应用程序实例
        app = DataProcessingApp()

        print("🎉 应用程序启动成功！")
        app.mainloop()

    except Exception as e:
        logger.error(f"应用程序启动失败: {str(e)}")
        print(f"❌ 错误: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
