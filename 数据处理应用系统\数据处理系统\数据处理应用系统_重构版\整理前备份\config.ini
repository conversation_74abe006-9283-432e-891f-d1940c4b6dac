[Database]
db_type = postgresql
db_host = localhost
db_port = 5432
db_name = postgres
db_user = postgres
db_password = zerochon
db_path = C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db

[Scripts]
intelligent_processor = report 脚本 3.0.py
modular_processor = report 模块化设计 7.0.py
refund_script_optimized = scripts/refund_process_optimized.py
data_import_script_optimized = scripts/data_import_optimized.py

[UI]
window_width = 900
window_height = 700
theme = iphone_style

[SQLite]
db_path = C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db
enabled = True

[PostgreSQL]
host = localhost
port = 5432
database = postgres
user = postgres
password = zerochon
enabled = True

