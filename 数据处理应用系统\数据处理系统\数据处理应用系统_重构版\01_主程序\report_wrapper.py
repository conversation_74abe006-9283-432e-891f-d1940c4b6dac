# -*- coding: utf-8 -*-
"""
数据处理包装脚本
解决编码问题的包装器
"""

import os
import sys
import subprocess
import locale

def setup_encoding():
    """设置编码环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # 设置控制台代码页
    try:
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except:
        pass
    
    # 设置locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
        except:
            pass
    
    # 重新配置标准输出
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except:
        pass

def main():
    """主函数"""
    # 设置编码
    setup_encoding()
    
    # 获取原始脚本路径
    original_script = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    if not os.path.exists(original_script):
        print(f"错误: 找不到原始脚本 {original_script}")
        return 1
    
    # 传递所有命令行参数
    cmd = [sys.executable, original_script] + sys.argv[1:]
    
    # 设置环境
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # 运行原始脚本
    try:
        result = subprocess.run(cmd, env=env)
        return result.returncode
    except Exception as e:
        print(f"运行脚本时出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
