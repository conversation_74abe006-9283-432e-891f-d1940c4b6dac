# -*- coding: utf-8 -*-
"""
创建包含特殊字符的测试Excel文件
用于验证Unicode修复效果
"""

import pandas as pd
import os

def create_test_excel():
    """创建包含特殊字符的测试Excel文件"""
    
    # 创建包含各种特殊字符的测试数据（符合数据库表结构）
    test_data = {
        'Order_price': [100.0, 200.0, 300.0, 150.0, 250.0],
        'Order_time': [
            '2024-12-19 10:00:00',
            '2024-12-19 10:01:00',
            '2024-12-19 10:02:00',
            '2024-12-19 10:03:00',
            '2024-12-19 10:04:00'
        ],
        'Order_status': [
            'Finished',
            'Payment – Success',  # em dash
            'Order "Complete"',   # smart quotes
            'Refund…',           # ellipsis
            'Processing'
        ],
        'Equipment_ID': ['E001', 'E002', 'E003', 'E004', 'E005'],
        'Equipment_name': [
            'Normal Equipment',
            'Equipment – Type A',  # em dash
            'Equipment "Special"', # smart quotes
            'Equipment…Pro',       # ellipsis
            'Standard Equipment'
        ],
        'Branch_name': [
            'Main Branch',
            'Branch – North',      # em dash
            'Branch "Central"',    # smart quotes
            'Branch…South',        # ellipsis
            'West Branch'
        ],
        'Order_types': ['Normal', 'VIP', 'Express', 'Standard', 'Premium'],
        'Transaction_Num': ['T001', 'T002', 'T003', 'T004', 'T005'],
        'Order_No': ['ORD001', 'ORD002', 'ORD003', 'ORD004', 'ORD005'],
        'Transaction_ID': ['TXN001', 'TXN002', 'TXN003', 'TXN004', 'TXN005'],
        'Payment_date': [
            '2024-12-19',
            '2024-12-19',
            '2024-12-19',
            '2024-12-19',
            '2024-12-19'
        ]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    output_file = 'test_unicode_excel.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"Created test Excel file: {output_file}")
    print(f"File contains {len(df)} rows with special Unicode characters")
    print("\nSample data:")
    print(df.head())
    
    return output_file

def main():
    """主函数"""
    print("=== Creating Test Excel File with Unicode Characters ===")
    
    try:
        output_file = create_test_excel()
        
        # 验证文件是否创建成功
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"\nSUCCESS: Test file created successfully")
            print(f"File: {output_file}")
            print(f"Size: {file_size} bytes")
            
            # 尝试重新读取文件验证
            try:
                df_read = pd.read_excel(output_file)
                print(f"Verification: Successfully read back {len(df_read)} rows")
                return 0
            except Exception as e:
                print(f"WARNING: Could not read back the file: {e}")
                return 1
        else:
            print("ERROR: Failed to create test file")
            return 1
            
    except Exception as e:
        print(f"ERROR: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
