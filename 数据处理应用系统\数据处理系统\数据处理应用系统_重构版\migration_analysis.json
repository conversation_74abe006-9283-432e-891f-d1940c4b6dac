{"database_path": "C:\\Users\\<USER>\\Desktop\\Day Report\\database\\sales_reports.db", "tables": {"APP_Sales": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE APP_Sales (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT\n, Transaction_Num TEXT)", "row_count": 4094, "postgresql_equivalent": "CREATE TABLE APP_Sales (\n    id INTEGER PRIMARY KEY,\n    <PERSON><PERSON><PERSON>_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "Combined_Sales": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Platform", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Combined_Sales (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    Platform TEXT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT\n)", "row_count": 126949, "postgresql_equivalent": "CREATE TABLE Combined_Sales (\n    id INTEGER PRIMARY KEY,\n    Platform TEXT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT\n);"}, "Date_Fix_Logs": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Platform", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Method", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Fix_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Before_Count", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "After_Count", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Error", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Date_Fix_Logs (\n        ID INTEGER PRIMARY KEY AUTOINCREMENT,\n        Platform TEXT,\n        Method TEXT,\n        Fix_Date TEXT,\n        Before_Count INTEGER,\n        After_Count INTEGER,\n        Error TEXT\n    )", "row_count": 50, "postgresql_equivalent": "CREATE TABLE Date_Fix_Logs (\n    ID INTEGER PRIMARY KEY,\n    Platform TEXT,\n    Method TEXT,\n    Fix_Date TEXT,\n    Before_Count INTEGER,\n    After_Count INTEGER,\n    Error TEXT\n);"}, "ZERO_Sales": {"columns": [{"name": "id", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE \"ZERO_Sales\"(\n  id INT,\n  Copartner_name TEXT,\n  Order_No TEXT,\n  Order_types TEXT,\n  Order_status TEXT,\n  Order_price REAL,\n  Payment REAL,\n  Order_time TEXT,\n  Equipment_ID TEXT,\n  Equipment_name TEXT,\n  Branch_name TEXT,\n  Payment_date TEXT,\n  User_name TEXT,\n  Time TEXT,\n  Matched_Order_ID TEXT,\n  OrderTime_dt TEXT,\n  Import_Date TEXT\n, Transaction_Num TEXT)", "row_count": 66506, "postgresql_equivalent": "CREATE TABLE ZERO_Sales (\n    id INTEGER,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "Import_Logs": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Platform", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Filename", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Sheet_Name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Message", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Log_Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Import_Logs (\n            ID INTEGER PRIMARY KEY AUTOINCREMENT,\n            Platform TEXT,\n            Filename TEXT,\n            Sheet_Name TEXT,\n            Import_Date TEXT,\n            Status TEXT,\n            Message TEXT,\n            Log_Time TEXT\n        )", "row_count": 11, "postgresql_equivalent": "CREATE TABLE Import_Logs (\n    ID INTEGER PRIMARY KEY,\n    Platform TEXT,\n    Filename TEXT,\n    Sheet_Name TEXT,\n    Import_Date TEXT,\n    Status TEXT,\n    Message TEXT,\n    Log_Time TEXT\n);"}, "Chair_Serial_No": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Serial_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Chair_Model", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Register_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Chair_Serial_No (\n        ID INTEGER PRIMARY KEY AUTOINCREMENT,\n        Serial_No TEXT UNIQUE,\n        Chair_Model TEXT,\n        Register_Date TEXT\n    )", "row_count": 0, "postgresql_equivalent": "CREATE TABLE Chair_Serial_No (\n    ID INTEGER PRIMARY KEY,\n    Serial_No TEXT,\n    Chair_Model TEXT,\n    Register_Date TEXT\n);"}, "Logs": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Timestamp", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Action", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Description", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Target", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Logs (\n            ID INTEGER PRIMARY KEY AUTOINCREMENT,\n            Timestamp TEXT,\n            Action TEXT,\n            Description TEXT,\n            Target TEXT\n        )", "row_count": 734, "postgresql_equivalent": "CREATE TABLE Logs (\n    ID INTEGER PRIMARY KEY,\n    Timestamp TEXT,\n    Action TEXT,\n    Description TEXT,\n    Target TEXT\n);"}, "Equipment_ID_bak": {"columns": [{"name": "ID", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "STATE", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Location", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Quantity", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Chair_Serial_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Sim_Card_Model", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Sim_Card_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Layer", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Company", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Effective_From", "type": "NUM", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Effective_To", "type": "NUM", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Rental", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "SIMCARDID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "NUM", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Last_Updated", "type": "NUM", "not_null": false, "default_value": null, "primary_key": false}, {"name": "CurrentFlag", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "DATE", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Equipment_ID_bak(\n  ID INT,\n  STATE TEXT,\n  Location TEXT,\n  Quantity INT,\n  Chair_Serial_No TEXT,\n  Sim_Card_Model TEXT,\n  Sim_Card_No TEXT,\n  Layer TEXT,\n  Company TEXT,\n  Effective_From NUM,\n  Effective_To NUM,\n  Rental REAL,\n  SIMCARDID TEXT,\n  Import_Date NUM,\n  Last_Updated NUM,\n  CurrentFlag TEXT,\n  DATE TEXT\n)", "row_count": 1100, "postgresql_equivalent": "CREATE TABLE Equipment_ID_bak (\n    ID INTEGER,\n    STATE TEXT,\n    Location TEXT,\n    Quantity INTEGER,\n    Chair_Serial_No TEXT,\n    Sim_Card_Model TEXT,\n    Sim_Card_No TEXT,\n    Layer TEXT,\n    Company TEXT,\n    Effective_From TEXT,\n    Effective_To TEXT,\n    Rental REAL,\n    <PERSON><PERSON>CA<PERSON><PERSON> TEXT,\n    Import_Date TEXT,\n    Last_Updated TEXT,\n    CurrentFlag TEXT,\n    DATE TEXT\n);"}, "Equipment_ID": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "STATE", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Location", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Quantity", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Chair_Serial_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Sim_Card_Model", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Sim_Card_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Layer", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Company", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Effective_From", "type": "DATE", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Effective_To", "type": "DATE", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Rental", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "SIMCARDID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Last_Updated", "type": "DATETIME", "not_null": false, "default_value": null, "primary_key": false}, {"name": "CurrentFlag", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "DATE", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE \"Equipment_ID\" (\"ID\" INTEGER PRIMARY KEY AUTOINCREMENT, \"STATE\" TEXT, \"Location\" TEXT, \"Quantity\" INTEGER, \"Chair_Serial_No\" TEXT, \"Sim_Card_Model\" TEXT, \"Sim_Card_No\" TEXT, \"Layer\" TEXT, \"Company\" TEXT, \"Effective_From\" DATE, \"Effective_To\" DATE, \"Rental\" TEXT, \"SIMCARDID\" TEXT, \"Import_Date\" DATETIME, \"Last_Updated\" DATETIME, \"CurrentFlag\" TEXT, \"DATE\" TEXT)", "row_count": 1205, "postgresql_equivalent": "CREATE TABLE Equipment_ID (\n    ID INTEGER PRIMARY KEY,\n    STATE TEXT,\n    Location TEXT,\n    Quantity INTEGER,\n    Chair_Serial_No TEXT,\n    Sim_Card_Model TEXT,\n    Sim_Card_No TEXT,\n    Layer TEXT,\n    Company TEXT,\n    Effective_From DATE,\n    Effective_To DATE,\n    Rental TEXT,\n    <PERSON><PERSON><PERSON><PERSON><PERSON> TEXT,\n    Import_Date TIMESTAMP,\n    Last_Updated TIMESTAMP,\n    CurrentFlag TEXT,\n    DATE TEXT\n);"}, "REFUND_LIST": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Transaction Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Settlement Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Refund Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Merchant Ref ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Channel", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Billing", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Actual", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Refund", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "MDR", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "GST", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Refund Fee", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Quantity", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Reference1", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Reference2", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "PROCESS", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE REFUND_LIST (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            [Transaction Date] TEXT,\n            [Settlement Date] TEXT,\n            [Refund Date] TEXT,\n            [Merchant Ref ID] TEXT,\n            [Transaction ID] TEXT,\n            [Channel] TEXT,\n            [Order ID] TEXT,\n            [Currency] TEXT,\n            [Billing] REAL,\n            [Actual] REAL,\n            [Refund] REAL,\n            [MDR] REAL,\n            [GST] REAL,\n            [Status] TEXT,\n            [Refund Fee] REAL,\n            [Quantity] INTEGER,\n            [Reference1] TEXT,\n            [Reference2] TEXT,\n            [PROCESS] TEXT\n        )", "row_count": 879, "postgresql_equivalent": "CREATE TABLE REFUND_LIST (\n    id INTEGER PRIMARY KEY,\n    Transaction Date TEXT,\n    Settlement Date TEXT,\n    Refund Date TEXT,\n    Merchant Ref ID TEXT,\n    Transaction ID TEXT,\n    Channel TEXT,\n    Order ID TEXT,\n    Currency TEXT,\n    Billing REAL,\n    Actual REAL,\n    Refund REAL,\n    MDR REAL,\n    GST REAL,\n    Status TEXT,\n    Refund Fee REAL,\n    Quantity INTEGER,\n    Reference1 TEXT,\n    Reference2 TEXT,\n    PROCESS TEXT\n);"}, "IOT_Sales": {"columns": [{"name": "id", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE IOT_Sales (id INT, Copartner_name TEXT, Order_No TEXT, Order_types TEXT, Order_status TEXT, Order_price REAL, Payment REAL, Order_time TEXT, Equipment_ID TEXT, Equipment_name TEXT, Branch_name TEXT, Payment_date TEXT, User_name TEXT, Time TEXT, Matched_Order_ID TEXT, OrderTime_dt TEXT, Import_Date TEXT, Transaction_Num TEXT)", "row_count": 376901, "postgresql_equivalent": "CREATE TABLE IOT_Sales (\n    id INTEGER,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "Equipment_ID_Anomalies": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Platform", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "File_Source", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Detection_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Notes", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE Equipment_ID_Anomalies (\n                ID INTEGER PRIMARY KEY AUTOINCREMENT,\n                Equipment_ID TEXT,\n                Platform TEXT,\n                File_Source TEXT,\n                Detection_Date TEXT,\n                Notes TEXT\n            )", "row_count": 0, "postgresql_equivalent": "CREATE TABLE Equipment_ID_Anomalies (\n    ID INTEGER PRIMARY KEY,\n    Equipment_ID TEXT,\n    Platform TEXT,\n    File_Source TEXT,\n    Detection_Date TEXT,\n    Notes TEXT\n);"}, "IOT_Sales_Refunding": {"columns": [{"name": "id", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE IOT_Sales_Refunding (\n                id INT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n            )", "row_count": 2502, "postgresql_equivalent": "CREATE TABLE IOT_Sales_Refunding (\n    id INTEGER,\n    <PERSON><PERSON>ner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "IOT_Sales_Close": {"columns": [{"name": "id", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE IOT_Sales_Close (\n                id INT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n            )", "row_count": 18606, "postgresql_equivalent": "CREATE TABLE IOT_Sales_Close (\n    id INTEGER,\n    <PERSON><PERSON><PERSON>_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "ZERO_Sales_Refunding": {"columns": [{"name": "id", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE ZERO_Sales_Refunding (\n                id INT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n            )", "row_count": 72, "postgresql_equivalent": "CREATE TABLE ZERO_Sales_Refunding (\n    id INTEGER,\n    <PERSON><PERSON><PERSON>_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "ZERO_Sales_Close": {"columns": [{"name": "id", "type": "INT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "REAL", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}], "create_sql": "CREATE TABLE ZERO_Sales_Close (\n                id INT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n            )", "row_count": 0, "postgresql_equivalent": "CREATE TABLE ZERO_Sales_Close (\n    id INTEGER,\n    <PERSON><PERSON><PERSON>_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price REAL,\n    Payment REAL,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Import_Date TEXT,\n    Transaction_Num TEXT\n);"}, "APP_Sales_Refunding": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Timestamp", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "create_sql": "CREATE TABLE APP_Sales_Refunding (\n    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price TEXT,\n    Payment TEXT,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Transaction_Num TEXT,\n    Import_Date TEXT,\n    Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n)", "row_count": 0, "postgresql_equivalent": "CREATE TABLE APP_Sales_Refunding (\n    ID INTEGER PRIMARY KEY,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price TEXT,\n    Payment TEXT,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Transaction_Num TEXT,\n    Import_Date TEXT,\n    Import_Timestamp TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP'\n);"}, "APP_Sales_Close": {"columns": [{"name": "ID", "type": "INTEGER", "not_null": false, "default_value": null, "primary_key": true}, {"name": "<PERSON><PERSON>ner_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_No", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_types", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_status", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_price", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Order_time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Equipment_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Branch_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Payment_date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "User_name", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Time", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Matched_Order_ID", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "OrderTime_dt", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Transaction_Num", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Date", "type": "TEXT", "not_null": false, "default_value": null, "primary_key": false}, {"name": "Import_Timestamp", "type": "TIMESTAMP", "not_null": false, "default_value": "CURRENT_TIMESTAMP", "primary_key": false}], "create_sql": "CREATE TABLE APP_Sales_Close (\n    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n    Copartner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price TEXT,\n    Payment TEXT,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Transaction_Num TEXT,\n    Import_Date TEXT,\n    Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n)", "row_count": 0, "postgresql_equivalent": "CREATE TABLE APP_Sales_Close (\n    ID INTEGER PRIMARY KEY,\n    <PERSON>rtner_name TEXT,\n    Order_No TEXT,\n    Order_types TEXT,\n    Order_status TEXT,\n    Order_price TEXT,\n    Payment TEXT,\n    Order_time TEXT,\n    Equipment_ID TEXT,\n    Equipment_name TEXT,\n    Branch_name TEXT,\n    Payment_date TEXT,\n    User_name TEXT,\n    Time TEXT,\n    Matched_Order_ID TEXT,\n    OrderTime_dt TEXT,\n    Transaction_Num TEXT,\n    Import_Date TEXT,\n    Import_Timestamp TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP'\n);"}}, "views": {"IOT_Daily_Sales": {"original_sql": "CREATE VIEW IOT_Daily_Sales AS\n            SELECT \n                Equipment_ID as Chair_Serial_No,\n                strftime('%Y-%m-%d', Order_time) as Sale_Date,\n                SUM(Order_Price) as Daily_IOT_Price,\n                COUNT(*) as Daily_IOT_Count\n            FROM IOT_Sales\n            GROUP BY Equipment_ID, Sale_Date", "postgresql_equivalent": "CREATE VIEW IOT_Daily_Sales AS\n            SELECT \n                Equipment_ID as Chair_Serial_No,\n                strftime('%Y-%m-%d', Order_time) as Sale_Date,\n                SUM(Order_Price) as Daily_IOT_Price,\n                COUNT(*) as Daily_IOT_Count\n            FROM IOT_Sales\n            GROUP BY Equipment_ID, Sale_Date"}, "ZERO_Daily_Sales": {"original_sql": "CREATE VIEW ZERO_Daily_Sales AS\n            SELECT \n                Equipment_ID as Chair_Serial_No,\n                strftime('%Y-%m-%d', Order_time) as Sale_Date,\n                SUM(Order_Price) as Daily_ZERO_Price,\n                COUNT(*) as Daily_ZERO_Count\n            FROM ZERO_Sales\n            GROUP BY Equipment_ID, Sale_Date", "postgresql_equivalent": "CREATE VIEW ZERO_Daily_Sales AS\n            SELECT \n                Equipment_ID as Chair_Serial_No,\n                strftime('%Y-%m-%d', Order_time) as Sale_Date,\n                SUM(Order_Price) as Daily_ZERO_Price,\n                COUNT(*) as Daily_ZERO_Count\n            FROM ZERO_Sales\n            GROUP BY Equipment_ID, Sale_Date"}, "Valid_Sales": {"original_sql": "CREATE VIEW Valid_Sales AS\n            SELECT \n                Chair_Serial_No,\n                STATE,\n                Location,\n                Quantity,\n                Layer,\n                Effective_From,\n                Effective_To,\n                Rental,\n                IOT_Sales,\n                ZERO_Sales,\n                IOT_Order_Price,\n                ZERO_Order_Price,\n                Total_Order_Price,\n                IOT_Date,\n                ZERO_Date\n            FROM Sales_Combined\n            WHERE Version_Rank = 1\n            AND (Effective_To >= date('now') OR Effective_To IS NULL)", "postgresql_equivalent": "CREATE VIEW Valid_Sales AS\n            SELECT \n                Chair_Serial_No,\n                STATE,\n                Location,\n                Quantity,\n                Layer,\n                Effective_From,\n                Effective_To,\n                Rental,\n                IOT_Sales,\n                ZERO_Sales,\n                IOT_Order_Price,\n                ZERO_Order_Price,\n                Total_Order_Price,\n                IOT_Date,\n                ZERO_Date\n            FROM Sales_Combined\n            WHERE Version_Rank = 1\n            AND (Effective_To >= date('now') OR Effective_To IS NULL)"}, "Equipment_Valid": {"original_sql": "CREATE VIEW Equipment_Valid AS SELECT \n    Chair_Serial_No, \n    STATE, \n    Location, \n    Quantity, \n    Layer, \n    CASE \n        WHEN Effective_From IS NULL THEN date('1900-01-01') \n        ELSE date(Effective_From) \n    END as Effective_From, \n    CASE \n        WHEN Effective_To IS NULL THEN date('9999-12-31') \n        ELSE date(Effective_To) \n    END as Effective_To, \n    Rental, \n    ID, \n    DATE\nFROM Equipment_ID", "postgresql_equivalent": "CREATE VIEW Equipment_Valid AS SELECT \n    Chair_Serial_No, \n    STATE, \n    Location, \n    Quantity, \n    Layer, \n    CASE \n        WHEN Effective_From IS NULL THEN date('1900-01-01') \n        ELSE date(Effective_From) \n    END as Effective_From, \n    CASE \n        WHEN Effective_To IS NULL THEN date('9999-12-31') \n        ELSE date(Effective_To) \n    END as Effective_To, \n    Rental, \n    ID, \n    DATE\nFROM Equipment_ID"}, "APP_Daily_Sales": {"original_sql": "CREATE VIEW APP_Daily_Sales AS\n            SELECT \n                Equipment_ID as Chair_Serial_No,\n                strftime('%Y-%m-%d', Order_time) as Sale_Date,\n                SUM(Order_Price) as Daily_APP_Price,\n                COUNT(*) as Daily_APP_Count\n            FROM APP_Sales\n            GROUP BY Equipment_ID, Sale_Date", "postgresql_equivalent": "CREATE VIEW APP_Daily_Sales AS\n            SELECT \n                Equipment_ID as Chair_Serial_No,\n                strftime('%Y-%m-%d', Order_time) as Sale_Date,\n                SUM(Order_Price) as Daily_APP_Price,\n                COUNT(*) as Daily_APP_Count\n            FROM APP_Sales\n            GROUP BY Equipment_ID, Sale_Date"}, "Sales_Combined": {"original_sql": "CREATE VIEW Sales_Combined AS\n            SELECT \n                e.Chair_Serial_No,\n                e.STATE,\n                e.Location,\n                e.Quantity,\n                e.Layer,\n                CASE \n                    WHEN e.Effective_From IS NULL THEN date('1900-01-01')\n                    ELSE date(e.Effective_From)\n                END as Effective_From,\n                CASE \n                    WHEN e.Effective_To IS NULL THEN date('9999-12-31')\n                    ELSE date(e.Effective_To)\n                END as Effective_To,\n                e.Rental,\n                COUNT(i.ID) as IOT_Sales,\n                COUNT(z.ID) as ZERO_Sales,\n                COUNT(a.ID) as APP_Sales,\n                COALESCE(SUM(i.Order_Price), 0) as IOT_Order_Price,\n                COALESCE(SUM(z.Order_Price), 0) as ZERO_Order_Price,\n                COALESCE(SUM(a.Order_Price), 0) as APP_Order_Price,\n                (COALESCE(SUM(i.Order_Price), 0) + COALESCE(SUM(z.Order_Price), 0) + COALESCE(SUM(a.Order_Price), 0)) as Total_Order_Price,\n                MAX(i.Order_time) as IOT_Date,\n                MAX(z.Order_time) as ZERO_Date,\n                MAX(a.Order_time) as APP_Date,\n                ROW_NUMBER() OVER (\n                    PARTITION BY e.Chair_Serial_No \n                    ORDER BY \n                        CASE \n                            WHEN e.Effective_From IS NULL THEN 1 \n                            ELSE 0 \n                        END DESC,\n                        date(e.Effective_From) DESC,\n                        e.ID DESC\n                ) as Version_Rank\n            FROM Equipment_ID e\n            LEFT JOIN IOT_Sales i ON e.Chair_Serial_No = i.Equipment_ID\n            LEFT JOIN ZERO_Sales z ON e.Chair_Serial_No = z.Equipment_ID\n            LEFT JOIN APP_Sales a ON e.Chair_Serial_No = a.Equipment_ID\n            GROUP BY e.Chair_Serial_No, e.STATE, e.Location, e.Quantity, e.Layer, \n                     e.Effective_From, e.Effective_To, e.Rental, e.ID", "postgresql_equivalent": "CREATE VIEW Sales_Combined AS\n            SELECT \n                e.Chair_Serial_No,\n                e.STATE,\n                e.Location,\n                e.Quantity,\n                e.Layer,\n                CASE \n                    WHEN e.Effective_From IS NULL THEN date('1900-01-01')\n                    ELSE date(e.Effective_From)\n                END as Effective_From,\n                CASE \n                    WHEN e.Effective_To IS NULL THEN date('9999-12-31')\n                    ELSE date(e.Effective_To)\n                END as Effective_To,\n                e.Rental,\n                COUNT(i.ID) as IOT_Sales,\n                COUNT(z.ID) as ZERO_Sales,\n                COUNT(a.ID) as APP_Sales,\n                COALESCE(SUM(i.Order_Price), 0) as IOT_Order_Price,\n                COALESCE(SUM(z.Order_Price), 0) as ZERO_Order_Price,\n                COALESCE(SUM(a.Order_Price), 0) as APP_Order_Price,\n                (COALESCE(SUM(i.Order_Price), 0) + COALESCE(SUM(z.Order_Price), 0) + COALESCE(SUM(a.Order_Price), 0)) as Total_Order_Price,\n                MAX(i.Order_time) as IOT_Date,\n                MAX(z.Order_time) as ZERO_Date,\n                MAX(a.Order_time) as APP_Date,\n                ROW_NUMBER() OVER (\n                    PARTITION BY e.Chair_Serial_No \n                    ORDER BY \n                        CASE \n                            WHEN e.Effective_From IS NULL THEN 1 \n                            ELSE 0 \n                        END DESC,\n                        date(e.Effective_From) DESC,\n                        e.ID DESC\n                ) as Version_Rank\n            FROM Equipment_ID e\n            LEFT JOIN IOT_Sales i ON e.Chair_Serial_No = i.Equipment_ID\n            LEFT JOIN ZERO_Sales z ON e.Chair_Serial_No = z.Equipment_ID\n            LEFT JOIN APP_Sales a ON e.Chair_Serial_No = a.Equipment_ID\n            GROUP BY e.Chair_Serial_No, e.STATE, e.Location, e.Quantity, e.Layer, \n                     e.Effective_From, e.Effective_To, e.Rental, e.ID"}, "Daily_Sales": {"original_sql": "CREATE VIEW Daily_Sales AS SELECT\n  Sale_Date,\n  SUM(Order_Price) AS Daily_Total_Price,\n  SUM(CASE WHEN Source = 'IOT'  THEN Order_Price ELSE 0 END) AS IOT_Total_Sales,\n  SUM(CASE WHEN Source = 'ZERO' THEN Order_Price ELSE 0 END) AS ZERO_Total_Sales,\n  SUM(CASE WHEN Source = 'APP'  THEN Order_Price ELSE 0 END) AS APP_Total_Sales\nFROM (\n  SELECT Order_time,\n         Order_Price,\n         'IOT'  AS Source,\n         strftime('%Y-%m-%d', Order_time) AS Sale_Date\n    FROM IOT_Sales\n  UNION ALL\n  SELECT Order_time,\n         Order_Price,\n         'ZERO' AS Source,\n         strftime('%Y-%m-%d', Order_time) AS Sale_Date\n    FROM ZERO_Sales\n  UNION ALL\n  SELECT Order_time,\n         Order_Price,\n         'APP'  AS Source,\n         strftime('%Y-%m-%d', Order_time) AS Sale_Date\n    FROM APP_Sales\n)\nGROUP BY Sale_Date\nORDER BY Sale_Date", "postgresql_equivalent": "CREATE VIEW Daily_Sales AS SELECT\n  Sale_Date,\n  SUM(Order_Price) AS Daily_Total_Price,\n  SUM(CASE WHEN Source = 'IOT'  THEN Order_Price ELSE 0 END) AS IOT_Total_Sales,\n  SUM(CASE WHEN Source = 'ZERO' THEN Order_Price ELSE 0 END) AS ZERO_Total_Sales,\n  SUM(CASE WHEN Source = 'APP'  THEN Order_Price ELSE 0 END) AS APP_Total_Sales\nFROM (\n  SELECT Order_time,\n         Order_Price,\n         'IOT'  AS Source,\n         strftime('%Y-%m-%d', Order_time) AS Sale_Date\n    FROM IOT_Sales\n  UNION ALL\n  SELECT Order_time,\n         Order_Price,\n         'ZERO' AS Source,\n         strftime('%Y-%m-%d', Order_time) AS Sale_Date\n    FROM ZERO_Sales\n  UNION ALL\n  SELECT Order_time,\n         Order_Price,\n         'APP'  AS Source,\n         strftime('%Y-%m-%d', Order_time) AS Sale_Date\n    FROM APP_Sales\n)\nGROUP BY Sale_Date\nORDER BY Sale_Date"}, "Current_Equipment": {"original_sql": "CREATE VIEW Current_Equipment AS SELECT\n  ev.Chair_Serial_No,\n  ev.STATE,\n  ev.Location,\n  ev.Quantity,\n  ev.Layer,\n  ev.Effective_From,\n  ev.Effective_To,\n  ev.Rental,\n  ev.DATE,\n\n  -- 仅对当前生效期内的记录进行计数\n  COUNT(\n    CASE \n      WHEN ev.Effective_From <= date('now')\n       AND ev.Effective_To   >= date('now')\n      THEN 1 \n      ELSE NULL \n    END\n  ) OVER (\n    PARTITION BY ev.Chair_Serial_No\n  ) AS OccurrenceCount,  -- :contentReference[oaicite:0]{index=0} :contentReference[oaicite:1]{index=1}\n\n  -- 如果同一设备号在当前生效期内出现多于一次，就标记“DUPLICATE”\n  CASE\n    WHEN COUNT(\n           CASE \n             WHEN ev.Effective_From <= date('now')\n              AND ev.Effective_To   >= date('now')\n             THEN 1 \n             ELSE NULL \n           END\n         ) OVER (\n           PARTITION BY ev.Chair_Serial_No\n         ) > 1\n    THEN 'DUPLICATE'\n    ELSE 'OK'\n  END AS Duplicate_Flag\n\nFROM Equipment_Valid AS ev", "postgresql_equivalent": "CREATE VIEW Current_Equipment AS SELECT\n  ev.Chair_Serial_No,\n  ev.STATE,\n  ev.Location,\n  ev.Quantity,\n  ev.Layer,\n  ev.Effective_From,\n  ev.Effective_To,\n  ev.Rental,\n  ev.DATE,\n\n  -- 仅对当前生效期内的记录进行计数\n  COUNT(\n    CASE \n      WHEN ev.Effective_From <= date('now')\n       AND ev.Effective_To   >= date('now')\n      THEN 1 \n      ELSE NULL \n    END\n  ) OVER (\n    PARTITION BY ev.Chair_Serial_No\n  ) AS OccurrenceCount,  -- :contentReference[oaicite:0]{index=0} :contentReference[oaicite:1]{index=1}\n\n  -- 如果同一设备号在当前生效期内出现多于一次，就标记“DUPLICATE”\n  CASE\n    WHEN COUNT(\n           CASE \n             WHEN ev.Effective_From <= date('now')\n              AND ev.Effective_To   >= date('now')\n             THEN 1 \n             ELSE NULL \n           END\n         ) OVER (\n           PARTITION BY ev.Chair_Serial_No\n         ) > 1\n    THEN 'DUPLICATE'\n    ELSE 'OK'\n  END AS Duplicate_Flag\n\nFROM Equipment_Valid AS ev"}, "Daily_Equipment_Sales": {"original_sql": "CREATE VIEW Daily_Equipment_Sales AS WITH \n  -- 1) 预聚合销售数据\n  Sales_Aggs AS (\n    SELECT \n      Chair_Serial_No, \n      Sale_Date,\n      SUM(CASE WHEN Source = 'IOT' THEN Price ELSE 0 END) AS IOT_Price,\n      SUM(CASE WHEN Source = 'ZERO' THEN Price ELSE 0 END) AS ZERO_Price,\n      SUM(CASE WHEN Source = 'APP' THEN Price ELSE 0 END) AS APP_Price,\n      SUM(CASE WHEN Source = 'IOT' THEN Count ELSE 0 END) AS IOT_Count,\n      SUM(CASE WHEN Source = 'ZERO' THEN Count ELSE 0 END) AS ZERO_Count,\n      SUM(CASE WHEN Source = 'APP' THEN Count ELSE 0 END) AS APP_Count,\n      SUM(Price) AS Total_Price\n    FROM (\n      SELECT Chair_Serial_No, Sale_Date, 'IOT' AS Source, Daily_IOT_Price AS Price, Daily_IOT_Count AS Count FROM IOT_Daily_Sales\n      UNION ALL\n      SELECT Chair_Serial_No, Sale_Date, 'ZERO', Daily_ZERO_Price, Daily_ZERO_Count FROM ZERO_Daily_Sales\n      UNION ALL\n      SELECT Chair_Serial_No, Sale_Date, 'APP', Daily_APP_Price, Daily_APP_Count FROM APP_Daily_Sales\n    ) AS AllSales\n    GROUP BY Chair_Serial_No, Sale_Date\n  ),\n  \n  -- 2) 构建日期维度\n  All_Dates AS (\n    SELECT DISTINCT Sale_Date FROM Sales_Aggs\n  ),\n  \n  -- 3) 主关联逻辑\n  Main_Join AS (\n    SELECT\n      e.Chair_Serial_No,\n      e.STATE,\n      e.Location,\n      e.Quantity,\n      e.Layer,\n      e.Effective_From,\n      e.Effective_To,\n      e.Rental,  -- 直接保留原始 NULL 值\n      e.DATE,\n      d.Sale_Date,\n      COALESCE(a.IOT_Price, 0) AS IOT_Price,\n      COALESCE(a.ZERO_Price, 0) AS ZERO_Price,\n      COALESCE(a.APP_Price, 0) AS APP_Price,\n      COALESCE(a.IOT_Count, 0) AS IOT_Count,\n      COALESCE(a.ZERO_Count, 0) AS ZERO_Count,\n      COALESCE(a.APP_Count, 0) AS APP_Count,\n      COALESCE(a.Total_Price, 0) AS Total_Price\n    FROM Current_Equipment e\n    CROSS JOIN All_Dates d\n    LEFT JOIN Sales_Aggs a \n      ON e.Chair_Serial_No = a.Chair_Serial_No \n      AND d.Sale_Date = a.Sale_Date\n    WHERE d.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')\n  ),\n  \n  -- 4) 补缺未匹配的销售记录\n  Unmatched_Sales AS (\n    SELECT \n      s.Chair_Serial_No,\n      NULL AS STATE,\n      NULL AS Location,\n      NULL AS Quantity,\n      NULL AS Layer,\n      NULL AS Effective_From,\n      NULL AS Effective_To,\n      NULL AS Rental,  -- 保持 NULL 以便 Power Query 显示为空白\n      NULL AS DATE,\n      s.Sale_Date,\n      COALESCE(a.IOT_Price, 0) AS IOT_Price,\n      COALESCE(a.ZERO_Price, 0) AS ZERO_Price,\n      COALESCE(a.APP_Price, 0) AS APP_Price,\n      COALESCE(a.IOT_Count, 0) AS IOT_Count,\n      COALESCE(a.ZERO_Count, 0) AS ZERO_Count,\n      COALESCE(a.APP_Count, 0) AS APP_Count,\n      COALESCE(a.Total_Price, 0) AS Total_Price\n    FROM (\n      SELECT Chair_Serial_No, Sale_Date FROM Sales_Aggs\n    ) AS s\n    LEFT JOIN Current_Equipment e \n      ON s.Chair_Serial_No = e.Chair_Serial_No \n      AND s.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')\n    LEFT JOIN Sales_Aggs a \n      ON s.Chair_Serial_No = a.Chair_Serial_No \n      AND s.Sale_Date = a.Sale_Date\n    WHERE e.Chair_Serial_No IS NULL\n  )\n\n-- 合并最终结果\nSELECT * FROM Main_Join\nUNION ALL\nSELECT * FROM Unmatched_Sales\nORDER BY Chair_Serial_No, Sale_Date", "postgresql_equivalent": "CREATE VIEW Daily_Equipment_Sales AS WITH \n  -- 1) 预聚合销售数据\n  Sales_Aggs AS (\n    SELECT \n      Chair_Serial_No, \n      Sale_Date,\n      SUM(CASE WHEN Source = 'IOT' THEN Price ELSE 0 END) AS IOT_Price,\n      SUM(CASE WHEN Source = 'ZERO' THEN Price ELSE 0 END) AS ZERO_Price,\n      SUM(CASE WHEN Source = 'APP' THEN Price ELSE 0 END) AS APP_Price,\n      SUM(CASE WHEN Source = 'IOT' THEN Count ELSE 0 END) AS IOT_Count,\n      SUM(CASE WHEN Source = 'ZERO' THEN Count ELSE 0 END) AS ZERO_Count,\n      SUM(CASE WHEN Source = 'APP' THEN Count ELSE 0 END) AS APP_Count,\n      SUM(Price) AS Total_Price\n    FROM (\n      SELECT Chair_Serial_No, Sale_Date, 'IOT' AS Source, Daily_IOT_Price AS Price, Daily_IOT_Count AS Count FROM IOT_Daily_Sales\n      UNION ALL\n      SELECT Chair_Serial_No, Sale_Date, 'ZERO', Daily_ZERO_Price, Daily_ZERO_Count FROM ZERO_Daily_Sales\n      UNION ALL\n      SELECT Chair_Serial_No, Sale_Date, 'APP', Daily_APP_Price, Daily_APP_Count FROM APP_Daily_Sales\n    ) AS AllSales\n    GROUP BY Chair_Serial_No, Sale_Date\n  ),\n  \n  -- 2) 构建日期维度\n  All_Dates AS (\n    SELECT DISTINCT Sale_Date FROM Sales_Aggs\n  ),\n  \n  -- 3) 主关联逻辑\n  Main_Join AS (\n    SELECT\n      e.Chair_Serial_No,\n      e.STATE,\n      e.Location,\n      e.Quantity,\n      e.Layer,\n      e.Effective_From,\n      e.Effective_To,\n      e.Rental,  -- 直接保留原始 NULL 值\n      e.DATE,\n      d.Sale_Date,\n      COALESCE(a.IOT_Price, 0) AS IOT_Price,\n      COALESCE(a.ZERO_Price, 0) AS ZERO_Price,\n      COALESCE(a.APP_Price, 0) AS APP_Price,\n      COALESCE(a.IOT_Count, 0) AS IOT_Count,\n      COALESCE(a.ZERO_Count, 0) AS ZERO_Count,\n      COALESCE(a.APP_Count, 0) AS APP_Count,\n      COALESCE(a.Total_Price, 0) AS Total_Price\n    FROM Current_Equipment e\n    CROSS JOIN All_Dates d\n    LEFT JOIN Sales_Aggs a \n      ON e.Chair_Serial_No = a.Chair_Serial_No \n      AND d.Sale_Date = a.Sale_Date\n    WHERE d.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')\n  ),\n  \n  -- 4) 补缺未匹配的销售记录\n  Unmatched_Sales AS (\n    SELECT \n      s.Chair_Serial_No,\n      NULL AS STATE,\n      NULL AS Location,\n      NULL AS Quantity,\n      NULL AS Layer,\n      NULL AS Effective_From,\n      NULL AS Effective_To,\n      NULL AS Rental,  -- 保持 NULL 以便 Power Query 显示为空白\n      NULL AS DATE,\n      s.Sale_Date,\n      COALESCE(a.IOT_Price, 0) AS IOT_Price,\n      COALESCE(a.ZERO_Price, 0) AS ZERO_Price,\n      COALESCE(a.APP_Price, 0) AS APP_Price,\n      COALESCE(a.IOT_Count, 0) AS IOT_Count,\n      COALESCE(a.ZERO_Count, 0) AS ZERO_Count,\n      COALESCE(a.APP_Count, 0) AS APP_Count,\n      COALESCE(a.Total_Price, 0) AS Total_Price\n    FROM (\n      SELECT Chair_Serial_No, Sale_Date FROM Sales_Aggs\n    ) AS s\n    LEFT JOIN Current_Equipment e \n      ON s.Chair_Serial_No = e.Chair_Serial_No \n      AND s.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')\n    LEFT JOIN Sales_Aggs a \n      ON s.Chair_Serial_No = a.Chair_Serial_No \n      AND s.Sale_Date = a.Sale_Date\n    WHERE e.Chair_Serial_No IS NULL\n  )\n\n-- 合并最终结果\nSELECT * FROM Main_Join\nUNION ALL\nSELECT * FROM Unmatched_Sales\nORDER BY Chair_Serial_No, Sale_Date"}, "Refunding_Close_Summary": {"original_sql": "CREATE VIEW Refunding_Close_Summary AS WITH\n  -- 1) 预聚合 Refunding 和 Close 数据\n  Refunding_Close_Aggs AS (\n    SELECT\n      Equipment_ID               AS Chair_Serial_No,\n      DATE(Order_time)           AS Sale_Date,\n      SUM(CASE WHEN Source = 'IOT_Refunding'  THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Refund,\n      SUM(CASE WHEN Source = 'IOT_Close'      THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Close,\n      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Refund,\n      SUM(CASE WHEN Source = 'ZERO_Close'     THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Close,\n      SUM(CASE WHEN Source = 'IOT_Refunding'  THEN 1 ELSE 0 END) AS IOT_Count_Refund,\n      SUM(CASE WHEN Source = 'IOT_Close'      THEN 1 ELSE 0 END) AS IOT_Count_Close,\n      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN 1 ELSE 0 END) AS ZERO_Count_Refund,\n      SUM(CASE WHEN Source = 'ZERO_Close'     THEN 1 ELSE 0 END) AS ZERO_Count_Close,\n      SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Price\n    FROM (\n      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Refunding' AS Source FROM IOT_Sales_Refunding\n      UNION ALL\n      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Close'     AS Source FROM IOT_Sales_Close\n      UNION ALL\n      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Refunding' AS Source FROM ZERO_Sales_Refunding\n      UNION ALL\n      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Close'     AS Source FROM ZERO_Sales_Close\n    ) AS AllRefundingClose\n    WHERE Equipment_ID IS NOT NULL\n      AND Order_time IS NOT NULL\n    GROUP BY Equipment_ID, DATE(Order_time)\n  ),\n\n  -- 2) 与 Current_Equipment 做左关联，补齐设备信息\n  Main_Join AS (\n    SELECT\n      a.Chair_Serial_No,\n      e.STATE,\n      e.Location,\n      e.Quantity,\n      e.Layer,\n      e.Effective_From,\n      e.Effective_To,\n      e.Rental,\n      e.DATE,\n      a.Sale_Date,\n      COALESCE(a.IOT_Price_Refund, 0) AS IOT_Price_Refund,\n      COALESCE(a.IOT_Price_Close,  0) AS IOT_Price_Close,\n      COALESCE(a.ZERO_Price_Refund, 0) AS ZERO_Price_Refund,\n      COALESCE(a.ZERO_Price_Close,  0) AS ZERO_Price_Close,\n      COALESCE(a.IOT_Count_Refund,  0) AS IOT_Count_Refund,\n      COALESCE(a.IOT_Count_Close,   0) AS IOT_Count_Close,\n      COALESCE(a.ZERO_Count_Refund, 0) AS ZERO_Count_Refund,\n      COALESCE(a.ZERO_Count_Close,  0) AS ZERO_Count_Close,\n      COALESCE(a.Total_Price,       0) AS Total_Price,\n      -- 新增金额合计\n      COALESCE(a.IOT_Price_Refund, 0) + COALESCE(a.ZERO_Price_Refund, 0) AS Total_Refund,\n      COALESCE(a.IOT_Price_Close,  0) + COALESCE(a.ZERO_Price_Close,  0) AS Total_Close,\n      -- 新增笔数合计\n      COALESCE(a.IOT_Count_Refund, 0) + COALESCE(a.ZERO_Count_Refund, 0) AS Total_Refund_Count,\n      COALESCE(a.IOT_Count_Close,  0) + COALESCE(a.ZERO_Count_Close,  0) AS Total_Close_Count\n    FROM Refunding_Close_Aggs AS a\n    LEFT JOIN Current_Equipment AS e\n      ON a.Chair_Serial_No = e.Chair_Serial_No\n     AND a.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01')\n                         AND IFNULL(e.Effective_To,   '2100-12-31')\n  )\n\n-- 最终结果\nSELECT *\nFROM Main_Join\nORDER BY Chair_Serial_No, Sale_Date", "postgresql_equivalent": "CREATE VIEW Refunding_Close_Summary AS WITH\n  -- 1) 预聚合 Refunding 和 Close 数据\n  Refunding_Close_Aggs AS (\n    SELECT\n      Equipment_ID               AS Chair_Serial_No,\n      DATE(Order_time)           AS Sale_Date,\n      SUM(CASE WHEN Source = 'IOT_Refunding'  THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Refund,\n      SUM(CASE WHEN Source = 'IOT_Close'      THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Close,\n      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Refund,\n      SUM(CASE WHEN Source = 'ZERO_Close'     THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Close,\n      SUM(CASE WHEN Source = 'IOT_Refunding'  THEN 1 ELSE 0 END) AS IOT_Count_Refund,\n      SUM(CASE WHEN Source = 'IOT_Close'      THEN 1 ELSE 0 END) AS IOT_Count_Close,\n      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN 1 ELSE 0 END) AS ZERO_Count_Refund,\n      SUM(CASE WHEN Source = 'ZERO_Close'     THEN 1 ELSE 0 END) AS ZERO_Count_Close,\n      SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Price\n    FROM (\n      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Refunding' AS Source FROM IOT_Sales_Refunding\n      UNION ALL\n      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Close'     AS Source FROM IOT_Sales_Close\n      UNION ALL\n      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Refunding' AS Source FROM ZERO_Sales_Refunding\n      UNION ALL\n      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Close'     AS Source FROM ZERO_Sales_Close\n    ) AS AllRefundingClose\n    WHERE Equipment_ID IS NOT NULL\n      AND Order_time IS NOT NULL\n    GROUP BY Equipment_ID, DATE(Order_time)\n  ),\n\n  -- 2) 与 Current_Equipment 做左关联，补齐设备信息\n  Main_Join AS (\n    SELECT\n      a.Chair_Serial_No,\n      e.STATE,\n      e.Location,\n      e.Quantity,\n      e.Layer,\n      e.Effective_From,\n      e.Effective_To,\n      e.Rental,\n      e.DATE,\n      a.Sale_Date,\n      COALESCE(a.IOT_Price_Refund, 0) AS IOT_Price_Refund,\n      COALESCE(a.IOT_Price_Close,  0) AS IOT_Price_Close,\n      COALESCE(a.ZERO_Price_Refund, 0) AS ZERO_Price_Refund,\n      COALESCE(a.ZERO_Price_Close,  0) AS ZERO_Price_Close,\n      COALESCE(a.IOT_Count_Refund,  0) AS IOT_Count_Refund,\n      COALESCE(a.IOT_Count_Close,   0) AS IOT_Count_Close,\n      COALESCE(a.ZERO_Count_Refund, 0) AS ZERO_Count_Refund,\n      COALESCE(a.ZERO_Count_Close,  0) AS ZERO_Count_Close,\n      COALESCE(a.Total_Price,       0) AS Total_Price,\n      -- 新增金额合计\n      COALESCE(a.IOT_Price_Refund, 0) + COALESCE(a.ZERO_Price_Refund, 0) AS Total_Refund,\n      COALESCE(a.IOT_Price_Close,  0) + COALESCE(a.ZERO_Price_Close,  0) AS Total_Close,\n      -- 新增笔数合计\n      COALESCE(a.IOT_Count_Refund, 0) + COALESCE(a.ZERO_Count_Refund, 0) AS Total_Refund_Count,\n      COALESCE(a.IOT_Count_Close,  0) + COALESCE(a.ZERO_Count_Close,  0) AS Total_Close_Count\n    FROM Refunding_Close_Aggs AS a\n    LEFT JOIN Current_Equipment AS e\n      ON a.Chair_Serial_No = e.Chair_Serial_No\n     AND a.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01')\n                         AND IFNULL(e.Effective_To,   '2100-12-31')\n  )\n\n-- 最终结果\nSELECT *\nFROM Main_Join\nORDER BY Chair_Serial_No, Sale_Date"}}, "indexes": {"idx_chair_serial_no": {"original_sql": "CREATE INDEX idx_chair_serial_no ON Equipment_ID(Chair_Serial_No)", "postgresql_equivalent": "CREATE INDEX idx_chair_serial_no ON Equipment_ID(Chair_Serial_No)"}, "idx_effective_dates": {"original_sql": "CREATE INDEX idx_effective_dates ON Equipment_ID(Effective_From, Effective_To)", "postgresql_equivalent": "CREATE INDEX idx_effective_dates ON Equipment_ID(Effective_From, Effective_To)"}, "idx_location": {"original_sql": "CREATE INDEX idx_location ON Equipment_ID(Location)", "postgresql_equivalent": "CREATE INDEX idx_location ON Equipment_ID(Location)"}, "idx_state": {"original_sql": "CREATE INDEX idx_state ON Equipment_ID(STATE)", "postgresql_equivalent": "CREATE INDEX idx_state ON Equipment_ID(STATE)"}, "idx_last_updated": {"original_sql": "CREATE INDEX idx_last_updated ON Equipment_ID(Last_Updated)", "postgresql_equivalent": "CREATE INDEX idx_last_updated ON Equipment_ID(Last_Updated)"}, "idx_logs_timestamp": {"original_sql": "CREATE INDEX idx_logs_timestamp ON Logs(Timestamp)", "postgresql_equivalent": "CREATE INDEX idx_logs_timestamp ON Logs(Timestamp)"}}, "data_statistics": {"APP_Sales": {"row_count": 4094, "column_stats": {"id": {"min_value": 1, "max_value": 4094, "non_null_count": 4094}, "Copartner_name": {"distinct_values": 1, "non_null_count": 2878, "max_length": 5}, "Order_No": {"distinct_values": 2877, "non_null_count": 2877, "max_length": 31}, "Order_types": {"distinct_values": 1, "non_null_count": 4094, "max_length": 9}, "Order_status": {"distinct_values": 1, "non_null_count": 4094, "max_length": 6}, "Order_price": {"min_value": 0.0, "max_value": 30.0, "non_null_count": 4094}, "Payment": {"min_value": 0.0, "max_value": 30.0, "non_null_count": 2877}, "Order_time": {"distinct_values": 43, "non_null_count": 4094, "max_length": 10}, "Equipment_ID": {"distinct_values": 835, "non_null_count": 4094, "max_length": 9}, "Equipment_name": {"distinct_values": 356, "non_null_count": 2878, "max_length": 47}, "Branch_name": {"distinct_values": 32, "non_null_count": 2878, "max_length": 24}, "Payment_date": {"distinct_values": 29, "non_null_count": 2877, "max_length": 10}, "User_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Time": {"distinct_values": 3922, "non_null_count": 4093, "max_length": 8}, "Matched_Order_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "OrderTime_dt": {"distinct_values": 4083, "non_null_count": 4093, "max_length": 19}, "Import_Date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Transaction_Num": {"distinct_values": 993, "non_null_count": 993, "max_length": 14}}}, "Combined_Sales": {"row_count": 126949, "column_stats": {"id": {"min_value": 1, "max_value": 126949, "non_null_count": 126949}, "Platform": {"distinct_values": 2, "non_null_count": 126949, "max_length": 4}, "Copartner_name": {"distinct_values": 2, "non_null_count": 126644, "max_length": 5}, "Order_No": {"distinct_values": 125969, "non_null_count": 126646, "max_length": 31}, "Order_types": {"distinct_values": 2, "non_null_count": 126694, "max_length": 13}, "Order_status": {"distinct_values": 1, "non_null_count": 126948, "max_length": 6}, "Order_price": {"min_value": 0.09, "max_value": 17007.1, "non_null_count": 126949}, "Payment": {"min_value": 0.0, "max_value": 100.0, "non_null_count": 126696}, "Order_time": {"distinct_values": 58411, "non_null_count": 126948, "max_length": 19}, "Equipment_ID": {"distinct_values": 1772, "non_null_count": 126948, "max_length": 23}, "Equipment_name": {"distinct_values": 464, "non_null_count": 126864, "max_length": 49}, "Branch_name": {"distinct_values": 35, "non_null_count": 126864, "max_length": 38}, "Payment_date": {"distinct_values": 119966, "non_null_count": 126606, "max_length": 19}, "User_name": {"distinct_values": 9774, "non_null_count": 12041, "max_length": 8}, "Time": {"distinct_values": 53045, "non_null_count": 121601, "max_length": 17}, "Matched_Order_ID": {"distinct_values": 34, "non_null_count": 34, "max_length": 31}, "OrderTime_dt": {"distinct_values": 53403, "non_null_count": 57237, "max_length": 19}, "Import_Date": {"distinct_values": 31, "non_null_count": 126949, "max_length": 10}}}, "Date_Fix_Logs": {"row_count": 50, "column_stats": {"ID": {"min_value": 1, "max_value": 50, "non_null_count": 50}, "Platform": {"distinct_values": 2, "non_null_count": 50, "max_length": 4}, "Method": {"distinct_values": 6, "non_null_count": 50, "max_length": 14}, "Fix_Date": {"distinct_values": 35, "non_null_count": 50, "max_length": 19}, "Before_Count": {"min_value": 14321, "max_value": 91749, "non_null_count": 50}, "After_Count": {"min_value": 0, "max_value": 91748, "non_null_count": 50}, "Error": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}}}, "ZERO_Sales": {"row_count": 66506, "column_stats": {"id": {"min_value": 1, "max_value": 50140, "non_null_count": 48842}, "Copartner_name": {"distinct_values": 1, "non_null_count": 66426, "max_length": 4}, "Order_No": {"distinct_values": 65751, "non_null_count": 66424, "max_length": 31}, "Order_types": {"distinct_values": 1, "non_null_count": 66478, "max_length": 13}, "Order_status": {"distinct_values": 1, "non_null_count": 66506, "max_length": 6}, "Order_price": {"min_value": 0.1, "max_value": 40.0, "non_null_count": 66506}, "Payment": {"min_value": 0.0, "max_value": 40.0, "non_null_count": 66478}, "Order_time": {"distinct_values": 104, "non_null_count": 66506, "max_length": 10}, "Equipment_ID": {"distinct_values": 565, "non_null_count": 66506, "max_length": 9}, "Equipment_name": {"distinct_values": 256, "non_null_count": 66498, "max_length": 37}, "Branch_name": {"distinct_values": 29, "non_null_count": 66498, "max_length": 20}, "Payment_date": {"distinct_values": 33390, "non_null_count": 66424, "max_length": 19}, "User_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Time": {"distinct_values": 35507, "non_null_count": 64526, "max_length": 17}, "Matched_Order_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "OrderTime_dt": {"distinct_values": 43102, "non_null_count": 43583, "max_length": 19}, "Import_Date": {"distinct_values": 53, "non_null_count": 48252, "max_length": 10}, "Transaction_Num": {"distinct_values": 1458, "non_null_count": 1458, "max_length": 10}}}, "Import_Logs": {"row_count": 11, "column_stats": {"ID": {"min_value": 1, "max_value": 17, "non_null_count": 11}, "Platform": {"distinct_values": 5, "non_null_count": 11, "max_length": 13}, "Filename": {"distinct_values": 4, "non_null_count": 11, "max_length": 25}, "Sheet_Name": {"distinct_values": 1, "non_null_count": 5, "max_length": 9}, "Import_Date": {"distinct_values": 2, "non_null_count": 4, "max_length": 19}, "Status": {"distinct_values": 2, "non_null_count": 11, "max_length": 2}, "Message": {"distinct_values": 10, "non_null_count": 11, "max_length": 109}, "Log_Time": {"distinct_values": 11, "non_null_count": 11, "max_length": 19}}}, "Chair_Serial_No": {"row_count": 0, "column_stats": {"ID": {"min_value": null, "max_value": null, "non_null_count": 0}, "Serial_No": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Chair_Model": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Register_Date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}}}, "Logs": {"row_count": 734, "column_stats": {"ID": {"min_value": 1, "max_value": 734, "non_null_count": 734}, "Timestamp": {"distinct_values": 734, "non_null_count": 734, "max_length": 23}, "Action": {"distinct_values": 16, "non_null_count": 734, "max_length": 8}, "Description": {"distinct_values": 421, "non_null_count": 734, "max_length": 270}, "Target": {"distinct_values": 357, "non_null_count": 734, "max_length": 65}}}, "Equipment_ID_bak": {"row_count": 1100, "column_stats": {"ID": {"min_value": 1, "max_value": 1111, "non_null_count": 1100}, "STATE": {"distinct_values": 16, "non_null_count": 1100, "max_length": 19}, "Location": {"distinct_values": 359, "non_null_count": 1100, "max_length": 46}, "Quantity": {"min_value": 1, "max_value": 16, "non_null_count": 1100}, "Chair_Serial_No": {"distinct_values": 1099, "non_null_count": 1100, "max_length": 9}, "Sim_Card_Model": {"distinct_values": 4, "non_null_count": 1059, "max_length": 11}, "Sim_Card_No": {"distinct_values": 554, "non_null_count": 563, "max_length": 13}, "Layer": {"distinct_values": 55, "non_null_count": 1085, "max_length": 15}, "Company": {"distinct_values": 7, "non_null_count": 186, "max_length": 11}, "Rental": {"min_value": 0.15, "max_value": "SOLD TO INVESTOR", "non_null_count": 1087}, "SIMCARDID": {"distinct_values": 25, "non_null_count": 1059, "max_length": 20}, "CurrentFlag": {"distinct_values": 2, "non_null_count": 5, "max_length": 8}, "DATE": {"distinct_values": 244, "non_null_count": 1091, "max_length": 40}}}, "Equipment_ID": {"row_count": 1205, "column_stats": {"ID": {"min_value": 1, "max_value": 1233, "non_null_count": 1205}, "STATE": {"distinct_values": 16, "non_null_count": 1205, "max_length": 19}, "Location": {"distinct_values": 390, "non_null_count": 1205, "max_length": 48}, "Quantity": {"min_value": 1, "max_value": 16, "non_null_count": 1205}, "Chair_Serial_No": {"distinct_values": 1167, "non_null_count": 1205, "max_length": 9}, "Sim_Card_Model": {"distinct_values": 4, "non_null_count": 1158, "max_length": 11}, "Sim_Card_No": {"distinct_values": 554, "non_null_count": 599, "max_length": 13}, "Layer": {"distinct_values": 55, "non_null_count": 1198, "max_length": 15}, "Company": {"distinct_values": 7, "non_null_count": 185, "max_length": 11}, "Rental": {"distinct_values": 71, "non_null_count": 1188, "max_length": 16}, "SIMCARDID": {"distinct_values": 67, "non_null_count": 1158, "max_length": 20}, "CurrentFlag": {"distinct_values": 2, "non_null_count": 4, "max_length": 8}, "DATE": {"distinct_values": 280, "non_null_count": 1203, "max_length": 40}}}, "REFUND_LIST": {"row_count": 879, "column_stats": {"id": {"min_value": 555, "max_value": 1486, "non_null_count": 879}, "Transaction Date": {"error": "near \"Transaction\": syntax error"}, "Settlement Date": {"error": "near \"Date\": syntax error"}, "Refund Date": {"error": "near \"Date\": syntax error"}, "Merchant Ref ID": {"error": "near \"Ref\": syntax error"}, "Transaction ID": {"error": "near \"Transaction\": syntax error"}, "Channel": {"distinct_values": 12, "non_null_count": 879, "max_length": 24}, "Order ID": {"error": "near \"ID\": syntax error"}, "Currency": {"distinct_values": 1, "non_null_count": 879, "max_length": 3}, "Billing": {"min_value": 1.0, "max_value": 100.0, "non_null_count": 879}, "Actual": {"min_value": 0.84, "max_value": 98.35, "non_null_count": 879}, "Refund": {"min_value": 1.0, "max_value": 100.0, "non_null_count": 879}, "MDR": {"min_value": 0.0, "max_value": 0.22, "non_null_count": 879}, "GST": {"min_value": 0.0, "max_value": 0.0, "non_null_count": 879}, "Status": {"distinct_values": 2, "non_null_count": 879, "max_length": 9}, "Refund Fee": {"error": "near \"Fee\": syntax error"}, "Quantity": {"min_value": 0, "max_value": 1, "non_null_count": 879}, "Reference1": {"distinct_values": 77, "non_null_count": 879, "max_length": 31}, "Reference2": {"distinct_values": 142, "non_null_count": 441, "max_length": 31}, "PROCESS": {"distinct_values": 3, "non_null_count": 879, "max_length": 3}}}, "IOT_Sales": {"row_count": 376901, "column_stats": {"id": {"min_value": 1, "max_value": 91749, "non_null_count": 91499}, "Copartner_name": {"distinct_values": 1, "non_null_count": 374503, "max_length": 5}, "Order_No": {"distinct_values": 374516, "non_null_count": 374538, "max_length": 31}, "Order_types": {"distinct_values": 3, "non_null_count": 376220, "max_length": 13}, "Order_status": {"distinct_values": 1, "non_null_count": 376901, "max_length": 6}, "Order_price": {"min_value": 0.09, "max_value": 140.0, "non_null_count": 376901}, "Payment": {"min_value": 0.0, "max_value": 100.0, "non_null_count": 374514}, "Order_time": {"distinct_values": 104, "non_null_count": 376901, "max_length": 10}, "Equipment_ID": {"distinct_values": 3226, "non_null_count": 376901, "max_length": 23}, "Equipment_name": {"distinct_values": 649, "non_null_count": 375460, "max_length": 49}, "Branch_name": {"distinct_values": 47, "non_null_count": 375460, "max_length": 38}, "Payment_date": {"distinct_values": 87637, "non_null_count": 374445, "max_length": 19}, "User_name": {"distinct_values": 35477, "non_null_count": 44838, "max_length": 8}, "Time": {"distinct_values": 65990, "non_null_count": 372400, "max_length": 17}, "Matched_Order_ID": {"distinct_values": 64, "non_null_count": 64, "max_length": 31}, "OrderTime_dt": {"distinct_values": 310992, "non_null_count": 328148, "max_length": 19}, "Import_Date": {"distinct_values": 55, "non_null_count": 177991, "max_length": 10}, "Transaction_Num": {"distinct_values": 41220, "non_null_count": 41224, "max_length": 10}}}, "Equipment_ID_Anomalies": {"row_count": 0, "column_stats": {"ID": {"min_value": null, "max_value": null, "non_null_count": 0}, "Equipment_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Platform": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "File_Source": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Detection_Date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Notes": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}}}, "IOT_Sales_Refunding": {"row_count": 2502, "column_stats": {"id": {"min_value": null, "max_value": null, "non_null_count": 0}, "Copartner_name": {"distinct_values": 2, "non_null_count": 2502, "max_length": 5}, "Order_No": {"distinct_values": 2502, "non_null_count": 2502, "max_length": 31}, "Order_types": {"distinct_values": 2, "non_null_count": 2502, "max_length": 13}, "Order_status": {"distinct_values": 2, "non_null_count": 2502, "max_length": 9}, "Order_price": {"min_value": 1.0, "max_value": 20.0, "non_null_count": 2502}, "Payment": {"min_value": 1.0, "max_value": 20.0, "non_null_count": 2502}, "Order_time": {"distinct_values": 43, "non_null_count": 2502, "max_length": 10}, "Equipment_ID": {"distinct_values": 260, "non_null_count": 2502, "max_length": 9}, "Equipment_name": {"distinct_values": 180, "non_null_count": 2502, "max_length": 40}, "Branch_name": {"distinct_values": 18, "non_null_count": 2502, "max_length": 23}, "Payment_date": {"distinct_values": 43, "non_null_count": 2502, "max_length": 10}, "User_name": {"distinct_values": 5, "non_null_count": 2502, "max_length": 8}, "Time": {"distinct_values": 1, "non_null_count": 799, "max_length": 0}, "Matched_Order_ID": {"distinct_values": 1, "non_null_count": 799, "max_length": 0}, "OrderTime_dt": {"distinct_values": 1, "non_null_count": 799, "max_length": 0}, "Import_Date": {"distinct_values": 1, "non_null_count": 2502, "max_length": 10}, "Transaction_Num": {"distinct_values": 2502, "non_null_count": 2502, "max_length": 12}}}, "IOT_Sales_Close": {"row_count": 18606, "column_stats": {"id": {"min_value": null, "max_value": null, "non_null_count": 0}, "Copartner_name": {"distinct_values": 1, "non_null_count": 18606, "max_length": 5}, "Order_No": {"distinct_values": 18606, "non_null_count": 18606, "max_length": 31}, "Order_types": {"distinct_values": 1, "non_null_count": 18606, "max_length": 12}, "Order_status": {"distinct_values": 1, "non_null_count": 18606, "max_length": 5}, "Order_price": {"min_value": 3.0, "max_value": 30.0, "non_null_count": 18606}, "Payment": {"min_value": 0.0, "max_value": 30.0, "non_null_count": 18606}, "Order_time": {"distinct_values": 43, "non_null_count": 18606, "max_length": 10}, "Equipment_ID": {"distinct_values": 1093, "non_null_count": 18606, "max_length": 9}, "Equipment_name": {"distinct_values": 476, "non_null_count": 18606, "max_length": 49}, "Branch_name": {"distinct_values": 41, "non_null_count": 18606, "max_length": 38}, "Payment_date": {"distinct_values": 1, "non_null_count": 18606, "max_length": 4}, "User_name": {"distinct_values": 12453, "non_null_count": 18606, "max_length": 8}, "Time": {"distinct_values": 1, "non_null_count": 6437, "max_length": 0}, "Matched_Order_ID": {"distinct_values": 1, "non_null_count": 6437, "max_length": 0}, "OrderTime_dt": {"distinct_values": 1, "non_null_count": 6437, "max_length": 0}, "Import_Date": {"distinct_values": 1, "non_null_count": 18606, "max_length": 10}, "Transaction_Num": {"distinct_values": 1, "non_null_count": 18606, "max_length": 0}}}, "ZERO_Sales_Refunding": {"row_count": 72, "column_stats": {"id": {"min_value": 42421, "max_value": 42840, "non_null_count": 48}, "Copartner_name": {"distinct_values": 1, "non_null_count": 72, "max_length": 4}, "Order_No": {"distinct_values": 70, "non_null_count": 72, "max_length": 31}, "Order_types": {"distinct_values": 1, "non_null_count": 72, "max_length": 13}, "Order_status": {"distinct_values": 2, "non_null_count": 72, "max_length": 9}, "Order_price": {"min_value": 1.0, "max_value": 15.0, "non_null_count": 72}, "Payment": {"min_value": 1.0, "max_value": 15.0, "non_null_count": 72}, "Order_time": {"distinct_values": 9, "non_null_count": 72, "max_length": 10}, "Equipment_ID": {"distinct_values": 18, "non_null_count": 72, "max_length": 9}, "Equipment_name": {"distinct_values": 13, "non_null_count": 72, "max_length": 32}, "Branch_name": {"distinct_values": 9, "non_null_count": 72, "max_length": 13}, "Payment_date": {"distinct_values": 9, "non_null_count": 72, "max_length": 10}, "User_name": {"distinct_values": 1, "non_null_count": 20, "max_length": 0}, "Time": {"distinct_values": 53, "non_null_count": 72, "max_length": 8}, "Matched_Order_ID": {"distinct_values": 1, "non_null_count": 20, "max_length": 0}, "OrderTime_dt": {"distinct_values": 53, "non_null_count": 72, "max_length": 19}, "Import_Date": {"distinct_values": 2, "non_null_count": 68, "max_length": 10}, "Transaction_Num": {"distinct_values": 22, "non_null_count": 24, "max_length": 10}}}, "ZERO_Sales_Close": {"row_count": 0, "column_stats": {"id": {"min_value": null, "max_value": null, "non_null_count": 0}, "Copartner_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_No": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_types": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_status": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_price": {"min_value": null, "max_value": null, "non_null_count": 0}, "Payment": {"min_value": null, "max_value": null, "non_null_count": 0}, "Order_time": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Equipment_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Equipment_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Branch_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Payment_date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "User_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Time": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Matched_Order_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "OrderTime_dt": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Import_Date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Transaction_Num": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}}}, "APP_Sales_Refunding": {"row_count": 0, "column_stats": {"ID": {"min_value": null, "max_value": null, "non_null_count": 0}, "Copartner_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_No": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_types": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_status": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_price": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Payment": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_time": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Equipment_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Equipment_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Branch_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Payment_date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "User_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Time": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Matched_Order_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "OrderTime_dt": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Transaction_Num": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Import_Date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}}}, "APP_Sales_Close": {"row_count": 0, "column_stats": {"ID": {"min_value": null, "max_value": null, "non_null_count": 0}, "Copartner_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_No": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_types": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_status": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_price": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Payment": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Order_time": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Equipment_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Equipment_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Branch_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Payment_date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "User_name": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Time": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Matched_Order_ID": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "OrderTime_dt": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Transaction_Num": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}, "Import_Date": {"distinct_values": 0, "non_null_count": 0, "max_length": 0}}}}, "migration_notes": ["数据库迁移注意事项:", "1. PostgreSQL区分大小写，建议使用小写表名和列名", "2. PostgreSQL的自增列使用SERIAL类型", "3. 检查日期时间格式是否兼容", "4. 验证所有视图和索引是否正确创建", "5. 测试应用程序连接和查询功能", "6. 备份原始SQLite数据库", "7. 在生产环境部署前进行充分测试"]}