# -*- coding: utf-8 -*-
"""
智能数据迁移脚本
SQLite到PostgreSQL的全量对比迁移
支持Equipment_ID精确匹配、智能检测、完整错误处理和自动回滚
"""

import os
import sys
import sqlite3
import pandas as pd
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from contextlib import contextmanager

# 可选依赖
try:
    import psycopg2
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from postgresql_manager import PostgreSQLManager
try:
    from utils.logger import get_logger
except ImportError:
    # 简化的日志器
    def get_logger(name):
        import logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        return logging.getLogger(name)

@dataclass
class MigrationStats:
    """迁移统计信息"""
    table_name: str
    total_sqlite_records: int = 0
    total_postgresql_records: int = 0
    new_records: int = 0
    updated_records: int = 0
    unchanged_records: int = 0
    conflicts: int = 0
    errors: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def duration(self) -> Optional[float]:
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

@dataclass
class ConflictRecord:
    """冲突记录信息"""
    equipment_id: str
    sqlite_record: Dict[str, Any]
    postgresql_records: List[Dict[str, Any]]
    conflict_type: str  # 'duplicate', 'mismatch'

class IntelligentMigrator:
    """智能迁移器"""
    
    def __init__(self, sqlite_db_path: str, base_dir: str = None):
        self.sqlite_db_path = sqlite_db_path
        self.base_dir = Path(base_dir) if base_dir else Path(os.path.dirname(os.path.abspath(__file__)))
        
        # 初始化PostgreSQL管理器
        self.pg_manager = PostgreSQLManager(str(self.base_dir))
        
        # 初始化日志
        self.logger = get_logger('intelligent_migration')
        
        # 迁移配置
        self.migration_config = self.load_migration_config()
        
        # 统计信息
        self.stats: Dict[str, MigrationStats] = {}
        self.conflicts: List[ConflictRecord] = []
        
        # 支持的表列表
        self.supported_tables = [
            'IOT_Sales', 'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales', 'ZERO_Sales_Refunding', 'ZERO_Sales_Close',
            'APP_Sales', 'APP_Sales_Refunding', 'APP_Sales_Close',
            'Combined_Sales'
        ]
    
    def load_migration_config(self) -> Dict[str, Any]:
        """加载迁移配置"""
        config_file = self.base_dir / "migration_config.json"
        
        default_config = {
            "batch_size": 1000,
            "conflict_resolution": "overwrite",  # overwrite, skip, manual
            "backup_before_migration": True,
            "verify_after_migration": True,
            "log_level": "INFO",
            "excluded_columns": ["id"],  # 排除的列
            "key_column": "Equipment_ID"  # 主键列
        }
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        
        return default_config
    
    def save_migration_config(self):
        """保存迁移配置"""
        config_file = self.base_dir / "migration_config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.migration_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    @contextmanager
    def get_sqlite_connection(self):
        """获取SQLite连接（上下文管理器）"""
        conn = None
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            self.logger.error(f"SQLite连接失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    @contextmanager
    def get_postgresql_connection(self):
        """获取PostgreSQL连接（上下文管理器）"""
        if not PSYCOPG2_AVAILABLE:
            raise Exception("psycopg2未安装，无法连接PostgreSQL。请运行: pip install psycopg2")

        conn = None
        try:
            conn = self.pg_manager.get_connection()
            conn.autocommit = False  # 手动控制事务
            yield conn
        except Exception as e:
            self.logger.error(f"PostgreSQL连接失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def get_table_list(self, selected_tables: List[str] = None) -> List[str]:
        """获取要迁移的表列表"""
        if selected_tables:
            # 验证选择的表是否存在
            valid_tables = []
            with self.get_sqlite_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = {row[0] for row in cursor.fetchall()}
                
                for table in selected_tables:
                    if table in existing_tables and table in self.supported_tables:
                        valid_tables.append(table)
                    else:
                        self.logger.warning(f"表 {table} 不存在或不支持，跳过")
            
            return valid_tables
        else:
            # 返回所有支持的表
            return self.supported_tables
    
    def get_table_schema(self, table_name: str, connection_type: str) -> Dict[str, str]:
        """获取表结构"""
        schema = {}
        
        try:
            if connection_type == 'sqlite':
                with self.get_sqlite_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    for col in columns:
                        schema[col[1]] = col[2]  # column_name: data_type
            
            elif connection_type == 'postgresql':
                with self.get_postgresql_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT column_name, data_type 
                        FROM information_schema.columns 
                        WHERE table_name = %s
                        ORDER BY ordinal_position
                    """, (table_name,))
                    columns = cursor.fetchall()
                    for col in columns:
                        schema[col[0]] = col[1]  # column_name: data_type
            
        except Exception as e:
            self.logger.error(f"获取表结构失败 {table_name} ({connection_type}): {e}")
        
        return schema
    
    def ensure_postgresql_table_exists(self, table_name: str, sqlite_schema: Dict[str, str]) -> bool:
        """确保PostgreSQL表存在，如果不存在则创建"""
        try:
            with self.get_postgresql_connection() as conn:
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    )
                """, (table_name,))
                
                table_exists = cursor.fetchone()[0]
                
                if not table_exists:
                    self.logger.info(f"创建PostgreSQL表: {table_name}")
                    
                    # 创建表结构
                    columns_ddl = []
                    for col_name, col_type in sqlite_schema.items():
                        # SQLite到PostgreSQL类型映射
                        pg_type = self.map_sqlite_to_postgresql_type(col_type)
                        columns_ddl.append(f'"{col_name}" {pg_type}')
                    
                    create_sql = f'CREATE TABLE "{table_name}" ({", ".join(columns_ddl)})'
                    cursor.execute(create_sql)
                    conn.commit()
                    
                    self.logger.info(f"表 {table_name} 创建成功")
                
                return True
                
        except Exception as e:
            self.logger.error(f"确保表存在失败 {table_name}: {e}")
            return False
    
    def map_sqlite_to_postgresql_type(self, sqlite_type: str) -> str:
        """SQLite类型到PostgreSQL类型的映射"""
        type_mapping = {
            'INTEGER': 'INTEGER',
            'REAL': 'REAL',
            'TEXT': 'TEXT',
            'BLOB': 'BYTEA',
            'NUMERIC': 'NUMERIC',
            'VARCHAR': 'VARCHAR',
            'DATETIME': 'TIMESTAMP',
            'DATE': 'DATE',
            'TIME': 'TIME'
        }
        
        sqlite_type_upper = sqlite_type.upper()
        
        # 处理带长度的类型
        for sqlite_key, pg_type in type_mapping.items():
            if sqlite_type_upper.startswith(sqlite_key):
                return pg_type
        
        # 默认使用TEXT
        return 'TEXT'
    
    def load_table_data(self, table_name: str, connection_type: str) -> pd.DataFrame:
        """加载表数据"""
        try:
            if connection_type == 'sqlite':
                with self.get_sqlite_connection() as conn:
                    df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
            
            elif connection_type == 'postgresql':
                with self.get_postgresql_connection() as conn:
                    df = pd.read_sql_query(f'SELECT * FROM "{table_name}"', conn)
            
            self.logger.info(f"加载 {connection_type} 表 {table_name}: {len(df)} 条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"加载表数据失败 {table_name} ({connection_type}): {e}")
            return pd.DataFrame()
    
    def compare_records(self, sqlite_df: pd.DataFrame, postgresql_df: pd.DataFrame, 
                       table_name: str) -> Tuple[pd.DataFrame, pd.DataFrame, List[ConflictRecord]]:
        """
        对比记录，返回需要插入和更新的数据
        
        Returns:
            (new_records_df, update_records_df, conflicts)
        """
        key_column = self.migration_config['key_column']
        excluded_columns = self.migration_config['excluded_columns']
        
        # 确保关键列存在
        if key_column not in sqlite_df.columns:
            self.logger.error(f"表 {table_name} 缺少关键列 {key_column}")
            return pd.DataFrame(), pd.DataFrame(), []
        
        new_records = []
        update_records = []
        conflicts = []
        
        # 创建PostgreSQL记录的索引（按Equipment_ID分组）
        pg_index = {}
        if not postgresql_df.empty and key_column in postgresql_df.columns:
            for idx, row in postgresql_df.iterrows():
                equipment_id = row[key_column]
                if equipment_id not in pg_index:
                    pg_index[equipment_id] = []
                pg_index[equipment_id].append(row.to_dict())
        
        # 对比每条SQLite记录
        for idx, sqlite_row in sqlite_df.iterrows():
            equipment_id = sqlite_row[key_column]
            
            if equipment_id not in pg_index:
                # 新记录
                new_records.append(sqlite_row.to_dict())
            else:
                pg_records = pg_index[equipment_id]
                
                if len(pg_records) == 1:
                    # 一对一匹配，检查是否需要更新
                    pg_record = pg_records[0]
                    
                    # 比较记录（排除指定列）
                    needs_update = False
                    for col in sqlite_row.index:
                        if col not in excluded_columns:
                            if col in pg_record and str(sqlite_row[col]) != str(pg_record[col]):
                                needs_update = True
                                break
                    
                    if needs_update:
                        # 需要更新，根据配置决定处理方式
                        if self.migration_config['conflict_resolution'] == 'overwrite':
                            update_records.append(sqlite_row.to_dict())
                        else:
                            conflicts.append(ConflictRecord(
                                equipment_id=equipment_id,
                                sqlite_record=sqlite_row.to_dict(),
                                postgresql_records=pg_records,
                                conflict_type='mismatch'
                            ))
                else:
                    # 多重匹配冲突
                    conflicts.append(ConflictRecord(
                        equipment_id=equipment_id,
                        sqlite_record=sqlite_row.to_dict(),
                        postgresql_records=pg_records,
                        conflict_type='duplicate'
                    ))
        
        new_df = pd.DataFrame(new_records) if new_records else pd.DataFrame()
        update_df = pd.DataFrame(update_records) if update_records else pd.DataFrame()
        
        self.logger.info(f"表 {table_name} 对比结果: 新增 {len(new_df)}, 更新 {len(update_df)}, 冲突 {len(conflicts)}")

        return new_df, update_df, conflicts

    def execute_migration(self, table_name: str, new_records_df: pd.DataFrame,
                         update_records_df: pd.DataFrame) -> bool:
        """执行迁移操作"""
        try:
            with self.get_postgresql_connection() as conn:
                cursor = conn.cursor()

                # 开始事务
                cursor.execute("BEGIN")

                success_count = 0
                error_count = 0

                # 插入新记录
                if not new_records_df.empty:
                    self.logger.info(f"插入 {len(new_records_df)} 条新记录到表 {table_name}")

                    for idx, row in new_records_df.iterrows():
                        try:
                            columns = list(row.index)
                            values = [row[col] for col in columns]

                            # 构建插入SQL
                            quoted_columns = [f'"{col}"' for col in columns]
                            placeholders = ', '.join(['%s'] * len(columns))
                            insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'

                            cursor.execute(insert_sql, values)
                            success_count += 1

                        except Exception as e:
                            self.logger.error(f"插入记录失败 {table_name}[{idx}]: {e}")
                            error_count += 1

                # 更新记录
                if not update_records_df.empty:
                    self.logger.info(f"更新 {len(update_records_df)} 条记录到表 {table_name}")

                    key_column = self.migration_config['key_column']
                    excluded_columns = self.migration_config['excluded_columns']

                    for idx, row in update_records_df.iterrows():
                        try:
                            # 构建更新SQL
                            update_columns = [col for col in row.index if col not in excluded_columns and col != key_column]

                            if update_columns:
                                set_clause = ', '.join([f'"{col}" = %s' for col in update_columns])
                                update_sql = f'UPDATE "{table_name}" SET {set_clause} WHERE "{key_column}" = %s'

                                values = [row[col] for col in update_columns] + [row[key_column]]
                                cursor.execute(update_sql, values)
                                success_count += 1

                        except Exception as e:
                            self.logger.error(f"更新记录失败 {table_name}[{idx}]: {e}")
                            error_count += 1

                # 提交事务
                if error_count == 0:
                    cursor.execute("COMMIT")
                    self.logger.info(f"表 {table_name} 迁移成功: {success_count} 条记录")
                    return True
                else:
                    cursor.execute("ROLLBACK")
                    self.logger.error(f"表 {table_name} 迁移失败: {error_count} 个错误，已回滚")
                    return False

        except Exception as e:
            self.logger.error(f"执行迁移失败 {table_name}: {e}")
            return False

    def verify_migration(self, table_name: str) -> bool:
        """验证迁移结果"""
        try:
            # 重新加载数据进行验证
            sqlite_df = self.load_table_data(table_name, 'sqlite')
            postgresql_df = self.load_table_data(table_name, 'postgresql')

            key_column = self.migration_config['key_column']

            if key_column not in sqlite_df.columns or key_column not in postgresql_df.columns:
                self.logger.warning(f"验证跳过 {table_name}: 缺少关键列 {key_column}")
                return True

            # 统计Equipment_ID数量
            sqlite_equipment_ids = set(sqlite_df[key_column].dropna())
            postgresql_equipment_ids = set(postgresql_df[key_column].dropna())

            missing_ids = sqlite_equipment_ids - postgresql_equipment_ids
            extra_ids = postgresql_equipment_ids - sqlite_equipment_ids

            if missing_ids:
                self.logger.warning(f"表 {table_name} 验证: 缺少 {len(missing_ids)} 个Equipment_ID")
                for equipment_id in list(missing_ids)[:5]:  # 只显示前5个
                    self.logger.warning(f"  缺少: {equipment_id}")

            if extra_ids:
                self.logger.info(f"表 {table_name} 验证: PostgreSQL额外有 {len(extra_ids)} 个Equipment_ID")

            # 计算匹配率
            total_sqlite = len(sqlite_equipment_ids)
            matched = len(sqlite_equipment_ids & postgresql_equipment_ids)
            match_rate = (matched / total_sqlite * 100) if total_sqlite > 0 else 100

            self.logger.info(f"表 {table_name} 验证结果: 匹配率 {match_rate:.2f}% ({matched}/{total_sqlite})")

            return match_rate >= 95.0  # 95%以上认为验证通过

        except Exception as e:
            self.logger.error(f"验证迁移失败 {table_name}: {e}")
            return False

    def migrate_table(self, table_name: str) -> bool:
        """迁移单个表"""
        self.logger.info(f"开始迁移表: {table_name}")

        # 初始化统计信息
        stats = MigrationStats(table_name=table_name, start_time=datetime.now())
        self.stats[table_name] = stats

        try:
            # 1. 获取表结构
            sqlite_schema = self.get_table_schema(table_name, 'sqlite')
            if not sqlite_schema:
                self.logger.error(f"无法获取SQLite表结构: {table_name}")
                return False

            # 2. 确保PostgreSQL表存在
            if not self.ensure_postgresql_table_exists(table_name, sqlite_schema):
                return False

            # 3. 加载数据
            sqlite_df = self.load_table_data(table_name, 'sqlite')
            postgresql_df = self.load_table_data(table_name, 'postgresql')

            stats.total_sqlite_records = len(sqlite_df)
            stats.total_postgresql_records = len(postgresql_df)

            if sqlite_df.empty:
                self.logger.info(f"表 {table_name} 无数据，跳过迁移")
                return True

            # 4. 对比数据
            new_df, update_df, conflicts = self.compare_records(sqlite_df, postgresql_df, table_name)

            stats.new_records = len(new_df)
            stats.updated_records = len(update_df)
            stats.conflicts = len(conflicts)
            stats.unchanged_records = stats.total_sqlite_records - stats.new_records - stats.updated_records - stats.conflicts

            # 记录冲突
            self.conflicts.extend(conflicts)

            # 5. 执行迁移
            if not new_df.empty or not update_df.empty:
                if not self.execute_migration(table_name, new_df, update_df):
                    stats.errors += 1
                    return False

            # 6. 验证迁移（如果配置启用）
            if self.migration_config.get('verify_after_migration', True):
                if not self.verify_migration(table_name):
                    self.logger.warning(f"表 {table_name} 验证失败，但迁移已完成")

            stats.end_time = datetime.now()
            self.logger.info(f"表 {table_name} 迁移完成，耗时 {stats.duration:.2f} 秒")
            return True

        except Exception as e:
            self.logger.error(f"迁移表失败 {table_name}: {e}")
            stats.errors += 1
            stats.end_time = datetime.now()
            return False

    def migrate_all_tables(self, selected_tables: List[str] = None) -> bool:
        """迁移所有选定的表"""
        self.logger.info("开始智能数据迁移")

        # 检查PostgreSQL状态
        if not self.pg_manager.is_installed():
            self.logger.error("PostgreSQL未安装，请先运行 postgresql_setup.py")
            return False

        if not self.pg_manager.is_running():
            self.logger.error("PostgreSQL未运行，请先启动服务")
            return False

        if not self.pg_manager.test_connection():
            self.logger.error("PostgreSQL连接测试失败")
            return False

        # 获取要迁移的表列表
        tables_to_migrate = self.get_table_list(selected_tables)
        if not tables_to_migrate:
            self.logger.error("没有找到要迁移的表")
            return False

        self.logger.info(f"计划迁移 {len(tables_to_migrate)} 个表: {', '.join(tables_to_migrate)}")

        # 备份PostgreSQL（如果配置启用）
        backup_file = None
        if self.migration_config.get('backup_before_migration', True):
            self.logger.info("开始备份PostgreSQL数据库...")
            backup_file = self.pg_manager.backup_database(f"migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql")
            if backup_file:
                self.logger.info(f"备份完成: {backup_file}")
            else:
                self.logger.warning("备份失败，但继续迁移")

        # 执行迁移
        successful_tables = []
        failed_tables = []

        for table_name in tables_to_migrate:
            try:
                if self.migrate_table(table_name):
                    successful_tables.append(table_name)
                else:
                    failed_tables.append(table_name)
            except Exception as e:
                self.logger.error(f"迁移表异常 {table_name}: {e}")
                failed_tables.append(table_name)

        # 生成迁移报告
        self.generate_migration_report()

        # 总结
        total_tables = len(tables_to_migrate)
        success_count = len(successful_tables)

        self.logger.info(f"迁移完成: {success_count}/{total_tables} 个表成功")

        if failed_tables:
            self.logger.error(f"失败的表: {', '.join(failed_tables)}")

        if self.conflicts:
            self.logger.warning(f"发现 {len(self.conflicts)} 个冲突，请查看迁移报告")

        return len(failed_tables) == 0

    def generate_migration_report(self):
        """生成迁移报告"""
        report_file = self.base_dir / "migration_logs" / f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(parents=True, exist_ok=True)

        # 准备报告数据
        report_data = {
            "migration_time": datetime.now().isoformat(),
            "sqlite_database": self.sqlite_db_path,
            "postgresql_config": self.pg_manager.config,
            "migration_config": self.migration_config,
            "summary": {
                "total_tables": len(self.stats),
                "successful_tables": len([s for s in self.stats.values() if s.errors == 0]),
                "failed_tables": len([s for s in self.stats.values() if s.errors > 0]),
                "total_conflicts": len(self.conflicts),
                "total_new_records": sum(s.new_records for s in self.stats.values()),
                "total_updated_records": sum(s.updated_records for s in self.stats.values()),
                "total_unchanged_records": sum(s.unchanged_records for s in self.stats.values())
            },
            "table_stats": {},
            "conflicts": []
        }

        # 添加表统计
        for table_name, stats in self.stats.items():
            report_data["table_stats"][table_name] = {
                "total_sqlite_records": stats.total_sqlite_records,
                "total_postgresql_records": stats.total_postgresql_records,
                "new_records": stats.new_records,
                "updated_records": stats.updated_records,
                "unchanged_records": stats.unchanged_records,
                "conflicts": stats.conflicts,
                "errors": stats.errors,
                "duration": stats.duration,
                "start_time": stats.start_time.isoformat() if stats.start_time else None,
                "end_time": stats.end_time.isoformat() if stats.end_time else None
            }

        # 添加冲突详情
        for conflict in self.conflicts:
            report_data["conflicts"].append({
                "equipment_id": conflict.equipment_id,
                "conflict_type": conflict.conflict_type,
                "sqlite_record": conflict.sqlite_record,
                "postgresql_records": conflict.postgresql_records
            })

        # 保存报告
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"迁移报告已生成: {report_file}")

            # 生成简化的文本报告
            self.generate_text_report(report_data, report_file.with_suffix('.txt'))

        except Exception as e:
            self.logger.error(f"生成迁移报告失败: {e}")

    def generate_text_report(self, report_data: Dict[str, Any], report_file: Path):
        """生成文本格式的迁移报告"""
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("智能数据迁移报告\n")
                f.write("=" * 80 + "\n\n")

                # 基本信息
                f.write(f"迁移时间: {report_data['migration_time']}\n")
                f.write(f"源数据库: {report_data['sqlite_database']}\n")
                f.write(f"目标数据库: PostgreSQL ({report_data['postgresql_config']['host']}:{report_data['postgresql_config']['port']})\n\n")

                # 总体统计
                summary = report_data['summary']
                f.write("总体统计:\n")
                f.write("-" * 40 + "\n")
                f.write(f"总表数: {summary['total_tables']}\n")
                f.write(f"成功表数: {summary['successful_tables']}\n")
                f.write(f"失败表数: {summary['failed_tables']}\n")
                f.write(f"总冲突数: {summary['total_conflicts']}\n")
                f.write(f"新增记录: {summary['total_new_records']}\n")
                f.write(f"更新记录: {summary['total_updated_records']}\n")
                f.write(f"未变记录: {summary['total_unchanged_records']}\n\n")

                # 表详情
                f.write("表迁移详情:\n")
                f.write("-" * 40 + "\n")
                for table_name, stats in report_data['table_stats'].items():
                    f.write(f"\n表: {table_name}\n")
                    f.write(f"  SQLite记录数: {stats['total_sqlite_records']}\n")
                    f.write(f"  PostgreSQL记录数: {stats['total_postgresql_records']}\n")
                    f.write(f"  新增: {stats['new_records']}\n")
                    f.write(f"  更新: {stats['updated_records']}\n")
                    f.write(f"  未变: {stats['unchanged_records']}\n")
                    f.write(f"  冲突: {stats['conflicts']}\n")
                    f.write(f"  错误: {stats['errors']}\n")
                    if stats['duration']:
                        f.write(f"  耗时: {stats['duration']:.2f} 秒\n")

                # 冲突详情
                if report_data['conflicts']:
                    f.write("\n冲突详情:\n")
                    f.write("-" * 40 + "\n")
                    for i, conflict in enumerate(report_data['conflicts'][:10], 1):  # 只显示前10个
                        f.write(f"\n冲突 {i}:\n")
                        f.write(f"  Equipment_ID: {conflict['equipment_id']}\n")
                        f.write(f"  冲突类型: {conflict['conflict_type']}\n")
                        f.write(f"  PostgreSQL记录数: {len(conflict['postgresql_records'])}\n")

                    if len(report_data['conflicts']) > 10:
                        f.write(f"\n... 还有 {len(report_data['conflicts']) - 10} 个冲突，详见JSON报告\n")

            self.logger.info(f"文本报告已生成: {report_file}")

        except Exception as e:
            self.logger.error(f"生成文本报告失败: {e}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='智能数据迁移工具')
    parser.add_argument('--sqlite-db', required=True, help='SQLite数据库路径')
    parser.add_argument('--tables', nargs='*', help='要迁移的表名列表（不指定则迁移所有支持的表）')
    parser.add_argument('--config', help='迁移配置文件路径')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式（只分析不执行）')
    parser.add_argument('--force', action='store_true', help='强制执行（跳过确认）')

    args = parser.parse_args()

    # 检查SQLite数据库文件
    if not os.path.exists(args.sqlite_db):
        print(f"❌ SQLite数据库文件不存在: {args.sqlite_db}")
        return 1

    # 创建迁移器
    migrator = IntelligentMigrator(args.sqlite_db)

    # 加载自定义配置
    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                custom_config = json.load(f)
            migrator.migration_config.update(custom_config)
            print(f"✅ 已加载自定义配置: {args.config}")
        except Exception as e:
            print(f"⚠️ 加载配置文件失败: {e}")

    # 显示配置信息
    print("📋 迁移配置:")
    for key, value in migrator.migration_config.items():
        print(f"  {key}: {value}")

    # 获取要迁移的表
    tables_to_migrate = migrator.get_table_list(args.tables)
    if not tables_to_migrate:
        print("❌ 没有找到要迁移的表")
        return 1

    print(f"\n📊 计划迁移 {len(tables_to_migrate)} 个表:")
    for table in tables_to_migrate:
        print(f"  - {table}")

    # 试运行模式
    if args.dry_run:
        print("\n🔍 试运行模式 - 只分析不执行迁移")
        # 这里可以添加试运行逻辑
        return 0

    # 确认执行
    if not args.force:
        response = input("\n❓ 确认执行迁移？(y/N): ")
        if response.lower() != 'y':
            print("❌ 迁移已取消")
            return 0

    # 执行迁移
    print("\n🚀 开始执行迁移...")
    success = migrator.migrate_all_tables(args.tables)

    if success:
        print("\n✅ 迁移完成！")
        return 0
    else:
        print("\n❌ 迁移失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
