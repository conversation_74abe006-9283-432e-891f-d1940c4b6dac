# -*- coding: utf-8 -*-
"""
测试脚本执行
验证脚本是否能正常响应参数
"""

import subprocess
import sys
import os

def test_script_execution():
    """测试脚本执行"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 测试1: 不带参数运行（应该显示用法说明）
    print("🧪 测试1: 不带参数运行")
    try:
        result = subprocess.run([
            sys.executable, script_path
        ], capture_output=True, text=True, timeout=10, encoding='utf-8')
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时（可能卡住了）")
    except Exception as e:
        print(f"❌ 执行错误: {e}")
    
    print("\n" + "="*50)
    
    # 测试2: 带参数运行（应该尝试处理文件）
    print("🧪 测试2: 带参数运行（模拟文件路径）")
    try:
        result = subprocess.run([
            sys.executable, script_path,
            "--file1", "test1.xlsx",
            "--file2", "test2.xlsx", 
            "--sheet_name", "test_sheet"
        ], capture_output=True, text=True, timeout=10, encoding='utf-8')
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时（可能卡住了）")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

if __name__ == "__main__":
    print("开始测试脚本执行...")
    test_script_execution()
    print("\n🎉 测试完成！")
    print("\n如果测试1显示用法说明，测试2尝试处理文件（即使失败），说明脚本响应正常。")
    print("如果出现超时，说明脚本仍然卡住，需要进一步修复。")
