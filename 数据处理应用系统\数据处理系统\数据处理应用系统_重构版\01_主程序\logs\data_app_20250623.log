2025-06-23 08:59:58,039 - INFO - 双数据库模块加载成功
2025-06-23 08:59:58,234 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-23 08:59:58,234 - INFO - [general] 请使用浏览按钮选择文件
2025-06-23 08:59:58,234 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-23 08:59:58,750 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-23 08:59:58,753 - INFO - [general] 请使用浏览按钮选择文件
2025-06-23 08:59:58,754 - INFO - [import] 请使用浏览按钮选择文件
2025-06-23 08:59:58,866 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-23 08:59:58,867 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-23 08:59:58,886 - INFO - [general] 数据库设置已加载
2025-06-23 08:59:58,886 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-23 08:59:58,886 - INFO - [settings] 数据库设置已加载
2025-06-23 08:59:58,886 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-23 08:59:59,888 - INFO - [database_config] 数据库状态已刷新
2025-06-23 08:59:59,895 - INFO - 数据处理与导入应用已启动
2025-06-23 08:59:59,996 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-23 09:00:05,334 - INFO - [import] 已选择1个导入文件: 160625 CHINA ZERO.xlsx
2025-06-23 09:00:05,336 - INFO - [import] 自动识别为ZERO平台
2025-06-23 09:00:11,442 - INFO - [import] 选择的目标数据库: SQLite
2025-06-23 09:00:11,442 - INFO - [import] 开始导入数据，平台类型: ZERO, 订单类型: all
2025-06-23 09:00:11,443 - INFO - [import] 目标数据库: SQLite
2025-06-23 09:00:11,443 - INFO - [import] 正在备份数据库...
2025-06-23 09:00:11,551 - INFO - SQLite数据库备份成功: sqlite_backup_20250623_090011.db
2025-06-23 09:00:11,552 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250623_090011.db
2025-06-23 09:00:11,552 - INFO - [import] 处理文件: 160625 CHINA ZERO.xlsx
2025-06-23 09:00:11,552 - INFO - [import] 数据导入脚本配置错误: data_import_script_optimized脚本不存在: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\scripts/data_import_optimized.py
请检查脚本文件路径配置。
2025-06-23 09:00:11,553 - INFO - [import] 文件 160625 CHINA ZERO.xlsx 导入失败
2025-06-23 09:00:11,553 - INFO - [import] 部分或全部文件导入失败
2025-06-23 09:00:11,553 - INFO - [import] 检测到导入失败，可以恢复到备份状态
2025-06-23 09:00:13,154 - INFO - [import] 恢复数据库失败: 文件不存在: C:\Users\<USER>\Desktop\Day Report\database\backups\sqlite_backup_20250623_090011.db
请检查文件路径是否正确。
2025-06-23 09:00:13,154 - INFO - [import] 数据库恢复失败
2025-06-23 09:11:31,677 - INFO - 双数据库模块加载成功
2025-06-23 09:11:31,846 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-23 09:11:31,846 - INFO - [general] 请使用浏览按钮选择文件
2025-06-23 09:11:31,846 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-23 09:11:32,178 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-23 09:11:32,186 - INFO - [general] 请使用浏览按钮选择文件
2025-06-23 09:11:32,186 - INFO - [import] 请使用浏览按钮选择文件
2025-06-23 09:11:32,302 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-23 09:11:32,302 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-23 09:11:32,328 - INFO - [general] 数据库设置已加载
2025-06-23 09:11:32,328 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-23 09:11:32,328 - INFO - [settings] 数据库设置已加载
2025-06-23 09:11:32,328 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-23 09:11:32,592 - INFO - [database_config] 数据库状态已刷新
2025-06-23 09:11:32,597 - INFO - 数据处理与导入应用已启动
2025-06-23 09:11:32,708 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-23 09:11:40,269 - INFO - [import] 已选择1个导入文件: 160625 CHINA ZERO.xlsx
2025-06-23 09:11:40,269 - INFO - [import] 自动识别为ZERO平台
2025-06-23 09:11:41,264 - INFO - [import] 选择的目标数据库: SQLite
2025-06-23 09:11:41,265 - INFO - [import] 开始导入数据，平台类型: ZERO, 订单类型: all
2025-06-23 09:11:41,265 - INFO - [import] 目标数据库: SQLite
2025-06-23 09:11:41,266 - INFO - [import] 正在备份数据库...
2025-06-23 09:11:41,341 - INFO - SQLite数据库备份成功: sqlite_backup_20250623_091141.db
2025-06-23 09:11:41,341 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250623_091141.db
2025-06-23 09:11:41,341 - INFO - [import] 处理文件: 160625 CHINA ZERO.xlsx
2025-06-23 09:11:41,341 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据导入脚本.py --file C:/Users/<USER>/Desktop/June/ZERO/160625 CHINA ZERO.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform ZERO
2025-06-23 09:11:42,815 - INFO - [import] ℹ️ 导入 - 数据库表结构已创建或已存在
2025-06-23 09:11:42,815 - INFO - [import] ℹ️ 导入 - 数据库初始化完成
2025-06-23 09:11:42,815 - INFO - [import] ℹ️ 导入 - 开始处理单个文件: C:/Users/<USER>/Desktop/June/ZERO/160625 CHINA ZERO.xlsx, 平台: ZERO
2025-06-23 09:11:42,815 - INFO - [import] ℹ️ 导入 - 文件 160625 CHINA ZERO.xlsx 总数据: 48，完全重复的: 0，部分字段不同的: 0，新增: 48
2025-06-23 09:11:42,816 - INFO - [import] ℹ️ 导入 - 文件 160625 CHINA ZERO.xlsx 涉及日期: ['2025-06-16']
2025-06-23 09:11:42,816 - INFO - [import] ℹ️ 导入 - 实际写入数据库 48 条
2025-06-23 09:11:42,816 - INFO - [import] ℹ️ 导入 - 文件 160625 CHINA ZERO.xlsx 导入成功
2025-06-23 09:11:42,816 - INFO - [import] 数据导入成功，返回码: 0
2025-06-23 09:11:42,816 - INFO - [import] 文件 160625 CHINA ZERO.xlsx 导入成功
2025-06-23 09:11:42,817 - INFO - [import] 所有文件导入成功
2025-06-23 09:11:52,561 - INFO - [import] 已清空文件选择
2025-06-23 09:26:22,789 - INFO - 双数据库模块加载成功
2025-06-23 09:26:23,000 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-23 09:26:23,000 - INFO - [general] 请使用浏览按钮选择文件
2025-06-23 09:26:23,001 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-23 09:26:23,414 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-23 09:26:23,421 - INFO - [general] 请使用浏览按钮选择文件
2025-06-23 09:26:23,421 - INFO - [import] 请使用浏览按钮选择文件
2025-06-23 09:26:23,568 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-23 09:26:23,568 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-23 09:26:23,597 - INFO - [general] 数据库设置已加载
2025-06-23 09:26:23,597 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-23 09:26:23,597 - INFO - [settings] 数据库设置已加载
2025-06-23 09:26:23,597 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-23 09:26:24,044 - INFO - [database_config] 数据库状态已刷新
2025-06-23 09:26:24,051 - INFO - 数据处理与导入应用已启动
2025-06-23 09:26:24,177 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-23 09:26:30,956 - INFO - [import] 已选择1个导入文件: 160625 CHINA IOT.xlsx
2025-06-23 09:26:30,957 - INFO - [import] 自动识别为IOT平台
2025-06-23 09:26:34,193 - INFO - [import] 选择的目标数据库: SQLite
2025-06-23 09:26:34,194 - INFO - [import] 开始导入数据，平台类型: IOT, 订单类型: all
2025-06-23 09:26:34,194 - INFO - [import] 目标数据库: SQLite
2025-06-23 09:26:34,195 - INFO - [import] 正在备份数据库...
2025-06-23 09:26:34,281 - INFO - SQLite数据库备份成功: sqlite_backup_20250623_092634.db
2025-06-23 09:26:34,282 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250623_092634.db
2025-06-23 09:26:34,282 - INFO - [import] 处理文件: 160625 CHINA IOT.xlsx
2025-06-23 09:26:34,282 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\../scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/IOT/160625 CHINA IOT.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform IOT
2025-06-23 09:26:38,190 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-23 09:26:38,190 - INFO - [import] 📤 导入脚本: File: 160625 CHINA IOT.xlsx
2025-06-23 09:26:38,190 - INFO - [import] 📤 导入脚本: Platform: IOT
2025-06-23 09:26:38,190 - INFO - [import] 📤 导入脚本: Total rows: 3729
2025-06-23 09:26:38,190 - INFO - [import] 📤 导入脚本: New data: 3729
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本: Actually inserted: 3729
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: 444 records
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本:   - IOT_Sales: 3139 records
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: 146 records
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: inserted 444 records
2025-06-23 09:26:38,191 - INFO - [import] 📤 导入脚本:   - IOT_Sales: inserted 3053 records
2025-06-23 09:26:38,192 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: inserted 146 records
2025-06-23 09:26:38,192 - INFO - [import] 📤 导入脚本:   - APP_Sales: inserted 86 records
2025-06-23 09:26:38,192 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-23 09:26:38,192 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-23 09:26:38,192 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-23 09:26:38,192 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-23 09:26:38,192 - INFO - [import] 数据导入成功，返回码: 0
2025-06-23 09:26:38,193 - INFO - [import] 文件 160625 CHINA IOT.xlsx 导入成功
2025-06-23 09:26:38,193 - INFO - [import] 所有文件导入成功
