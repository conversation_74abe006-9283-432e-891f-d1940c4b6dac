# -*- coding: utf-8 -*-
"""
完整代码逻辑检查
验证所有修复是否正确，确保与用户数据库结构完全匹配
"""

import sys
import os
import pandas as pd

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_column_mapping():
    """检查列名映射逻辑"""
    print("=== 检查列名映射逻辑 ===")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 用户Excel文件的列名
        user_excel_columns = [
            'Copartner name', 'Order No.', 'Transaction Num', 'Order types', 
            'Order status', 'Order price', 'Payment', 'Order time', 
            'Equipment ID', 'Equipment name', 'Branch name', 
            'Payment date', 'User name'
        ]
        
        # 用户数据库表的列名（从截图获得）
        user_db_columns = [
            'id', 'Copartner_name', 'Order_No', 'Order_types', 'Order_status', 
            'Order_price', 'Payment', 'Order_time', 'Equipment_ID', 
            'Equipment_name', 'Branch_name', 'Payment_date', 'User_name', 
            'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date', 
            'Transaction_Num'
        ]
        
        # 创建测试DataFrame
        test_data = {}
        for col in user_excel_columns:
            test_data[col] = ['test_value'] * 3
        
        df = pd.DataFrame(test_data)
        print(f"原始Excel列名: {list(df.columns)}")
        
        # 测试列名标准化
        df_standardized = processor._standardize_column_names(df)
        print(f"标准化后列名: {list(df_standardized.columns)}")
        
        # 检查是否有Transaction_ID列（应该没有）
        if 'Transaction_ID' in df_standardized.columns:
            print("❌ 错误：发现Transaction_ID列，但数据库中没有此列")
            return False
        else:
            print("✅ 正确：没有Transaction_ID列")
        
        # 检查是否有Transaction_Num列（应该有）
        if 'Transaction_Num' in df_standardized.columns:
            print("✅ 正确：有Transaction_Num列")
        else:
            print("❌ 错误：缺少Transaction_Num列")
            return False
        
        # 检查所有映射是否正确
        expected_mappings = {
            'Copartner name': 'Copartner_name',
            'Order No.': 'Order_No',
            'Transaction Num': 'Transaction_Num',
            'Order types': 'Order_types',
            'Order status': 'Order_status',
            'Order price': 'Order_price',
            'Payment': 'Payment',
            'Order time': 'Order_time',
            'Equipment ID': 'Equipment_ID',
            'Equipment name': 'Equipment_name',
            'Branch name': 'Branch_name',
            'Payment date': 'Payment_date',
            'User name': 'User_name'
        }
        
        all_correct = True
        for excel_col, expected_db_col in expected_mappings.items():
            if expected_db_col in df_standardized.columns:
                print(f"✅ {excel_col} -> {expected_db_col}")
            else:
                print(f"❌ {excel_col} -> {expected_db_col} (映射失败)")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 列名映射检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_default_columns():
    """检查默认列设置"""
    print("\n=== 检查默认列设置 ===")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建空DataFrame
        df = pd.DataFrame()
        
        # 测试缺失列处理
        df_with_defaults = processor._handle_missing_columns(df, 'IOT')
        print(f"添加默认列后: {list(df_with_defaults.columns)}")
        
        # 检查是否有Transaction_ID列（应该没有）
        if 'Transaction_ID' in df_with_defaults.columns:
            print("❌ 错误：默认列中包含Transaction_ID")
            return False
        else:
            print("✅ 正确：默认列中没有Transaction_ID")
        
        # 检查是否有Transaction_Num列（应该有）
        if 'Transaction_Num' in df_with_defaults.columns:
            print("✅ 正确：默认列中有Transaction_Num")
        else:
            print("❌ 错误：默认列中缺少Transaction_Num")
            return False
        
        # 检查所有必需的列是否都存在
        required_columns = [
            'Copartner_name', 'Order_No', 'Order_types', 'Order_status',
            'Order_price', 'Payment', 'Order_time', 'Equipment_ID',
            'Equipment_name', 'Branch_name', 'Payment_date', 'User_name',
            'Transaction_Num'
        ]
        
        missing_columns = []
        for col in required_columns:
            if col not in df_with_defaults.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"❌ 缺少必需列: {missing_columns}")
            return False
        else:
            print("✅ 所有必需列都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 默认列检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_data_types():
    """检查数据类型标准化"""
    print("\n=== 检查数据类型标准化 ===")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含不同数据类型的测试数据
        test_data = {
            'Copartner_name': ['Partner1', 'Partner2'],
            'Order_No': ['ORD001', 'ORD002'],
            'Transaction_Num': ['T001', 'T002'],
            'Order_price': [100.0, 200.0],
            'Equipment_ID': [1001, 1002],  # 数字类型
            'Order_status': ['Finished', 'Refunded']
        }
        
        df = pd.DataFrame(test_data)
        print("原始数据类型:")
        print(df.dtypes)
        
        # 测试数据类型标准化
        df_standardized = processor._standardize_data_types(df)
        print("\n标准化后数据类型:")
        print(df_standardized.dtypes)
        
        # 检查所有列是否都是object类型（字符串）
        non_object_columns = []
        for col in df_standardized.columns:
            if df_standardized[col].dtype != 'object':
                non_object_columns.append(col)
        
        if non_object_columns:
            print(f"❌ 以下列不是字符串类型: {non_object_columns}")
            return False
        else:
            print("✅ 所有列都是字符串类型")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据类型检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_validators():
    """检查验证器逻辑"""
    print("\n=== 检查验证器逻辑 ===")
    
    try:
        from utils.validators import input_validator
        
        # 创建测试数据
        test_data = {
            'Order_price': [100.0, 200.0, 300.0],
            'Order_time': ['2024-12-19 10:00:00', '2024-12-19 10:01:00', '2024-12-19 10:02:00'],
            'Transaction_Num': ['T001', 'T002', 'T003'],
            'Order_No': ['ORD001', 'ORD002', 'ORD003']
        }
        
        df = pd.DataFrame(test_data)
        
        # 测试数据验证
        is_valid, errors = input_validator.validate_transaction_data(df, "test.xlsx")
        
        if is_valid:
            print("✅ 数据验证通过")
        else:
            print(f"❌ 数据验证失败: {errors}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证器检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主检查函数"""
    print("=== 完整代码逻辑检查 ===")
    print("检查所有修复是否正确，确保与用户数据库结构完全匹配\n")
    
    tests = [
        ("列名映射逻辑", check_column_mapping),
        ("默认列设置", check_default_columns),
        ("数据类型标准化", check_data_types),
        ("验证器逻辑", check_validators)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n{'='*50}")
    print(f"检查结果总结")
    print(f"{'='*50}")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有检查都通过！代码逻辑完全正确！")
        print("系统现在应该能够正确处理用户的Excel文件。")
        return 0
    else:
        print(f"\n⚠️ 有 {total-passed} 项检查失败，需要进一步修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
