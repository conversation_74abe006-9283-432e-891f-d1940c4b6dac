# -*- coding: utf-8 -*-
"""
最终日志简化脚本
彻底简化所有冗余输出，只保留关键统计信息
"""

import re
import os

def final_log_simplification():
    """最终日志简化 - 移除所有冗余的详细输出"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义要移除或简化的输出模式
    patterns_to_remove = [
        # 1. 移除每个Transaction ID的详细处理过程
        r'print\(f".*处理Transaction ID.*找到.*条匹配记录"\)',
        r'print\(f".*记录.*标记设置成功.*"\)',
        r'print\(f".*Transaction ID.*匹配统计.*"\)',
        
        # 2. 移除修复前后的详细统计
        r'print\(f".*修复前匹配数.*修复后匹配数.*"\)',
        r'print\(f".*Transaction ID仍无匹配.*"\)',
        
        # 3. 移除详细的验证信息
        r'print\(f".*验证.*实际匹配的记录数量.*"\)',
        r'print\(f".*验证.*DataFrame中实际标记数量.*"\)',
        r'print\(f".*严重警告.*matched_indices数量.*"\)',
        
        # 4. 移除详细的插入统计
        r'print\(f".*插入统计.*条记录.*总金额.*"\)',
        r'print\(f".*TXN:.*Order:.*RM.*"\)',
        
        # 5. 移除详细的文件统计
        r'print\(f".*第二文件总记录数.*"\)',
        r'print\(f".*第二文件Transaction Num非空记录数.*"\)',
        r'print\(f".*第二文件Transaction Num唯一值数量.*"\)',
        r'print\(f".*第二文件Transaction Num填充率.*"\)',
        
        # 6. 移除详细的匹配能力分析
        r'print\(f".*第一文件有效Transaction ID数量.*"\)',
        r'print\(f".*第二文件有效Transaction Num数量.*"\)',
        r'print\(f".*第一文件Transaction ID样本.*"\)',
        r'print\(f".*第二文件Transaction Num样本.*"\)',
        r'print\(f".*可匹配的Transaction ID/Num数量.*"\)',
        r'print\(f".*匹配的Transaction ID样本.*"\)',
        
        # 7. 移除详细的处理过程
        r'print\(f".*处理日期.*条记录.*"\)',
        r'print\(f".*发现.*个日期分组.*"\)',
        
        # 8. 移除详细的金额分析
        r'print\(f".*第一文件总金额.*包含所有settled记录.*"\)',
        r'print\(f".*第一文件没有Order types列.*无法检查API订单.*"\)',
        r'print\(f".*第二文件总金额.*排除API订单.*"\)',
        r'print\(f".*金额差异.*第一文件无法排除API订单.*"\)',
        
        # 9. 移除详细的数据恢复过程
        r'print\(f".*Transaction Num修复.*条.*"\)',
        r'print\(f".*Equipment信息恢复.*条.*"\)',
        r'print\(f".*Order No.信息恢复.*条.*"\)',
        
        # 10. 移除详细的状态统计
        r'print\(f"True:.*"\)',
        r'print\(f"False:.*"\)',
        r'print\(f"NaN:.*"\)',
        r'print\(f"\'Finish\':.*条"\)',
        r'print\(f"\'Refunded\':.*条"\)',
        
        # 11. 移除详细的最终统计
        r'print\(f".*第二文件最终总金额.*"\)',
        r'print\(f".*第一文件总金额.*"\)',
        r'print\(f".*金额差异.*"\)',
        
        # 12. 移除详细的文件路径信息
        r'print\(f"使用指定的文件路径:"\)',
        r'print\(f"第一文件:.*"\)',
        r'print\(f"第二文件:.*"\)',
        r'print\(f"Sheet名称:.*"\)',
    ]
    
    # 应用移除规则
    modified_content = content
    removal_count = 0
    
    for pattern in patterns_to_remove:
        matches = re.findall(pattern, modified_content)
        if matches:
            modified_content = re.sub(pattern, '# 详细日志已简化', modified_content)
            removal_count += len(matches)
            print(f"移除了 {len(matches)} 个详细输出: {pattern[:50]}...")
    
    # 添加简化的摘要输出函数
    summary_function = '''
# ======================【简化摘要输出】======================
def print_simple_summary(stats, total_bill_amt, df2):
    """打印简化的处理摘要"""
    
    if stats and stats.current_mode:
        mode_stats = stats.mode_stats[stats.current_mode]
        
        # 基本处理结果
        log_manager.log_summary(f"处理完成: 匹配 {mode_stats['matched']} 条, 插入 {mode_stats['inserted']} 条")
        
        # 金额验证
        try:
            df2_finish = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
                            (~df2["Order types"].str.strip().str.lower().str.contains("api", na=False))]
            final_total = df2_finish["Order price"].sum()
            
            if abs(total_bill_amt - final_total) < 0.01:
                log_manager.log_summary(f"金额验证: RM{total_bill_amt:.2f} 完全匹配")
            else:
                log_manager.log_summary(f"金额差异: RM{abs(total_bill_amt - final_total):.2f}")
        except:
            log_manager.log_summary("金额验证: 无法计算")
        
        log_manager.log_summary("数据处理完成")

'''
    
    # 在文件中查找合适的位置插入简化函数
    if 'print_simple_summary' not in modified_content:
        # 在LogManager类后面插入
        insert_pos = modified_content.find('# 全局日志管理器\nlog_manager = LogManager()')
        if insert_pos != -1:
            insert_pos = modified_content.find('\n', insert_pos) + 1
            modified_content = modified_content[:insert_pos] + summary_function + modified_content[insert_pos:]
    
    # 替换复杂的统计输出为简化版本
    complex_stats_pattern = r'print\(".*transaction_id 模式统计:"\)\s*print\("处理:.*条"\)\s*print\("匹配:.*条"\)\s*print\("插入:.*条"\)'
    if re.search(complex_stats_pattern, modified_content, re.DOTALL):
        modified_content = re.sub(complex_stats_pattern, 'print_simple_summary(processor.stats, total_bill_amt, df2)', modified_content, flags=re.DOTALL)
        print("替换了复杂统计输出为简化版本")
    
    # 移除重复的处理完成信息
    modified_content = re.sub(r'print\(".*处理完成，耗时:.*秒"\)', '# 处理时间已简化', modified_content)
    modified_content = re.sub(r'print\(".*模块化数据处理完成"\)', '# 模块完成信息已简化', modified_content)
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"\n✅ 最终日志简化完成！")
    print(f"总共移除了 {removal_count} 个详细输出")
    print(f"文件已更新: {script_path}")

def create_ultra_simple_version():
    """创建超简化版本 - 只保留最关键的信息"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 将所有剩余的详细print语句改为log_detailed
    detailed_print_patterns = [
        r'print\(" 检查Transaction Num与Transaction ID的匹配能力\.\.\."\)',
        r'print\(" 将使用传统更新方式"\)',
        r'print\(".*检测到 Transaction Num 列"\)',
        r'print\(".*Transaction Num具备匹配能力"\)',
        r'print\(".*将使用Transaction ID匹配方式"\)',
        r'print\(".*将使用Transaction ID进行数据匹配和同步"\)',
        r'print\(".*启动模块化数据处理器\.\.\."\)',
        r'print\(".*开始按日期分组处理\.\.\."\)',
        r'print\(".*匹配模式设置为:.*"\)',
        r'print\(".*执行数据恢复和补全\.\.\."\)',
        r'print\(".*开始执行数据恢复\.\.\."\)',
        r'print\(".*Transaction ID匹配模式.*跳过自动修正"\)',
        r'print\(".*Transaction ID匹配模式.*数据已通过Transaction ID同步.*无需自动修正"\)',
        r'print\(".*处理完成，结果已保存.*"\)',
        r'print\("- 数据已保存 DATA sheet"\)',
        r'print\("- 日志已保存 LOG sheet"\)',
        r'print\(".*金额匹配成功！"\)',
        r'print\("脚本执行完成"\)',
    ]
    
    modified_content = content
    conversion_count = 0
    
    for pattern in detailed_print_patterns:
        matches = re.findall(pattern, modified_content)
        if matches:
            # 提取print内容并转换为log_detailed
            def replace_func(match):
                print_content = match.group(0)
                # 提取引号内的内容
                content_match = re.search(r'print\("(.*)"\)', print_content)
                if content_match:
                    inner_content = content_match.group(1)
                    return f'log_manager.log_detailed("{inner_content}")'
                return print_content
            
            modified_content = re.sub(pattern, replace_func, modified_content)
            conversion_count += len(matches)
            print(f"转换了 {len(matches)} 个print为log_detailed")
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"\n✅ 超简化版本创建完成！")
    print(f"总共转换了 {conversion_count} 个print语句")

if __name__ == "__main__":
    print("开始最终日志简化...")
    
    # 1. 移除所有冗余的详细输出
    final_log_simplification()
    
    # 2. 创建超简化版本
    create_ultra_simple_version()
    
    print("\n🎉 日志系统彻底简化完成！")
    print("现在运行应用程序应该只会看到最关键的统计信息。")
    print("\n预期的简化效果:")
    print("- 开始数据处理...")
    print("- 第一文件加载完成: XX 条记录")
    print("- Transaction ID列检测完成")
    print("- 开始智能检测Transaction Num匹配能力...")
    print("- Transaction Num具备匹配能力")
    print("- 处理完成: 匹配 XX 条, 插入 XX 条")
    print("- 金额验证: RMXX.XX 完全匹配")
    print("- 数据处理完成")
