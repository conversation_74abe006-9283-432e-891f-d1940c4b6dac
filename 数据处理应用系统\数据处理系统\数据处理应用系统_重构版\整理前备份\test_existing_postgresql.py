# -*- coding: utf-8 -*-
"""
测试现有PostgreSQL数据的脚本
确保双数据库管理器能正确连接和使用现有的PostgreSQL数据
"""

import os
import sys

def test_dual_database_manager():
    """测试双数据库管理器"""
    print("🧪 测试双数据库管理器连接现有PostgreSQL...")
    
    try:
        from database.dual_database_manager import get_dual_database_manager
        manager = get_dual_database_manager()
        
        # 获取数据库状态
        status = manager.get_database_status()
        
        print("\n=== 双数据库状态检查 ===")
        for db_type, info in status.items():
            print(f"\n{db_type} 数据库:")
            enabled_status = "✅ 已启用" if info['enabled'] else "❌ 未启用"
            connected_status = "✅ 连接成功" if info['connected'] else "❌ 连接失败"
            print(f"  启用状态: {enabled_status}")
            print(f"  连接状态: {connected_status}")
            
            if info['connected'] and info['tables']:
                print(f"  表数量: {len(info['tables'])}")
                total_rows = sum(table['rows'] for table in info['tables'])
                print(f"  总行数: {total_rows:,}")
                print("  主要表:")
                for table in info['tables'][:5]:  # 显示前5个表
                    print(f"    {table['name']}: {table['rows']:,} 行")
                
                if len(info['tables']) > 5:
                    print(f"    ... 还有 {len(info['tables']) - 5} 个表")
        
        # 测试可用数据库
        available_dbs = manager.get_available_databases()
        print(f"\n📊 可用数据库: {available_dbs}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_backup_functionality():
    """测试备份功能"""
    print("\n🧪 测试备份功能...")
    
    try:
        from database.dual_database_manager import get_dual_database_manager
        manager = get_dual_database_manager()
        
        # 测试PostgreSQL备份
        if manager.postgresql_config['enabled'] and manager.test_postgresql_connection():
            print("📤 测试PostgreSQL备份...")
            backup_file = manager.backup_database('PostgreSQL')
            print(f"✅ PostgreSQL备份成功: {backup_file}")
            
            # 检查备份文件是否存在
            if os.path.exists(backup_file):
                file_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
                print(f"📁 备份文件大小: {file_size:.2f} MB")
            else:
                print("⚠️ 备份文件不存在")
        
        # 测试SQLite备份
        if manager.sqlite_config['enabled'] and manager.test_sqlite_connection():
            print("📤 测试SQLite备份...")
            backup_file = manager.backup_database('SQLite')
            print(f"✅ SQLite备份成功: {backup_file}")
            
            # 检查备份文件是否存在
            if os.path.exists(backup_file):
                file_size = os.path.getsize(backup_file) / (1024 * 1024)  # MB
                print(f"📁 备份文件大小: {file_size:.2f} MB")
            else:
                print("⚠️ 备份文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 备份测试失败: {e}")
        return False

def test_data_import():
    """测试数据导入功能"""
    print("\n🧪 测试数据导入功能...")
    
    try:
        import pandas as pd
        from database.dual_database_manager import get_dual_database_manager
        
        manager = get_dual_database_manager()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Order_No': ['TEST_EXISTING_001', 'TEST_EXISTING_002'],
            'Equipment_ID': ['EQ_TEST_001', 'EQ_TEST_002'],
            'Order_time': ['2024-06-19 10:00:00', '2024-06-19 11:00:00'],
            'Order_price': [10.0, 20.0],
            'Order_status': ['Finished', 'Finished'],
            'Order_types': ['Normal', 'Normal'],
            'Equipment_name': ['测试设备1', '测试设备2'],
            'Branch_name': ['测试分店1', '测试分店2']
        })
        
        print(f"📊 创建测试数据: {len(test_data)} 行")
        
        # 测试导入到可用数据库
        available_dbs = manager.get_available_databases()
        
        if available_dbs:
            print(f"📤 测试导入到: {available_dbs}")
            results = manager.import_data_to_multiple_databases(
                test_data, 
                'TEST_EXISTING_DATA', 
                available_dbs
            )
            
            for db_type, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  {db_type}: {status}")
            
            return all(results.values())
        else:
            print("⚠️ 没有可用的数据库")
            return False
        
    except Exception as e:
        print(f"❌ 数据导入测试失败: {e}")
        return False

def test_main_application_integration():
    """测试主应用程序集成"""
    print("\n🧪 测试主应用程序集成...")
    
    try:
        # 检查主应用程序是否能正确识别双数据库
        from 数据处理与导入应用_完整版 import ImportTab
        
        print("✅ 主应用程序导入成功")
        
        # 检查DUAL_DATABASE_AVAILABLE标志
        import 数据处理与导入应用_完整版 as main_app
        if hasattr(main_app, 'DUAL_DATABASE_AVAILABLE'):
            print(f"📊 双数据库功能状态: {main_app.DUAL_DATABASE_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用程序集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 测试现有PostgreSQL数据集成...")
    print("=" * 60)
    
    tests = [
        ("双数据库管理器", test_dual_database_manager),
        ("备份功能", test_backup_functionality),
        ("数据导入功能", test_data_import),
        ("主应用程序集成", test_main_application_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常 {test_name}: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！现有PostgreSQL数据已完全集成。")
        print("\n📋 您现在可以:")
        print("1. 在主应用程序中选择'同时导入两个数据库'")
        print("2. 使用双数据库备份功能")
        print("3. 在SQLite和PostgreSQL之间同步数据")
        print("4. 使用智能迁移工具进行增量更新")
    else:
        print("⚠️ 部分测试失败，请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
