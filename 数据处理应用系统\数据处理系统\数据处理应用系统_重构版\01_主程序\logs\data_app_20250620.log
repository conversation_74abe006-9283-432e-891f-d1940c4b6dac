2025-06-20 11:07:54,538 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:07:54,723 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:07:54,724 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:07:54,724 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:07:54,747 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:07:54,747 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:07:54,753 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:07:54,761 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:07:54,823 - INFO - [general] 数据库设置已加载
2025-06-20 11:07:54,823 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:07:54,823 - INFO - [settings] 数据库设置已加载
2025-06-20 11:07:54,823 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:07:54,825 - INFO - 数据处理与导入应用已启动
2025-06-20 11:07:54,926 - INFO - [settings] 没有找到备份文件
2025-06-20 11:15:40,270 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:15:40,366 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:15:40,366 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:15:40,366 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:15:40,377 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:15:40,377 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:15:40,385 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:15:40,385 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:15:40,399 - INFO - [general] 数据库设置已加载
2025-06-20 11:15:40,399 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:15:40,399 - INFO - [settings] 数据库设置已加载
2025-06-20 11:15:40,399 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:15:40,401 - INFO - 数据处理与导入应用已启动
2025-06-20 11:15:40,603 - INFO - [settings] 没有找到备份文件
2025-06-20 11:27:23,323 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:27:23,561 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:27:23,561 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:27:23,561 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:27:23,601 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:27:23,601 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:27:23,613 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:27:23,614 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:27:23,682 - INFO - [general] 数据库设置已加载
2025-06-20 11:27:23,683 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:27:23,683 - INFO - [settings] 数据库设置已加载
2025-06-20 11:27:23,684 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:27:23,686 - INFO - 数据处理与导入应用已启动
2025-06-20 11:27:23,813 - INFO - [settings] 没有找到备份文件
2025-06-20 11:27:34,432 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:27:34,538 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:27:34,538 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:27:34,538 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:27:34,550 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:27:34,550 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:27:34,558 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:27:34,558 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:27:34,573 - INFO - [general] 数据库设置已加载
2025-06-20 11:27:34,573 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:27:34,573 - INFO - [settings] 数据库设置已加载
2025-06-20 11:27:34,573 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:27:34,574 - INFO - 数据处理与导入应用已启动
2025-06-20 11:27:34,777 - INFO - [settings] 没有找到备份文件
2025-06-20 11:27:45,889 - INFO - [settings] 没有找到备份文件
2025-06-20 11:34:36,578 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:34:36,757 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:34:36,757 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:34:36,758 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:34:36,779 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:34:36,779 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:34:36,788 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:34:36,789 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:34:36,853 - INFO - [general] 数据库设置已加载
2025-06-20 11:34:36,853 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:34:36,853 - INFO - [settings] 数据库设置已加载
2025-06-20 11:34:36,853 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:34:36,855 - INFO - 数据处理与导入应用已启动
2025-06-20 11:34:36,970 - INFO - [settings] 没有找到备份文件
2025-06-20 11:39:20,240 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:39:20,337 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:39:20,337 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:39:20,337 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:39:20,347 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:39:20,348 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:39:20,356 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:39:20,356 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:39:20,371 - INFO - [general] 数据库设置已加载
2025-06-20 11:39:20,372 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:39:20,372 - INFO - [settings] 数据库设置已加载
2025-06-20 11:39:20,372 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:39:20,374 - INFO - 数据处理与导入应用已启动
2025-06-20 11:39:20,596 - INFO - [settings] 没有找到备份文件
2025-06-20 11:42:48,111 - WARNING - 双数据库模块不可用，将使用单数据库模式
2025-06-20 11:42:48,311 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:42:48,312 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:42:48,312 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:42:48,333 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:42:48,334 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:42:48,339 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:42:48,339 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:42:48,404 - INFO - [general] 数据库设置已加载
2025-06-20 11:42:48,404 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:42:48,404 - INFO - [settings] 数据库设置已加载
2025-06-20 11:42:48,404 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:42:48,406 - INFO - 数据处理与导入应用已启动
2025-06-20 11:42:48,514 - INFO - [settings] 没有找到备份文件
2025-06-20 11:45:20,986 - INFO - 双数据库模块加载成功
2025-06-20 11:45:21,124 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:45:21,124 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:45:21,124 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:45:21,602 - INFO - [general] 可用数据库: SQLite
2025-06-20 11:45:21,611 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:45:21,611 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:45:21,722 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:45:21,723 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:45:21,765 - INFO - [general] 数据库设置已加载
2025-06-20 11:45:21,766 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:45:21,766 - INFO - [settings] 数据库设置已加载
2025-06-20 11:45:21,766 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:45:21,923 - INFO - [database_config] 数据库状态已刷新
2025-06-20 11:45:21,925 - INFO - 数据处理与导入应用已启动
2025-06-20 11:45:22,094 - INFO - [settings] 没有找到备份文件
2025-06-20 11:45:39,983 - INFO - 双数据库模块加载成功
2025-06-20 11:45:40,149 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:45:40,149 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:45:40,149 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:45:40,482 - INFO - [general] 可用数据库: SQLite
2025-06-20 11:45:40,485 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:45:40,485 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:45:40,560 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:45:40,560 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:45:40,600 - INFO - [general] 数据库设置已加载
2025-06-20 11:45:40,600 - INFO - [general] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:45:40,600 - INFO - [settings] 数据库设置已加载
2025-06-20 11:45:40,600 - INFO - [settings] 当前数据库路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\database\sales_reports.db
2025-06-20 11:45:40,699 - INFO - [database_config] 数据库状态已刷新
2025-06-20 11:45:40,700 - INFO - 数据处理与导入应用已启动
2025-06-20 11:45:40,838 - INFO - [settings] 没有找到备份文件
2025-06-20 11:45:59,415 - INFO - [settings] 已选择数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:46:00,521 - INFO - [settings] 数据库路径已保存: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:46:04,057 - INFO - [settings] 没有找到备份文件
2025-06-20 11:46:58,964 - INFO - 双数据库模块加载成功
2025-06-20 11:46:59,153 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:46:59,153 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:46:59,154 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:46:59,536 - INFO - [general] 可用数据库: SQLite
2025-06-20 11:46:59,539 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:46:59,540 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:46:59,620 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:46:59,620 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:46:59,641 - INFO - [general] 数据库设置已加载
2025-06-20 11:46:59,641 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:46:59,641 - INFO - [settings] 数据库设置已加载
2025-06-20 11:46:59,641 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:46:59,737 - INFO - [database_config] 数据库状态已刷新
2025-06-20 11:46:59,742 - INFO - 数据处理与导入应用已启动
2025-06-20 11:46:59,864 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 11:47:12,965 - INFO - [import] 数据库导入目标已更改为: 仅PostgreSQL
2025-06-20 11:47:14,366 - INFO - [import] 可用数据库: SQLite
2025-06-20 11:50:27,986 - INFO - 双数据库模块加载成功
2025-06-20 11:50:28,181 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:50:28,182 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:50:28,182 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:50:28,726 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-20 11:50:28,731 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:50:28,731 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:50:28,840 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:50:28,840 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:50:28,859 - INFO - [general] 数据库设置已加载
2025-06-20 11:50:28,860 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:50:28,860 - INFO - [settings] 数据库设置已加载
2025-06-20 11:50:28,860 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:50:29,203 - INFO - [database_config] 数据库状态已刷新
2025-06-20 11:50:29,209 - INFO - 数据处理与导入应用已启动
2025-06-20 11:50:29,316 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 11:56:47,326 - INFO - 双数据库模块加载成功
2025-06-20 11:56:47,491 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 11:56:47,491 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:56:47,491 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 11:56:47,911 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-20 11:56:47,917 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 11:56:47,918 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 11:56:48,145 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:56:48,145 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 11:56:48,166 - INFO - [general] 数据库设置已加载
2025-06-20 11:56:48,167 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:56:48,167 - INFO - [settings] 数据库设置已加载
2025-06-20 11:56:48,167 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 11:56:48,454 - INFO - [database_config] 数据库状态已刷新
2025-06-20 11:56:48,458 - INFO - 数据处理与导入应用已启动
2025-06-20 11:56:48,557 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 11:57:04,354 - INFO - [import] 开始测试数据库连接...
2025-06-20 11:57:04,360 - INFO - [import] ✅ SQLite连接测试成功
2025-06-20 11:57:04,402 - INFO - [import] ✅ PostgreSQL连接测试成功
2025-06-20 11:57:04,501 - INFO - [import] 可用数据库: SQLite, PostgreSQL
2025-06-20 11:57:12,156 - INFO - [import] 可用数据库: SQLite, PostgreSQL
2025-06-20 11:57:12,158 - INFO - [import] 数据库配置已更新
2025-06-20 12:00:56,699 - INFO - 双数据库模块加载成功
2025-06-20 12:00:56,871 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 12:00:56,872 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 12:00:56,872 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 12:00:57,334 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-20 12:00:57,338 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 12:00:57,339 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 12:00:57,545 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 12:00:57,546 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 12:00:57,571 - INFO - [general] 数据库设置已加载
2025-06-20 12:00:57,571 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 12:00:57,571 - INFO - [settings] 数据库设置已加载
2025-06-20 12:00:57,572 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 12:00:57,830 - INFO - [database_config] 数据库状态已刷新
2025-06-20 12:00:57,834 - INFO - 数据处理与导入应用已启动
2025-06-20 12:00:57,943 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:04:19,683 - INFO - 双数据库模块加载成功
2025-06-20 17:04:19,851 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 17:04:19,851 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 17:04:19,851 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 17:04:20,447 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-20 17:04:20,451 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 17:04:20,451 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 17:04:20,568 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 17:04:20,568 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 17:04:20,598 - INFO - [general] 数据库设置已加载
2025-06-20 17:04:20,598 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 17:04:20,598 - INFO - [settings] 数据库设置已加载
2025-06-20 17:04:20,598 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 17:04:21,268 - INFO - [database_config] 数据库状态已刷新
2025-06-20 17:04:21,278 - INFO - 数据处理与导入应用已启动
2025-06-20 17:04:21,392 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:05:01,488 - INFO - [processing] 已选择第一文件: TRANSACTION_LISTING_FROM_DATE_16062025_TO_16062025_zeroiot.xlsx
2025-06-20 17:05:04,581 - INFO - [processing] 已选择第二文件: 160625 CHINA IOT.xlsx
2025-06-20 17:05:07,899 - INFO - [processing] 开始处理文件...
2025-06-20 17:05:07,900 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-20 17:05:07,900 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-20 17:05:07,900 - INFO - [processing] 使用指定的Sheet名称: transaction
2025-06-20 17:05:09,613 - INFO - [processing] 使用第一文件路径: C:/Users/<USER>/Desktop/June/IOT/TRANSACTION_LISTING_FROM_DATE_16062025_TO_16062025_zeroiot.xlsx
2025-06-20 17:05:09,613 - INFO - [processing] 使用第二文件路径: C:/Users/<USER>/Desktop/June/IOT/160625 CHINA IOT.xlsx
2025-06-20 17:05:09,613 - INFO - [processing] 使用sheet: transaction
2025-06-20 17:05:09,613 - INFO - [processing] 第一文件读取的列名： ['Date', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-20 17:05:09,613 - INFO - [processing] 第一文件列数：12
2025-06-20 17:05:09,613 - INFO - [processing] ⏰ 使用Date列的时间信息
2025-06-20 17:05:09,613 - INFO - [processing] 警告：第一文件列数为 12，预期为27列。将继续处理，但请检查文件格式是否正确。
2025-06-20 17:05:09,614 - INFO - [processing] 找到'Transaction ID'列，将用于匹配
2025-06-20 17:05:09,614 - INFO - [processing] ⏰ 处理日期时间数据...
2025-06-20 17:05:09,614 - INFO - [processing] 📅 日期一致性检查通过: 2025-06-16
2025-06-20 17:05:09,614 - INFO - [processing] 第一文件9位ID数量：2532
2025-06-20 17:05:09,614 - INFO - [processing] 检查Transaction ID一致性...
2025-06-20 17:05:09,614 - INFO - [processing] ✅ 所有Transaction ID都是唯一的
2025-06-20 17:05:09,614 - INFO - [processing] 第二文件标准化后列名： ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-20 17:05:09,614 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                        [0m| 0/3053 [00:00<?, ?条/s][0m
2025-06-20 17:05:09,614 - INFO - [processing] 🔍 开始智能检测Transaction Num匹配能力...
2025-06-20 17:05:09,615 - INFO - [processing] ✅ 检测到 Transaction Num 列
2025-06-20 17:05:09,615 - INFO - [processing] � 第二文件总记录数: 3695
2025-06-20 17:05:09,615 - INFO - [processing] � 第二文件Transaction Num非空记录数: 3251
2025-06-20 17:05:09,615 - INFO - [processing] � 第二文件Transaction Num唯一值数量: 3251
2025-06-20 17:05:09,615 - INFO - [processing] � 第二文件Transaction Num填充率: 88.0%
2025-06-20 17:05:09,616 - INFO - [processing] 🔍 检查Transaction Num与Transaction ID的匹配能力...
2025-06-20 17:05:09,616 - INFO - [processing] 📊 第一文件有效Transaction ID数量: 3053
2025-06-20 17:05:09,616 - INFO - [processing] � 第二文件有效Transaction Num数量: 3251
2025-06-20 17:05:09,616 - INFO - [processing] 📊 第一文件Transaction ID样本: ['2935590731', '2935243412', '2935957204', '2935070686', '2935939282']
2025-06-20 17:05:09,616 - INFO - [processing] 📊 第二文件Transaction Num样本: ['2935590731', '2935957204', '2935709555', '2935243412', '2935070686']
2025-06-20 17:05:09,616 - INFO - [processing] 📊 可匹配的Transaction ID/Num数量: 3019
2025-06-20 17:05:09,617 - INFO - [processing] 📊 匹配的Transaction ID样本: ['2935590731', '2935243412', '2935957204', '2935070686', '2935939282']
2025-06-20 17:05:09,617 - INFO - [processing] 📊 匹配率: 98.9%
2025-06-20 17:05:09,617 - INFO - [processing] ✅ Transaction Num具备匹配能力
2025-06-20 17:05:09,617 - INFO - [processing] 🔄 将使用Transaction ID匹配方式
2025-06-20 17:05:09,617 - INFO - [processing] 🎯 匹配模式: Transaction ID匹配
2025-06-20 17:05:09,617 - INFO - [processing] 📝 将使用Transaction ID进行数据匹配和同步
2025-06-20 17:05:09,618 - INFO - [processing] 🚀 启动模块化数据处理器...
2025-06-20 17:05:09,618 - INFO - [processing] 🗓️ 开始按日期分组处理...
2025-06-20 17:05:09,618 - INFO - [processing] 🎯 匹配模式设置为: transaction_id
2025-06-20 17:05:09,618 - INFO - [processing] 📊 发现 1 个日期分组
2025-06-20 17:05:09,715 - INFO - [processing] 错误: 处理 9_digit 阶段:   0%|[32m                                                   [0m| 0/3053 [00:00<?, ?条/s][0m
2025-06-20 17:05:09,740 - INFO - [processing] 📅 处理日期: 2025-06-16 (3053 条记录)
2025-06-20 17:05:09,740 - INFO - [processing] 🔍 调试 1: Transaction ID: '2934585148' -> '2934585148'
2025-06-20 17:05:09,740 - INFO - [processing] 🔍 修复前匹配数: 1, 修复后匹配数: 1
2025-06-20 17:05:09,740 - INFO - [processing] ✅ Transaction ID匹配成功
2025-06-20 17:05:09,740 - INFO - [processing] 🔍 处理Transaction ID 2934585148，找到 1 条匹配记录
2025-06-20 17:05:09,740 - INFO - [processing] ✅ 记录 3694 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,741 - INFO - [processing] 📊 Transaction ID 2934585148 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,741 - INFO - [processing] 🔍 调试 2: Transaction ID: '2934585372' -> '2934585372'
2025-06-20 17:05:09,741 - INFO - [processing] 🔍 修复前匹配数: 1, 修复后匹配数: 1
2025-06-20 17:05:09,741 - INFO - [processing] ✅ Transaction ID匹配成功
2025-06-20 17:05:09,741 - INFO - [processing] 🔍 处理Transaction ID 2934585372，找到 1 条匹配记录
2025-06-20 17:05:09,741 - INFO - [processing] ✅ 记录 3693 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,741 - INFO - [processing] 📊 Transaction ID 2934585372 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,742 - INFO - [processing] 🔍 调试 3: Transaction ID: '2934588574' -> '2934588574'
2025-06-20 17:05:09,742 - INFO - [processing] 🔍 修复前匹配数: 1, 修复后匹配数: 1
2025-06-20 17:05:09,742 - INFO - [processing] ✅ Transaction ID匹配成功
2025-06-20 17:05:09,742 - INFO - [processing] 🔍 处理Transaction ID 2934588574，找到 1 条匹配记录
2025-06-20 17:05:09,742 - INFO - [processing] ✅ 记录 3692 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,742 - INFO - [processing] 📊 Transaction ID 2934588574 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,742 - INFO - [processing] 🔍 调试 4: Transaction ID: '2934589198' -> '2934589198'
2025-06-20 17:05:09,742 - INFO - [processing] 🔍 修复前匹配数: 1, 修复后匹配数: 1
2025-06-20 17:05:09,742 - INFO - [processing] ✅ Transaction ID匹配成功
2025-06-20 17:05:09,742 - INFO - [processing] 🔍 处理Transaction ID 2934589198，找到 1 条匹配记录
2025-06-20 17:05:09,743 - INFO - [processing] ✅ 记录 3691 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,743 - INFO - [processing] 📊 Transaction ID 2934589198 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,743 - INFO - [processing] 🔍 调试 5: Transaction ID: '2934593122' -> '2934593122'
2025-06-20 17:05:09,743 - INFO - [processing] 🔍 修复前匹配数: 1, 修复后匹配数: 1
2025-06-20 17:05:09,743 - INFO - [processing] ✅ Transaction ID匹配成功
2025-06-20 17:05:09,743 - INFO - [processing] 🔍 处理Transaction ID 2934593122，找到 1 条匹配记录
2025-06-20 17:05:09,743 - INFO - [processing] ✅ 记录 3688 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,743 - INFO - [processing] 📊 Transaction ID 2934593122 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,743 - INFO - [processing] 🔍 处理Transaction ID 2934602693，找到 1 条匹配记录
2025-06-20 17:05:09,743 - INFO - [processing] ✅ 记录 3687 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,744 - INFO - [processing] 📊 Transaction ID 2934602693 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,744 - INFO - [processing] 🔍 处理Transaction ID 2934604186，找到 1 条匹配记录
2025-06-20 17:05:09,744 - INFO - [processing] ✅ 记录 3686 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,744 - INFO - [processing] 📊 Transaction ID 2934604186 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,744 - INFO - [processing] 🔍 处理Transaction ID 2934605442，找到 1 条匹配记录
2025-06-20 17:05:09,744 - INFO - [processing] ✅ 记录 3685 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,744 - INFO - [processing] 📊 Transaction ID 2934605442 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,744 - INFO - [processing] 🔍 处理Transaction ID 2934610635，找到 1 条匹配记录
2025-06-20 17:05:09,744 - INFO - [processing] ✅ 记录 3684 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,745 - INFO - [processing] 📊 Transaction ID 2934610635 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,745 - INFO - [processing] 🔍 处理Transaction ID 2934611394，找到 1 条匹配记录
2025-06-20 17:05:09,745 - INFO - [processing] ✅ 记录 3683 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,745 - INFO - [processing] 📊 Transaction ID 2934611394 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,745 - INFO - [processing] 🔍 处理Transaction ID 2934619237，找到 1 条匹配记录
2025-06-20 17:05:09,745 - INFO - [processing] ✅ 记录 3682 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,745 - INFO - [processing] 📊 Transaction ID 2934619237 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,745 - INFO - [processing] 🔍 处理Transaction ID 2934628095，找到 1 条匹配记录
2025-06-20 17:05:09,746 - INFO - [processing] ✅ 记录 3681 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,746 - INFO - [processing] 📊 Transaction ID 2934628095 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,746 - INFO - [processing] 🔍 处理Transaction ID 2934641994，找到 1 条匹配记录
2025-06-20 17:05:09,746 - INFO - [processing] ✅ 记录 3678 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,746 - INFO - [processing] 📊 Transaction ID 2934641994 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,746 - INFO - [processing] 🔍 处理Transaction ID 2934644769，找到 1 条匹配记录
2025-06-20 17:05:09,746 - INFO - [processing] ✅ 记录 3675 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,747 - INFO - [processing] 📊 Transaction ID 2934644769 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,747 - INFO - [processing] 🔍 处理Transaction ID 2934648240，找到 1 条匹配记录
2025-06-20 17:05:09,747 - INFO - [processing] ✅ 记录 3674 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,747 - INFO - [processing] 📊 Transaction ID 2934648240 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,747 - INFO - [processing] 🔍 处理Transaction ID 2934649373，找到 1 条匹配记录
2025-06-20 17:05:09,747 - INFO - [processing] ✅ 记录 3673 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,747 - INFO - [processing] 📊 Transaction ID 2934649373 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,748 - INFO - [processing] 🔍 处理Transaction ID 2934650524，找到 1 条匹配记录
2025-06-20 17:05:09,748 - INFO - [processing] ✅ 记录 3672 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,748 - INFO - [processing] 📊 Transaction ID 2934650524 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,748 - INFO - [processing] 🔍 处理Transaction ID 2934653690，找到 1 条匹配记录
2025-06-20 17:05:09,748 - INFO - [processing] ✅ 记录 3670 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,748 - INFO - [processing] 📊 Transaction ID 2934653690 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,748 - INFO - [processing] 🔍 处理Transaction ID 2934654915，找到 1 条匹配记录
2025-06-20 17:05:09,748 - INFO - [processing] ✅ 记录 3669 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,748 - INFO - [processing] 📊 Transaction ID 2934654915 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,748 - INFO - [processing] 🔍 处理Transaction ID 2934663076，找到 1 条匹配记录
2025-06-20 17:05:09,749 - INFO - [processing] ✅ 记录 3668 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,749 - INFO - [processing] 📊 Transaction ID 2934663076 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,749 - INFO - [processing] 🔍 处理Transaction ID 2934664445，找到 1 条匹配记录
2025-06-20 17:05:09,749 - INFO - [processing] ✅ 记录 3667 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,749 - INFO - [processing] 📊 Transaction ID 2934664445 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,749 - INFO - [processing] 🔍 处理Transaction ID 2934669997，找到 1 条匹配记录
2025-06-20 17:05:09,749 - INFO - [processing] ✅ 记录 3666 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,749 - INFO - [processing] 📊 Transaction ID 2934669997 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,750 - INFO - [processing] 🔍 处理Transaction ID 2934679863，找到 1 条匹配记录
2025-06-20 17:05:09,750 - INFO - [processing] ✅ 记录 3662 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,750 - INFO - [processing] 📊 Transaction ID 2934679863 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,750 - INFO - [processing] 🔍 处理Transaction ID 2934680129，找到 1 条匹配记录
2025-06-20 17:05:09,750 - INFO - [processing] ✅ 记录 3660 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,750 - INFO - [processing] 📊 Transaction ID 2934680129 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,750 - INFO - [processing] 🔍 处理Transaction ID 2934684431，找到 1 条匹配记录
2025-06-20 17:05:09,751 - INFO - [processing] ✅ 记录 3658 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,751 - INFO - [processing] 📊 Transaction ID 2934684431 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,751 - INFO - [processing] 🔍 处理Transaction ID 2934687994，找到 1 条匹配记录
2025-06-20 17:05:09,751 - INFO - [processing] ✅ 记录 3655 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,751 - INFO - [processing] 📊 Transaction ID 2934687994 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,751 - INFO - [processing] 🔍 处理Transaction ID 2934688247，找到 1 条匹配记录
2025-06-20 17:05:09,751 - INFO - [processing] ✅ 记录 3654 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,752 - INFO - [processing] 📊 Transaction ID 2934688247 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,752 - INFO - [processing] 🔍 处理Transaction ID 2934690653，找到 1 条匹配记录
2025-06-20 17:05:09,752 - INFO - [processing] ✅ 记录 3651 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,752 - INFO - [processing] 📊 Transaction ID 2934690653 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,752 - INFO - [processing] 🔍 处理Transaction ID 2934694993，找到 1 条匹配记录
2025-06-20 17:05:09,752 - INFO - [processing] ✅ 记录 3649 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,753 - INFO - [processing] 📊 Transaction ID 2934694993 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,753 - INFO - [processing] 🔍 处理Transaction ID 2934695318，找到 1 条匹配记录
2025-06-20 17:05:09,753 - INFO - [processing] ✅ 记录 3648 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,753 - INFO - [processing] 📊 Transaction ID 2934695318 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,753 - INFO - [processing] 🔍 处理Transaction ID 2934700523，找到 1 条匹配记录
2025-06-20 17:05:09,753 - INFO - [processing] ✅ 记录 3645 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,754 - INFO - [processing] 📊 Transaction ID 2934700523 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,754 - INFO - [processing] 🔍 处理Transaction ID 2934700699，找到 1 条匹配记录
2025-06-20 17:05:09,754 - INFO - [processing] ✅ 记录 3644 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,754 - INFO - [processing] 📊 Transaction ID 2934700699 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,755 - INFO - [processing] 🔍 处理Transaction ID 2934721499，找到 1 条匹配记录
2025-06-20 17:05:09,755 - INFO - [processing] ✅ 记录 3635 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,755 - INFO - [processing] 📊 Transaction ID 2934721499 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,755 - INFO - [processing] 🔍 处理Transaction ID 2934725527，找到 1 条匹配记录
2025-06-20 17:05:09,755 - INFO - [processing] ✅ 记录 3633 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,755 - INFO - [processing] 📊 Transaction ID 2934725527 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,755 - INFO - [processing] 🔍 处理Transaction ID 2934725699，找到 1 条匹配记录
2025-06-20 17:05:09,756 - INFO - [processing] ✅ 记录 3632 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,756 - INFO - [processing] 📊 Transaction ID 2934725699 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,756 - INFO - [processing] 🔍 处理Transaction ID 2934730839，找到 1 条匹配记录
2025-06-20 17:05:09,756 - INFO - [processing] ✅ 记录 3629 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,756 - INFO - [processing] 📊 Transaction ID 2934730839 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,756 - INFO - [processing] 🔍 处理Transaction ID 2934740293，找到 1 条匹配记录
2025-06-20 17:05:09,757 - INFO - [processing] ✅ 记录 3628 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,757 - INFO - [processing] 📊 Transaction ID 2934740293 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,757 - INFO - [processing] 🔍 处理Transaction ID 2934745969，找到 1 条匹配记录
2025-06-20 17:05:09,757 - INFO - [processing] ✅ 记录 3627 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,757 - INFO - [processing] 📊 Transaction ID 2934745969 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,757 - INFO - [processing] 🔍 处理Transaction ID 2934746376，找到 1 条匹配记录
2025-06-20 17:05:09,758 - INFO - [processing] ✅ 记录 3626 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,758 - INFO - [processing] 📊 Transaction ID 2934746376 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:09,758 - INFO - [processing] 🔍 处理Transaction ID 2934748746，找到 1 条匹配记录
2025-06-20 17:05:09,758 - INFO - [processing] ✅ 记录 3625 标记设置成功: Matched_Flag = True
2025-06-20 17:05:09,816 - INFO - [processing] 错误: 处理 9_digit 阶段:   1%|[32m▍                                        [0m| 32/3053 [00:00<00:09, 314.73条/s][0m
2025-06-20 17:05:09,916 - INFO - [processing] 错误: 处理 9_digit 阶段:   2%|[32m▉                                        [0m| 67/3053 [00:00<00:08, 334.73条/s][0m
2025-06-20 17:05:10,018 - INFO - [processing] 错误: 处理 9_digit 阶段:   4%|[32m█▍                                      [0m| 109/3053 [00:00<00:07, 373.01条/s][0m
2025-06-20 17:05:10,118 - INFO - [processing] 错误: 处理 9_digit 阶段:   5%|[32m██                                      [0m| 153/3053 [00:00<00:07, 396.61条/s][0m
2025-06-20 17:05:10,219 - INFO - [processing] 错误: 处理 9_digit 阶段:   6%|[32m██▌                                     [0m| 194/3053 [00:00<00:07, 401.29条/s][0m
2025-06-20 17:05:10,321 - INFO - [processing] 错误: 处理 9_digit 阶段:   8%|[32m███                                     [0m| 237/3053 [00:00<00:06, 409.33条/s][0m
2025-06-20 17:05:10,424 - INFO - [processing] 错误: 处理 9_digit 阶段:   9%|[32m███▋                                    [0m| 281/3053 [00:00<00:06, 416.87条/s][0m
2025-06-20 17:05:10,527 - INFO - [processing] 错误: 处理 9_digit 阶段:  11%|[32m████▏                                   [0m| 323/3053 [00:00<00:06, 413.12条/s][0m
2025-06-20 17:05:10,629 - INFO - [processing] 错误: 处理 9_digit 阶段:  12%|[32m████▊                                   [0m| 365/3053 [00:00<00:06, 412.66条/s][0m
2025-06-20 17:05:10,732 - INFO - [processing] 错误: 处理 9_digit 阶段:  13%|[32m█████▍                                  [0m| 412/3053 [00:01<00:06, 427.12条/s][0m
2025-06-20 17:05:10,832 - INFO - [processing] 错误: 处理 9_digit 阶段:  15%|[32m█████▉                                  [0m| 456/3053 [00:01<00:06, 426.67条/s][0m
2025-06-20 17:05:10,932 - INFO - [processing] 错误: 处理 9_digit 阶段:  16%|[32m██████▌                                 [0m| 499/3053 [00:01<00:05, 427.29条/s][0m
2025-06-20 17:05:11,033 - INFO - [processing] 错误: 处理 9_digit 阶段:  18%|[32m███████▏                                [0m| 545/3053 [00:01<00:05, 437.03条/s][0m
2025-06-20 17:05:11,136 - INFO - [processing] 错误: 处理 9_digit 阶段:  19%|[32m███████▋                                [0m| 589/3053 [00:01<00:05, 436.61条/s][0m
2025-06-20 17:05:11,236 - INFO - [processing] 错误: 处理 9_digit 阶段:  21%|[32m████████▎                               [0m| 633/3053 [00:01<00:05, 434.10条/s][0m
2025-06-20 17:05:11,337 - INFO - [processing] 错误: 处理 9_digit 阶段:  22%|[32m████████▊                               [0m| 677/3053 [00:01<00:05, 435.45条/s][0m
2025-06-20 17:05:11,438 - INFO - [processing] 错误: 处理 9_digit 阶段:  24%|[32m█████████▍                              [0m| 721/3053 [00:01<00:05, 435.66条/s][0m
2025-06-20 17:05:11,538 - INFO - [processing] 错误: 处理 9_digit 阶段:  25%|[32m██████████                              [0m| 767/3053 [00:01<00:05, 442.13条/s][0m
2025-06-20 17:05:11,651 - INFO - [processing] 错误: 处理 9_digit 阶段:  27%|[32m██████████▋                             [0m| 813/3053 [00:01<00:05, 447.45条/s][0m
2025-06-20 17:05:11,775 - INFO - [processing] 错误: 处理 9_digit 阶段:  28%|[32m███████████▏                            [0m| 858/3053 [00:02<00:05, 431.82条/s][0m
2025-06-20 17:05:11,877 - INFO - [processing] 错误: 处理 9_digit 阶段:  30%|[32m███████████▊                            [0m| 902/3053 [00:02<00:05, 405.30条/s][0m
2025-06-20 17:05:11,978 - INFO - [processing] 错误: 处理 9_digit 阶段:  31%|[32m████████████▎                           [0m| 944/3053 [00:02<00:05, 407.60条/s][0m
2025-06-20 17:05:12,078 - INFO - [processing] 错误: 处理 9_digit 阶段:  32%|[32m████████████▉                           [0m| 989/3053 [00:02<00:04, 417.94条/s][0m
2025-06-20 17:05:12,185 - INFO - [processing] 错误: 处理 9_digit 阶段:  34%|[32m█████████████▏                         [0m| 1033/3053 [00:02<00:04, 424.20条/s][0m
2025-06-20 17:05:12,285 - INFO - [processing] 错误: 处理 9_digit 阶段:  35%|[32m█████████████▋                         [0m| 1076/3053 [00:02<00:04, 417.70条/s][0m
2025-06-20 17:05:12,387 - INFO - [processing] 错误: 处理 9_digit 阶段:  37%|[32m██████████████▎                        [0m| 1121/3053 [00:02<00:04, 426.73条/s][0m
2025-06-20 17:05:12,492 - INFO - [processing] 错误: 处理 9_digit 阶段:  38%|[32m██████████████▉                        [0m| 1168/3053 [00:02<00:04, 436.98条/s][0m
2025-06-20 17:05:12,607 - INFO - [processing] 错误: 处理 9_digit 阶段:  40%|[32m███████████████▍                       [0m| 1212/3053 [00:02<00:04, 431.28条/s][0m
2025-06-20 17:05:12,711 - INFO - [processing] 错误: 处理 9_digit 阶段:  41%|[32m████████████████                       [0m| 1256/3053 [00:02<00:04, 416.09条/s][0m
2025-06-20 17:05:12,813 - INFO - [processing] 错误: 处理 9_digit 阶段:  43%|[32m████████████████▌                      [0m| 1298/3053 [00:03<00:04, 411.84条/s][0m
2025-06-20 17:05:12,914 - INFO - [processing] 错误: 处理 9_digit 阶段:  44%|[32m█████████████████▏                     [0m| 1344/3053 [00:03<00:04, 423.79条/s][0m
2025-06-20 17:05:13,033 - INFO - [processing] 错误: 处理 9_digit 阶段:  45%|[32m█████████████████▋                     [0m| 1388/3053 [00:03<00:03, 426.91条/s][0m
2025-06-20 17:05:13,150 - INFO - [processing] 错误: 处理 9_digit 阶段:  47%|[32m██████████████████▎                    [0m| 1431/3053 [00:03<00:03, 405.64条/s][0m
2025-06-20 17:05:13,219 - INFO - [processing] 📊 Transaction ID 2934748746 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,220 - INFO - [processing] 🔍 处理Transaction ID 2934783619，找到 1 条匹配记录
2025-06-20 17:05:13,220 - INFO - [processing] ✅ 记录 3620 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,220 - INFO - [processing] 📊 Transaction ID 2934783619 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,220 - INFO - [processing] 🔍 处理Transaction ID 2934794651，找到 1 条匹配记录
2025-06-20 17:05:13,220 - INFO - [processing] ✅ 记录 3619 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,220 - INFO - [processing] 📊 Transaction ID 2934794651 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,220 - INFO - [processing] 🔍 处理Transaction ID 2934799727，找到 1 条匹配记录
2025-06-20 17:05:13,221 - INFO - [processing] ✅ 记录 3618 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,221 - INFO - [processing] 📊 Transaction ID 2934799727 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,221 - INFO - [processing] 🔍 处理Transaction ID 2934811070，找到 1 条匹配记录
2025-06-20 17:05:13,221 - INFO - [processing] ✅ 记录 3616 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,221 - INFO - [processing] 📊 Transaction ID 2934811070 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,221 - INFO - [processing] 🔍 处理Transaction ID 2934817618，找到 1 条匹配记录
2025-06-20 17:05:13,221 - INFO - [processing] ✅ 记录 3615 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,221 - INFO - [processing] 📊 Transaction ID 2934817618 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,221 - INFO - [processing] 🔍 处理Transaction ID 2934822550，找到 1 条匹配记录
2025-06-20 17:05:13,222 - INFO - [processing] ✅ 记录 3612 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,222 - INFO - [processing] 📊 Transaction ID 2934822550 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,222 - INFO - [processing] 🔍 处理Transaction ID 2934823248，找到 1 条匹配记录
2025-06-20 17:05:13,222 - INFO - [processing] ✅ 记录 3611 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,222 - INFO - [processing] 📊 Transaction ID 2934823248 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,222 - INFO - [processing] 🔍 处理Transaction ID 2934823329，找到 1 条匹配记录
2025-06-20 17:05:13,222 - INFO - [processing] ✅ 记录 3610 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,222 - INFO - [processing] 📊 Transaction ID 2934823329 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,222 - INFO - [processing] 🔍 处理Transaction ID 2934831858，找到 1 条匹配记录
2025-06-20 17:05:13,223 - INFO - [processing] ✅ 记录 3609 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,223 - INFO - [processing] 📊 Transaction ID 2934831858 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,223 - INFO - [processing] 🔍 处理Transaction ID 2934833412，找到 1 条匹配记录
2025-06-20 17:05:13,223 - INFO - [processing] ✅ 记录 3608 标记设置成功: Matched_Flag = True
2025-06-20 17:05:13,223 - INFO - [processing] 📊 Transaction ID 2934833412 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934843459 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934846962 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934848051 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934850929 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934857201 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934859973 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,224 - INFO - [processing] 📊 Transaction ID 2934863003 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,225 - INFO - [processing] 📊 Transaction ID 2934863120 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,225 - INFO - [processing] 📊 Transaction ID 2934864072 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,225 - INFO - [processing] 📊 Transaction ID 2934865030 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,225 - INFO - [processing] 📊 Transaction ID 2934865049 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,225 - INFO - [processing] 📊 Transaction ID 2934867866 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,225 - INFO - [processing] 📊 Transaction ID 2934868632 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,226 - INFO - [processing] 📊 Transaction ID 2934872955 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,226 - INFO - [processing] 📊 Transaction ID 2934874787 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,226 - INFO - [processing] 📊 Transaction ID 2934878982 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,226 - INFO - [processing] 📊 Transaction ID 2934882977 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,226 - INFO - [processing] 📊 Transaction ID 2934887624 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,227 - INFO - [processing] 📊 Transaction ID 2934887811 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,227 - INFO - [processing] 📊 Transaction ID 2934889609 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,227 - INFO - [processing] 📊 Transaction ID 2934901166 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,227 - INFO - [processing] 📊 Transaction ID 2934902151 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,227 - INFO - [processing] 📊 Transaction ID 2934903407 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,228 - INFO - [processing] 📊 Transaction ID 2934903487 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,228 - INFO - [processing] 📊 Transaction ID 2934904462 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,228 - INFO - [processing] 📊 Transaction ID 2934905382 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,228 - INFO - [processing] 📊 Transaction ID 2934907015 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934907371 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934915102 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934915722 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934916061 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934918859 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934920436 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,229 - INFO - [processing] 📊 Transaction ID 2934920907 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,230 - INFO - [processing] 📊 Transaction ID 2934922202 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,230 - INFO - [processing] 📊 Transaction ID 2934925788 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,230 - INFO - [processing] 📊 Transaction ID 2934926749 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,230 - INFO - [processing] 📊 Transaction ID 2934928217 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,230 - INFO - [processing] 📊 Transaction ID 2934930913 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934932122 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934933896 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934936469 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934936580 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934937195 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934938766 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,231 - INFO - [processing] 📊 Transaction ID 2934939110 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,232 - INFO - [processing] 📊 Transaction ID 2934939468 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,232 - INFO - [processing] 📊 Transaction ID 2934939714 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,232 - INFO - [processing] 📊 Transaction ID 2934940660 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,232 - INFO - [processing] 📊 Transaction ID 2934943109 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:13,232 - INFO - [processing] 🔍 总体进度: 已处理 100 条匹配记录
2025-06-20 17:05:13,233 - INFO - [processing] 🔍 实时标记验证: True=100, False=3595, NaN=0, 总计=3695
2025-06-20 17:05:13,233 - INFO - [processing] 🔍 总体进度: 已处理 200 条匹配记录
2025-06-20 17:05:13,233 - INFO - [processing] 🔍 实时标记验证: True=200, False=3495, NaN=0, 总计=3695
2025-06-20 17:05:13,233 - INFO - [processing] 🔍 总体进度: 已处理 300 条匹配记录
2025-06-20 17:05:13,233 - INFO - [processing] 🔍 实时标记验证: True=300, False=3395, NaN=0, 总计=3695
2025-06-20 17:05:13,233 - INFO - [processing] 🔍 总体进度: 已处理 400 条匹配记录
2025-06-20 17:05:13,234 - INFO - [processing] 🔍 实时标记验证: True=400, False=3295, NaN=0, 总计=3695
2025-06-20 17:05:13,234 - INFO - [processing] 🔍 总体进度: 已处理 500 条匹配记录
2025-06-20 17:05:13,234 - INFO - [processing] 🔍 实时标记验证: True=500, False=3195, NaN=0, 总计=3695
2025-06-20 17:05:13,234 - INFO - [processing] 🔍 总体进度: 已处理 600 条匹配记录
2025-06-20 17:05:13,234 - INFO - [processing] 🔍 实时标记验证: True=600, False=3095, NaN=0, 总计=3695
2025-06-20 17:05:13,235 - INFO - [processing] 🔍 总体进度: 已处理 700 条匹配记录
2025-06-20 17:05:13,235 - INFO - [processing] 🔍 实时标记验证: True=700, False=2995, NaN=0, 总计=3695
2025-06-20 17:05:13,235 - INFO - [processing] 🔍 总体进度: 已处理 800 条匹配记录
2025-06-20 17:05:13,235 - INFO - [processing] 🔍 实时标记验证: True=800, False=2895, NaN=0, 总计=3695
2025-06-20 17:05:13,235 - INFO - [processing] 🔍 总体进度: 已处理 900 条匹配记录
2025-06-20 17:05:13,236 - INFO - [processing] 🔍 实时标记验证: True=900, False=2795, NaN=0, 总计=3695
2025-06-20 17:05:13,236 - INFO - [processing] 🔍 总体进度: 已处理 1000 条匹配记录
2025-06-20 17:05:13,236 - INFO - [processing] 🔍 实时标记验证: True=1000, False=2695, NaN=0, 总计=3695
2025-06-20 17:05:13,236 - INFO - [processing] 🔍 总体进度: 已处理 1100 条匹配记录
2025-06-20 17:05:13,236 - INFO - [processing] 🔍 实时标记验证: True=1100, False=2595, NaN=0, 总计=3695
2025-06-20 17:05:13,236 - INFO - [processing] 🔍 总体进度: 已处理 1200 条匹配记录
2025-06-20 17:05:13,237 - INFO - [processing] 🔍 实时标记验证: True=1200, False=2495, NaN=0, 总计=3695
2025-06-20 17:05:13,237 - INFO - [processing] 🔍 总体进度: 已处理 1300 条匹配记录
2025-06-20 17:05:13,237 - INFO - [processing] 🔍 实时标记验证: True=1300, False=2395, NaN=0, 总计=3695
2025-06-20 17:05:13,237 - INFO - [processing] 🔍 总体进度: 已处理 1400 条匹配记录
2025-06-20 17:05:13,237 - INFO - [processing] 🔍 实时标记验证: True=1400, False=2295, NaN=0, 总计=3695
2025-06-20 17:05:13,237 - INFO - [processing] ❌ Transaction ID 2935687567 在第二文件中未找到匹配记录
2025-06-20 17:05:13,238 - INFO - [processing] 🔍 插入新记录: Order ID=603010897, Amount=RM15.00, Phase=9_digit, Transaction ID=2935687567, Mode=Transaction ID
2025-06-20 17:05:13,238 - INFO - [processing] 🔍 Transaction同步插入: Transaction ID=2935687567, Order ID=603010897, Amount=RM15.00, Phase=9_digit
2025-06-20 17:05:13,238 - INFO - [processing] 📝 执行标准插入: Phase=9_digit, Order ID=603010897
2025-06-20 17:05:13,238 - INFO - [processing] 🔍 插入前标记数量: 1434
2025-06-20 17:05:13,238 - INFO - [processing] 🔍 插入后标记数量: 1435
2025-06-20 17:05:13,239 - INFO - [processing] ✅ 插入操作正常: 标记数量从1434增加到1435
2025-06-20 17:05:13,239 - INFO - [processing] 🔍 总体进度: 已处理 1500 条匹配记录
2025-06-20 17:05:13,239 - INFO - [processing] 🔍 实时标记验证: True=1501, False=2195, NaN=0, 总计=3696
2025-06-20 17:05:13,239 - INFO - [processing] 🚨 标记不一致警告: matched_indices=1500, 实际标记=1501
2025-06-20 17:05:13,239 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,239 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,240 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,240 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,240 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,240 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,240 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,240 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,241 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:13,258 - INFO - [processing] 错误: 处理 9_digit 阶段:  48%|[32m██████████████████▊                    [0m| 1472/3053 [00:03<00:04, 388.42条/s][0m
2025-06-20 17:05:13,362 - INFO - [processing] 错误: 处理 9_digit 阶段:  50%|[32m███████████████████▎                   [0m| 1512/3053 [00:03<00:04, 382.66条/s][0m
2025-06-20 17:05:13,467 - INFO - [processing] 错误: 处理 9_digit 阶段:  51%|[32m███████████████████▊                   [0m| 1554/3053 [00:03<00:03, 389.55条/s][0m
2025-06-20 17:05:13,572 - INFO - [processing] 错误: 处理 9_digit 阶段:  52%|[32m████████████████████▎                  [0m| 1594/3053 [00:03<00:03, 386.71条/s][0m
2025-06-20 17:05:13,673 - INFO - [processing] 错误: 处理 9_digit 阶段:  53%|[32m████████████████████▊                  [0m| 1633/3053 [00:03<00:03, 381.78条/s][0m
2025-06-20 17:05:13,775 - INFO - [processing] 错误: 处理 9_digit 阶段:  55%|[32m█████████████████████▍                 [0m| 1680/3053 [00:04<00:03, 405.37条/s][0m
2025-06-20 17:05:13,877 - INFO - [processing] 错误: 处理 9_digit 阶段:  57%|[32m██████████████████████                 [0m| 1726/3053 [00:04<00:03, 419.32条/s][0m
2025-06-20 17:05:13,978 - INFO - [processing] 错误: 处理 9_digit 阶段:  58%|[32m██████████████████████▋                [0m| 1773/3053 [00:04<00:02, 432.12条/s][0m
2025-06-20 17:05:14,090 - INFO - [processing] 错误: 处理 9_digit 阶段:  60%|[32m███████████████████████▏               [0m| 1820/3053 [00:04<00:02, 441.44条/s][0m
2025-06-20 17:05:14,191 - INFO - [processing] 错误: 处理 9_digit 阶段:  61%|[32m███████████████████████▊               [0m| 1865/3053 [00:04<00:02, 429.27条/s][0m
2025-06-20 17:05:14,298 - INFO - [processing] 错误: 处理 9_digit 阶段:  63%|[32m████████████████████████▍              [0m| 1911/3053 [00:04<00:02, 436.66条/s][0m
2025-06-20 17:05:14,399 - INFO - [processing] 错误: 处理 9_digit 阶段:  64%|[32m████████████████████████▉              [0m| 1955/3053 [00:04<00:02, 428.99条/s][0m
2025-06-20 17:05:14,501 - INFO - [processing] 错误: 处理 9_digit 阶段:  66%|[32m█████████████████████████▌             [0m| 2001/3053 [00:04<00:02, 436.07条/s][0m
2025-06-20 17:05:14,602 - INFO - [processing] 错误: 处理 9_digit 阶段:  67%|[32m██████████████████████████▏            [0m| 2047/3053 [00:04<00:02, 440.55条/s][0m
2025-06-20 17:05:14,709 - INFO - [processing] 错误: 处理 9_digit 阶段:  69%|[32m██████████████████████████▋            [0m| 2093/3053 [00:04<00:02, 445.75条/s][0m
2025-06-20 17:05:14,814 - INFO - [processing] 错误: 处理 9_digit 阶段:  70%|[32m███████████████████████████▎           [0m| 2138/3053 [00:05<00:02, 437.49条/s][0m
2025-06-20 17:05:14,927 - INFO - [processing] 错误: 处理 9_digit 阶段:  71%|[32m███████████████████████████▊           [0m| 2182/3053 [00:05<00:02, 431.25条/s][0m
2025-06-20 17:05:15,031 - INFO - [processing] 错误: 处理 9_digit 阶段:  73%|[32m████████████████████████████▍          [0m| 2226/3053 [00:05<00:01, 419.75条/s][0m
2025-06-20 17:05:15,132 - INFO - [processing] 错误: 处理 9_digit 阶段:  74%|[32m████████████████████████████▉          [0m| 2269/3053 [00:05<00:01, 417.23条/s][0m
2025-06-20 17:05:15,233 - INFO - [processing] 错误: 处理 9_digit 阶段:  76%|[32m█████████████████████████████▌         [0m| 2316/3053 [00:05<00:01, 430.28条/s][0m
2025-06-20 17:05:15,318 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,318 - INFO - [processing] 🔍 总体进度: 已处理 1600 条匹配记录
2025-06-20 17:05:15,318 - INFO - [processing] 🔍 实时标记验证: True=1601, False=2095, NaN=0, 总计=3696
2025-06-20 17:05:15,319 - INFO - [processing] 🚨 标记不一致警告: matched_indices=1600, 实际标记=1601
2025-06-20 17:05:15,319 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,319 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,319 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,319 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,320 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,320 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,320 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,320 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,320 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 总体进度: 已处理 1700 条匹配记录
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 实时标记验证: True=1701, False=1995, NaN=0, 总计=3696
2025-06-20 17:05:15,321 - INFO - [processing] 🚨 标记不一致警告: matched_indices=1700, 实际标记=1701
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,321 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 总体进度: 已处理 1800 条匹配记录
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 实时标记验证: True=1801, False=1895, NaN=0, 总计=3696
2025-06-20 17:05:15,322 - INFO - [processing] 🚨 标记不一致警告: matched_indices=1800, 实际标记=1801
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,322 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,323 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,323 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,323 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,323 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,324 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,324 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,324 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,324 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,324 - INFO - [processing] 🔍 总体进度: 已处理 1900 条匹配记录
2025-06-20 17:05:15,324 - INFO - [processing] 🔍 实时标记验证: True=1901, False=1795, NaN=0, 总计=3696
2025-06-20 17:05:15,324 - INFO - [processing] 🚨 标记不一致警告: matched_indices=1900, 实际标记=1901
2025-06-20 17:05:15,325 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,325 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,325 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,325 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,325 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,326 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,326 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,326 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,326 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,326 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,327 - INFO - [processing] 🔍 总体进度: 已处理 2000 条匹配记录
2025-06-20 17:05:15,327 - INFO - [processing] 🔍 实时标记验证: True=2001, False=1695, NaN=0, 总计=3696
2025-06-20 17:05:15,327 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2000, 实际标记=2001
2025-06-20 17:05:15,327 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,327 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,327 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,328 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,328 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,328 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,328 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,328 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,328 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,329 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,329 - INFO - [processing] 🔍 总体进度: 已处理 2100 条匹配记录
2025-06-20 17:05:15,329 - INFO - [processing] 🔍 实时标记验证: True=2101, False=1595, NaN=0, 总计=3696
2025-06-20 17:05:15,330 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2100, 实际标记=2101
2025-06-20 17:05:15,330 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,330 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,330 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,330 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,331 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,331 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,331 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,331 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,332 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,332 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,332 - INFO - [processing] 🔍 总体进度: 已处理 2200 条匹配记录
2025-06-20 17:05:15,332 - INFO - [processing] 🔍 实时标记验证: True=2201, False=1495, NaN=0, 总计=3696
2025-06-20 17:05:15,332 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2200, 实际标记=2201
2025-06-20 17:05:15,333 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,333 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,333 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,334 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,334 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,334 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,334 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,335 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,335 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,335 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,335 - INFO - [processing] 🔍 总体进度: 已处理 2300 条匹配记录
2025-06-20 17:05:15,335 - INFO - [processing] 🔍 实时标记验证: True=2301, False=1395, NaN=0, 总计=3696
2025-06-20 17:05:15,335 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2300, 实际标记=2301
2025-06-20 17:05:15,335 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,336 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,336 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,336 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,336 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,336 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,337 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,337 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,337 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,337 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,337 - INFO - [processing] 🔍 总体进度: 已处理 2400 条匹配记录
2025-06-20 17:05:15,338 - INFO - [processing] 🔍 实时标记验证: True=2401, False=1295, NaN=0, 总计=3696
2025-06-20 17:05:15,338 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2400, 实际标记=2401
2025-06-20 17:05:15,338 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,338 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,338 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,338 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:15,340 - INFO - [processing] 错误: 处理 9_digit 阶段:  77%|[32m██████████████████████████████▏        [0m| 2361/3053 [00:05<00:01, 435.53条/s][0m
2025-06-20 17:05:15,441 - INFO - [processing] 错误: 处理 9_digit 阶段:  79%|[32m██████████████████████████████▋        [0m| 2405/3053 [00:05<00:01, 427.06条/s][0m
2025-06-20 17:05:15,543 - INFO - [processing] 错误: 处理 9_digit 阶段:  80%|[32m███████████████████████████████▎       [0m| 2449/3053 [00:05<00:01, 429.62条/s][0m
2025-06-20 17:05:15,640 - INFO - [processing] 错误: 处理 9_digit 阶段:  82%|[32m███████████████████████████████▊       [0m| 2494/3053 [00:05<00:01, 434.14条/s][0m
2025-06-20 17:05:15,656 - INFO - [processing] 错误: 处理 over_9 阶段:  83%|[32m█████████████████████████████████▏      [0m| 2532/3053 [00:06<00:01, 434.14条/s][0m
2025-06-20 17:05:15,775 - INFO - [processing] 错误: 处理 over_9 阶段:  83%|[32m█████████████████████████████████▎      [0m| 2538/3053 [00:06<00:01, 419.56条/s][0m
2025-06-20 17:05:15,914 - INFO - [processing] 错误: 处理 over_9 阶段:  85%|[32m█████████████████████████████████▊      [0m| 2581/3053 [00:06<00:01, 400.11条/s][0m
2025-06-20 17:05:16,037 - INFO - [processing] 错误: 处理 over_9 阶段:  86%|[32m██████████████████████████████████▎     [0m| 2622/3053 [00:06<00:01, 363.53条/s][0m
2025-06-20 17:05:16,141 - INFO - [processing] 错误: 处理 over_9 阶段:  87%|[32m██████████████████████████████████▊     [0m| 2660/3053 [00:06<00:01, 346.76条/s][0m
2025-06-20 17:05:16,244 - INFO - [processing] 错误: 处理 over_9 阶段:  88%|[32m███████████████████████████████████▎    [0m| 2696/3053 [00:06<00:01, 346.59条/s][0m
2025-06-20 17:05:16,390 - INFO - [processing] 错误: 处理 over_9 阶段:  89%|[32m███████████████████████████████████▊    [0m| 2732/3053 [00:06<00:00, 346.95条/s][0m
2025-06-20 17:05:16,508 - INFO - [processing] 错误: 处理 over_9 阶段:  91%|[32m████████████████████████████████████▎   [0m| 2767/3053 [00:06<00:00, 308.67条/s][0m
2025-06-20 17:05:16,633 - INFO - [processing] 错误: 处理 over_9 阶段:  92%|[32m████████████████████████████████████▋   [0m| 2799/3053 [00:06<00:00, 297.56条/s][0m
2025-06-20 17:05:16,734 - INFO - [processing] 错误: 处理 over_9 阶段:  93%|[32m█████████████████████████████████████   [0m| 2830/3053 [00:07<00:00, 282.39条/s][0m
2025-06-20 17:05:16,836 - INFO - [processing] 错误: 处理 over_9 阶段:  94%|[32m█████████████████████████████████████▌  [0m| 2865/3053 [00:07<00:00, 299.25条/s][0m
2025-06-20 17:05:16,947 - INFO - [processing] 错误: 处理 over_9 阶段:  95%|[32m█████████████████████████████████████▉  [0m| 2899/3053 [00:07<00:00, 308.36条/s][0m
2025-06-20 17:05:17,049 - INFO - [processing] 错误: 处理 over_9 阶段:  96%|[32m██████████████████████████████████████▍ [0m| 2931/3053 [00:07<00:00, 302.34条/s][0m
2025-06-20 17:05:17,149 - INFO - [processing] 错误: 处理 over_9 阶段:  97%|[32m██████████████████████████████████████▊ [0m| 2963/3053 [00:07<00:00, 305.73条/s][0m
2025-06-20 17:05:17,237 - INFO - [processing] 错误: 处理 over_9 阶段:  98%|[32m███████████████████████████████████████▏[0m| 2994/3053 [00:07<00:00, 306.89条/s][0m
2025-06-20 17:05:17,254 - INFO - [processing] 错误: 处理 anomaly 阶段:  99%|[32m██████████████████████████████████████▌[0m| 3022/3053 [00:07<00:00, 306.89条/s][0m
2025-06-20 17:05:17,327 - INFO - [processing] 错误: 处理 anomaly 阶段:  99%|[32m██████████████████████████████████████▋[0m| 3025/3053 [00:07<00:00, 303.74条/s][0m
2025-06-20 17:05:17,339 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,339 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,339 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,340 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,340 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,340 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,340 - INFO - [processing] 🔍 总体进度: 已处理 2500 条匹配记录
2025-06-20 17:05:17,341 - INFO - [processing] 🔍 实时标记验证: True=2501, False=1195, NaN=0, 总计=3696
2025-06-20 17:05:17,341 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2500, 实际标记=2501
2025-06-20 17:05:17,341 - INFO - [processing] 🔍 样本检查: 索引3683 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,341 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,341 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,341 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,342 - INFO - [processing] ❌ Transaction ID 2935120010 在第二文件中未找到匹配记录
2025-06-20 17:05:17,342 - INFO - [processing] 🔍 插入新记录: Order ID=2025061611451934457295567675392, Amount=RM5.00, Phase=over_9, Transaction ID=2935120010, Mode=Transaction ID
2025-06-20 17:05:17,343 - INFO - [processing] 🔍 Transaction同步插入: Transaction ID=2935120010, Order ID=2025061611451934457295567675392, Amount=RM5.00, Phase=over_9
2025-06-20 17:05:17,343 - INFO - [processing] ⚠️ 备份中找不到Order No. 2025061611451934457295567675392，使用标准插入
2025-06-20 17:05:17,343 - INFO - [processing] 📝 执行标准插入: Phase=over_9, Order ID=2025061611451934457295567675392
2025-06-20 17:05:17,343 - INFO - [processing] 🔍 插入前标记数量: 2587
2025-06-20 17:05:17,343 - INFO - [processing] 🔍 插入后标记数量: 2588
2025-06-20 17:05:17,343 - INFO - [processing] ✅ 插入操作正常: 标记数量从2587增加到2588
2025-06-20 17:05:17,343 - INFO - [processing] ❌ Transaction ID 2935122132 在第二文件中未找到匹配记录
2025-06-20 17:05:17,343 - INFO - [processing] 🔍 插入新记录: Order ID=2025061611461934457634563907584, Amount=RM5.00, Phase=over_9, Transaction ID=2935122132, Mode=Transaction ID
2025-06-20 17:05:17,344 - INFO - [processing] 🔍 Transaction同步插入: Transaction ID=2935122132, Order ID=2025061611461934457634563907584, Amount=RM5.00, Phase=over_9
2025-06-20 17:05:17,344 - INFO - [processing] ⚠️ 备份中找不到Order No. 2025061611461934457634563907584，使用标准插入
2025-06-20 17:05:17,344 - INFO - [processing] 📝 执行标准插入: Phase=over_9, Order ID=2025061611461934457634563907584
2025-06-20 17:05:17,344 - INFO - [processing] 🔍 插入前标记数量: 2589
2025-06-20 17:05:17,344 - INFO - [processing] 🔍 插入后标记数量: 2590
2025-06-20 17:05:17,344 - INFO - [processing] ✅ 插入操作正常: 标记数量从2589增加到2590
2025-06-20 17:05:17,344 - INFO - [processing] 🔍 总体进度: 已处理 2600 条匹配记录
2025-06-20 17:05:17,344 - INFO - [processing] 🔍 实时标记验证: True=2603, False=1095, NaN=0, 总计=3698
2025-06-20 17:05:17,345 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2600, 实际标记=2603
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3689 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,345 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 总体进度: 已处理 2700 条匹配记录
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 实时标记验证: True=2703, False=995, NaN=0, 总计=3698
2025-06-20 17:05:17,346 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2700, 实际标记=2703
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,346 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3689 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 总体进度: 已处理 2800 条匹配记录
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 实时标记验证: True=2803, False=895, NaN=0, 总计=3698
2025-06-20 17:05:17,347 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2800, 实际标记=2803
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,347 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,348 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,348 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,348 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,348 - INFO - [processing] 🔍 样本检查: 索引3689 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,348 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,348 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,349 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,349 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,349 - INFO - [processing] 🔍 总体进度: 已处理 2900 条匹配记录
2025-06-20 17:05:17,349 - INFO - [processing] 🔍 实时标记验证: True=2903, False=795, NaN=0, 总计=3698
2025-06-20 17:05:17,350 - INFO - [processing] 🚨 标记不一致警告: matched_indices=2900, 实际标记=2903
2025-06-20 17:05:17,350 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,350 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,350 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,350 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,350 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,350 - INFO - [processing] 🔍 样本检查: 索引3689 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 总体进度: 已处理 3000 条匹配记录
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 实时标记验证: True=3003, False=695, NaN=0, 总计=3698
2025-06-20 17:05:17,351 - INFO - [processing] 🚨 标记不一致警告: matched_indices=3000, 实际标记=3003
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3684 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3685 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,351 - INFO - [processing] 🔍 样本检查: 索引3686 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3687 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3688 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3689 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3691 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3692 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3693 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] 🔍 样本检查: 索引3694 -> Matched_Flag=True (类型: <class 'bool'>)
2025-06-20 17:05:17,352 - INFO - [processing] ✅ 处理完成，耗时: 7.75秒
2025-06-20 17:05:17,352 - INFO - [processing] 📊 transaction_id 模式统计:
2025-06-20 17:05:17,352 - INFO - [processing] 处理: 3053 条
2025-06-20 17:05:17,353 - INFO - [processing] 匹配: 3019 条
2025-06-20 17:05:17,353 - INFO - [processing] 插入: 34 条
2025-06-20 17:05:17,353 - INFO - [processing] ✅ 模块化数据处理完成
2025-06-20 17:05:17,353 - INFO - [processing] 🔍 验证 - 实际匹配的记录数量: 3019
2025-06-20 17:05:17,353 - INFO - [processing] 🔍 验证 - DataFrame中实际标记数量: 3053
2025-06-20 17:05:17,353 - INFO - [processing] 🚨 严重警告：matched_indices数量与DataFrame标记数量不符！
2025-06-20 17:05:17,353 - INFO - [processing] matched_indices: 3019
2025-06-20 17:05:17,353 - INFO - [processing] DataFrame标记: 3053
2025-06-20 17:05:17,354 - INFO - [processing] 差异: -34
2025-06-20 17:05:17,354 - INFO - [processing] 🔍 调试 - Matched_Flag标记统计:
2025-06-20 17:05:17,354 - INFO - [processing] 总记录数: 3729
2025-06-20 17:05:17,354 - INFO - [processing] 已标记记录数: 3053
2025-06-20 17:05:17,354 - INFO - [processing] 未标记记录数: 676
2025-06-20 17:05:17,354 - INFO - [processing] 🔍 调试 - 按状态分组的Matched_Flag统计:
2025-06-20 17:05:17,354 - INFO - [processing] Matched_Flag  False  True
2025-06-20 17:05:17,354 - INFO - [processing] Order status
2025-06-20 17:05:17,354 - INFO - [processing] Close           444      0
2025-06-20 17:05:17,355 - INFO - [processing] Finish           86   3053
2025-06-20 17:05:17,355 - INFO - [processing] Refunded        145      0
2025-06-20 17:05:17,355 - INFO - [processing] Refunding         1      0
2025-06-20 17:05:17,355 - INFO - [processing] 🔍 调试 - 插入记录统计:
2025-06-20 17:05:20,005 - INFO - [processing] 错误: 处理 anomaly 阶段: 100%|[32m███████████████████████████████████████[0m| 3053/3053 [00:07<00:00, 395.82条/s][0m
2025-06-20 17:05:20,005 - INFO - [processing] 插入记录数: 34
2025-06-20 17:05:20,005 - INFO - [processing] 错误: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py:2136: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`
2025-06-20 17:05:20,005 - INFO - [processing] 插入记录总金额: RM355.00
2025-06-20 17:05:20,005 - INFO - [processing] 错误: df2["Matched_Flag"] = df2["Matched_Flag"].fillna(False).astype(bool)
2025-06-20 17:05:20,005 - INFO - [processing] 平均每条记录金额: RM10.44
2025-06-20 17:05:20,006 - INFO - [processing] 前5条插入记录:
2025-06-20 17:05:20,006 - INFO - [processing] 1. Transaction ID: 2935687567, Order ID: 603010897, Amount: RM15.00
2025-06-20 17:05:20,006 - INFO - [processing] 2. Transaction ID: 2935120010, Order ID: 2025061611451934457295567675392, Amount: RM5.00
2025-06-20 17:05:20,006 - INFO - [processing] 3. Transaction ID: 2935122132, Order ID: 2025061611461934457634563907584, Amount: RM5.00
2025-06-20 17:05:20,006 - INFO - [processing] 4. Transaction ID: 2934893880, Order ID: PAY20250616010618739172, Amount: RM10.00
2025-06-20 17:05:20,006 - INFO - [processing] 5. Transaction ID: 2934951404, Order ID: PAY20250616010659312544, Amount: RM10.00
2025-06-20 17:05:20,006 - INFO - [processing] 🔍 调试 - 金额差异分析:
2025-06-20 17:05:20,006 - INFO - [processing] 第一文件总金额（包含所有settled记录）: RM22681.10
2025-06-20 17:05:20,006 - INFO - [processing] 第一文件没有Order types列，无法检查API订单
2025-06-20 17:05:20,007 - INFO - [processing] 第二文件总金额（排除API订单）: RM22681.10
2025-06-20 17:05:20,007 - INFO - [processing] 金额差异（第一文件无法排除API订单）: RM0.00
2025-06-20 17:05:20,007 - INFO - [processing] Transaction ID匹配: 3104条
2025-06-20 17:05:20,007 - INFO - [processing] Transaction ID插入: 42条
2025-06-20 17:05:20,007 - INFO - [processing] 理论上应该完全匹配，差异应该为0
2025-06-20 17:05:20,007 - INFO - [processing] 执行数据恢复和补全...
2025-06-20 17:05:20,007 - INFO - [processing] 🔍 调试 - 数据恢复前已标记记录数: 3053
2025-06-20 17:05:20,007 - INFO - [processing] 🔄 开始执行数据恢复...
2025-06-20 17:05:20,007 - INFO - [processing] ✅ Transaction Num修复: 0 条
2025-06-20 17:05:20,007 - INFO - [processing] ✅ Equipment信息恢复: 1 条
2025-06-20 17:05:20,007 - INFO - [processing] ✅ Order No.信息恢复: 0 条
2025-06-20 17:05:20,008 - INFO - [processing] ⚠️ 发现 2 个问题:
2025-06-20 17:05:20,008 - INFO - [processing] 🔴 错误: Order No. 2025061611451934457295567675392 在备份数据中找不到对应记录
2025-06-20 17:05:20,008 - INFO - [processing] 🔴 错误: Order No. 2025061611461934457634563907584 在备份数据中找不到对应记录
2025-06-20 17:05:20,008 - INFO - [processing] 🔍 调试 - 数据恢复后已标记记录数: 3053
2025-06-20 17:05:20,008 - INFO - [processing] 🔍 调试 - 处理Matched_Flag前的统计:
2025-06-20 17:05:20,008 - INFO - [processing] True: 3053
2025-06-20 17:05:20,008 - INFO - [processing] False: 676
2025-06-20 17:05:20,008 - INFO - [processing] NaN: 0
2025-06-20 17:05:20,008 - INFO - [processing] 🔍 调试 - 处理Matched_Flag后的统计:
2025-06-20 17:05:20,008 - INFO - [processing] True: 3053
2025-06-20 17:05:20,008 - INFO - [processing] False: 676
2025-06-20 17:05:20,008 - INFO - [processing] NaN: 0
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - 第二文件Order status分布:
2025-06-20 17:05:20,009 - INFO - [processing] 'Finish': 3139 条
2025-06-20 17:05:20,009 - INFO - [processing] 'Close': 444 条
2025-06-20 17:05:20,009 - INFO - [processing] 'Refunded': 145 条
2025-06-20 17:05:20,009 - INFO - [processing] 'Refunding': 1 条
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - 第二文件总记录数: 3729
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - finish状态记录数: 3139
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - 第二文件API订单数量: 86
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - 第二文件API订单总金额: RM695.00
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - 第二文件finish状态总金额（包含API）: RM23376.10
2025-06-20 17:05:20,009 - INFO - [processing] 🔍 调试 - 排除API订单后记录数: 3053
2025-06-20 17:05:20,010 - INFO - [processing] 🔍 调试 - df2_after最终记录数: 3053
2025-06-20 17:05:20,010 - INFO - [processing] 🔍 调试 - df2_after总金额: RM22681.10
2025-06-20 17:05:20,010 - INFO - [processing] Transaction ID匹配模式：金额差异在可接受范围内，跳过自动修正
2025-06-20 17:05:20,010 - INFO - [processing] Transaction ID匹配模式：数据已通过Transaction ID同步，无需自动修正
2025-06-20 17:05:20,011 - INFO - [processing] 处理完成！结果已写入 C:/Users/<USER>/Desktop/June/IOT/160625 CHINA IOT.xlsx
2025-06-20 17:05:20,011 - INFO - [processing] - 数据已写入 DATA sheet
2025-06-20 17:05:20,011 - INFO - [processing] - 日志已写入 LOG sheet
2025-06-20 17:05:20,011 - INFO - [processing] 第二文件最终总金额: RM22681.10
2025-06-20 17:05:20,011 - INFO - [processing] 第一文件总金额: RM22681.10
2025-06-20 17:05:20,011 - INFO - [processing] 金额差异: RM0.00
2025-06-20 17:05:20,011 - INFO - [processing] ✓ 金额匹配成功！
2025-06-20 17:05:20,012 - INFO - [processing] 使用指定的文件路径:
2025-06-20 17:05:20,012 - INFO - [processing] 第一文件: C:/Users/<USER>/Desktop/June/IOT/TRANSACTION_LISTING_FROM_DATE_16062025_TO_16062025_zeroiot.xlsx
2025-06-20 17:05:20,012 - INFO - [processing] 第二文件: C:/Users/<USER>/Desktop/June/IOT/160625 CHINA IOT.xlsx
2025-06-20 17:05:20,012 - INFO - [processing] Sheet名称: transaction
2025-06-20 17:05:20,012 - INFO - [processing] 脚本执行完成
2025-06-20 17:05:20,278 - INFO - [processing] 文件处理成功完成
2025-06-20 17:05:34,285 - INFO - [processing] 已选择第一文件: TRANSACTION_LISTING_FROM_DATE_16062025_TO_16062025_zeropowerstatio.xlsx
2025-06-20 17:05:36,988 - INFO - [processing] 已选择第二文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:05:38,407 - INFO - [processing] 开始处理文件...
2025-06-20 17:05:38,408 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-20 17:05:38,408 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-20 17:05:38,409 - INFO - [processing] 使用指定的Sheet名称: transaction
2025-06-20 17:05:39,158 - INFO - [processing] 使用第一文件路径: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_LISTING_FROM_DATE_16062025_TO_16062025_zeropowerstatio.xlsx
2025-06-20 17:05:39,158 - INFO - [processing] 使用第二文件路径: C:/Users/<USER>/Desktop/June/ZERO/160625 CHINA ZERO.xlsx
2025-06-20 17:05:39,158 - INFO - [processing] 使用sheet: transaction
2025-06-20 17:05:39,158 - INFO - [processing] 第一文件读取的列名： ['Date', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-20 17:05:39,159 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/47 [00:00<?, ?条/s][0m
2025-06-20 17:05:39,159 - INFO - [processing] 第一文件列数：12
2025-06-20 17:05:39,159 - INFO - [processing] ⏰ 使用Date列的时间信息
2025-06-20 17:05:39,159 - INFO - [processing] 警告：第一文件列数为 12，预期为27列。将继续处理，但请检查文件格式是否正确。
2025-06-20 17:05:39,159 - INFO - [processing] 找到'Transaction ID'列，将用于匹配
2025-06-20 17:05:39,159 - INFO - [processing] ⏰ 处理日期时间数据...
2025-06-20 17:05:39,159 - INFO - [processing] 📅 日期一致性检查通过: 2025-06-16
2025-06-20 17:05:39,160 - INFO - [processing] 第一文件9位ID数量：47
2025-06-20 17:05:39,160 - INFO - [processing] 检查Transaction ID一致性...
2025-06-20 17:05:39,160 - INFO - [processing] ✅ 所有Transaction ID都是唯一的
2025-06-20 17:05:39,160 - INFO - [processing] 第二文件标准化后列名： ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-20 17:05:39,160 - INFO - [processing] 🔍 开始智能检测Transaction Num匹配能力...
2025-06-20 17:05:39,160 - INFO - [processing] ✅ 检测到 Transaction Num 列
2025-06-20 17:05:39,161 - INFO - [processing] � 第二文件总记录数: 48
2025-06-20 17:05:39,161 - INFO - [processing] � 第二文件Transaction Num非空记录数: 48
2025-06-20 17:05:39,161 - INFO - [processing] � 第二文件Transaction Num唯一值数量: 48
2025-06-20 17:05:39,161 - INFO - [processing] � 第二文件Transaction Num填充率: 100.0%
2025-06-20 17:05:39,161 - INFO - [processing] 🔍 检查Transaction Num与Transaction ID的匹配能力...
2025-06-20 17:05:39,161 - INFO - [processing] 📊 第一文件有效Transaction ID数量: 47
2025-06-20 17:05:39,161 - INFO - [processing] � 第二文件有效Transaction Num数量: 48
2025-06-20 17:05:39,161 - INFO - [processing] 📊 第一文件Transaction ID样本: ['2935926521', '2936200995', '2935941452', '2935863218', '2935574855']
2025-06-20 17:05:39,162 - INFO - [processing] 📊 第二文件Transaction Num样本: ['2935926521', '2936200995', '2935941452', '2935863218', '2934935459']
2025-06-20 17:05:39,162 - INFO - [processing] 📊 可匹配的Transaction ID/Num数量: 47
2025-06-20 17:05:39,162 - INFO - [processing] 📊 匹配的Transaction ID样本: ['2935926521', '2936200995', '2935941452', '2935863218', '2935574855']
2025-06-20 17:05:39,162 - INFO - [processing] 📊 匹配率: 100.0%
2025-06-20 17:05:39,162 - INFO - [processing] ✅ Transaction Num具备匹配能力
2025-06-20 17:05:39,162 - INFO - [processing] 🔄 将使用Transaction ID匹配方式
2025-06-20 17:05:39,162 - INFO - [processing] 🎯 匹配模式: Transaction ID匹配
2025-06-20 17:05:39,162 - INFO - [processing] 📝 将使用Transaction ID进行数据匹配和同步
2025-06-20 17:05:39,162 - INFO - [processing] 🚀 启动模块化数据处理器...
2025-06-20 17:05:39,162 - INFO - [processing] 🗓️ 开始按日期分组处理...
2025-06-20 17:05:39,162 - INFO - [processing] 🎯 匹配模式设置为: transaction_id
2025-06-20 17:05:39,163 - INFO - [processing] 📊 发现 1 个日期分组
2025-06-20 17:05:39,231 - INFO - [processing] 📅 处理日期: 2025-06-16 (47 条记录)
2025-06-20 17:05:39,231 - INFO - [processing] 🔍 调试 1: Transaction ID: '2934886971' -> '2934886971'
2025-06-20 17:05:39,232 - INFO - [processing] 🔍 修复前匹配数: 0, 修复后匹配数: 1
2025-06-20 17:05:39,232 - INFO - [processing] ✅ 数据类型修复生效！匹配数从 0 增加到 1
2025-06-20 17:05:39,232 - INFO - [processing] 🔍 处理Transaction ID 2934886971，找到 1 条匹配记录
2025-06-20 17:05:39,232 - INFO - [processing] ✅ 记录 47 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,232 - INFO - [processing] 📊 Transaction ID 2934886971 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,232 - INFO - [processing] 🔍 调试 2: Transaction ID: '2934888008' -> '2934888008'
2025-06-20 17:05:39,232 - INFO - [processing] 🔍 修复前匹配数: 0, 修复后匹配数: 1
2025-06-20 17:05:39,233 - INFO - [processing] ✅ 数据类型修复生效！匹配数从 0 增加到 1
2025-06-20 17:05:39,233 - INFO - [processing] 🔍 处理Transaction ID 2934888008，找到 1 条匹配记录
2025-06-20 17:05:39,233 - INFO - [processing] ✅ 记录 46 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,233 - INFO - [processing] 📊 Transaction ID 2934888008 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,233 - INFO - [processing] 🔍 调试 3: Transaction ID: '2934935459' -> '2934935459'
2025-06-20 17:05:39,233 - INFO - [processing] 🔍 修复前匹配数: 0, 修复后匹配数: 1
2025-06-20 17:05:39,233 - INFO - [processing] ✅ 数据类型修复生效！匹配数从 0 增加到 1
2025-06-20 17:05:39,233 - INFO - [processing] 🔍 处理Transaction ID 2934935459，找到 1 条匹配记录
2025-06-20 17:05:39,234 - INFO - [processing] ✅ 记录 45 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,234 - INFO - [processing] 📊 Transaction ID 2934935459 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,234 - INFO - [processing] 🔍 调试 4: Transaction ID: '2934981373' -> '2934981373'
2025-06-20 17:05:39,234 - INFO - [processing] 🔍 修复前匹配数: 0, 修复后匹配数: 1
2025-06-20 17:05:39,234 - INFO - [processing] ✅ 数据类型修复生效！匹配数从 0 增加到 1
2025-06-20 17:05:39,234 - INFO - [processing] 🔍 处理Transaction ID 2934981373，找到 1 条匹配记录
2025-06-20 17:05:39,234 - INFO - [processing] ✅ 记录 44 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,234 - INFO - [processing] 📊 Transaction ID 2934981373 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,234 - INFO - [processing] 🔍 调试 5: Transaction ID: '2934982104' -> '2934982104'
2025-06-20 17:05:39,235 - INFO - [processing] 🔍 修复前匹配数: 0, 修复后匹配数: 1
2025-06-20 17:05:39,235 - INFO - [processing] ✅ 数据类型修复生效！匹配数从 0 增加到 1
2025-06-20 17:05:39,235 - INFO - [processing] 🔍 处理Transaction ID 2934982104，找到 1 条匹配记录
2025-06-20 17:05:39,235 - INFO - [processing] ✅ 记录 43 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,235 - INFO - [processing] 📊 Transaction ID 2934982104 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,235 - INFO - [processing] 🔍 处理Transaction ID 2935046766，找到 1 条匹配记录
2025-06-20 17:05:39,235 - INFO - [processing] ✅ 记录 42 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,235 - INFO - [processing] 📊 Transaction ID 2935046766 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,236 - INFO - [processing] 🔍 处理Transaction ID 2935067612，找到 1 条匹配记录
2025-06-20 17:05:39,236 - INFO - [processing] ✅ 记录 41 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,236 - INFO - [processing] 📊 Transaction ID 2935067612 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,236 - INFO - [processing] 🔍 处理Transaction ID 2935070657，找到 1 条匹配记录
2025-06-20 17:05:39,236 - INFO - [processing] ✅ 记录 40 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,236 - INFO - [processing] 📊 Transaction ID 2935070657 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,236 - INFO - [processing] 🔍 处理Transaction ID 2935075594，找到 1 条匹配记录
2025-06-20 17:05:39,236 - INFO - [processing] ✅ 记录 39 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,237 - INFO - [processing] 📊 Transaction ID 2935075594 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,237 - INFO - [processing] 🔍 处理Transaction ID 2935189631，找到 1 条匹配记录
2025-06-20 17:05:39,237 - INFO - [processing] ✅ 记录 38 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,237 - INFO - [processing] 📊 Transaction ID 2935189631 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,237 - INFO - [processing] 🔍 处理Transaction ID 2935208265，找到 1 条匹配记录
2025-06-20 17:05:39,237 - INFO - [processing] ✅ 记录 37 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,237 - INFO - [processing] 📊 Transaction ID 2935208265 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,238 - INFO - [processing] 🔍 处理Transaction ID 2935237387，找到 1 条匹配记录
2025-06-20 17:05:39,238 - INFO - [processing] ✅ 记录 36 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,238 - INFO - [processing] 📊 Transaction ID 2935237387 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,238 - INFO - [processing] 🔍 处理Transaction ID 2935302396，找到 1 条匹配记录
2025-06-20 17:05:39,238 - INFO - [processing] ✅ 记录 35 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,238 - INFO - [processing] 📊 Transaction ID 2935302396 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,238 - INFO - [processing] 🔍 处理Transaction ID 2935379822，找到 1 条匹配记录
2025-06-20 17:05:39,239 - INFO - [processing] ✅ 记录 34 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,239 - INFO - [processing] 📊 Transaction ID 2935379822 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,239 - INFO - [processing] 🔍 处理Transaction ID 2935385609，找到 1 条匹配记录
2025-06-20 17:05:39,239 - INFO - [processing] ✅ 记录 33 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,239 - INFO - [processing] 📊 Transaction ID 2935385609 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,239 - INFO - [processing] 🔍 处理Transaction ID 2935414209，找到 1 条匹配记录
2025-06-20 17:05:39,239 - INFO - [processing] ✅ 记录 32 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,239 - INFO - [processing] 📊 Transaction ID 2935414209 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,240 - INFO - [processing] 🔍 处理Transaction ID 2935441327，找到 1 条匹配记录
2025-06-20 17:05:39,240 - INFO - [processing] ✅ 记录 31 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,240 - INFO - [processing] 📊 Transaction ID 2935441327 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,240 - INFO - [processing] 🔍 处理Transaction ID 2935519444，找到 1 条匹配记录
2025-06-20 17:05:39,240 - INFO - [processing] ✅ 记录 30 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,240 - INFO - [processing] 错误: 处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/47 [00:00<?, ?条/s][0mc:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py:971: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '2934886971' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.
2025-06-20 17:05:39,241 - INFO - [processing] 📊 Transaction ID 2935519444 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,241 - INFO - [processing] 错误: self.df2.at[m_idx, "Transaction Num"] = trans_id_clean
2025-06-20 17:05:39,241 - INFO - [processing] 🔍 处理Transaction ID 2935529460，找到 1 条匹配记录
2025-06-20 17:05:39,241 - INFO - [processing] ✅ 记录 29 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,241 - INFO - [processing] 📊 Transaction ID 2935529460 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,242 - INFO - [processing] 🔍 处理Transaction ID 2935568866，找到 1 条匹配记录
2025-06-20 17:05:39,242 - INFO - [processing] ✅ 记录 28 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,242 - INFO - [processing] 📊 Transaction ID 2935568866 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,242 - INFO - [processing] 🔍 处理Transaction ID 2935574855，找到 1 条匹配记录
2025-06-20 17:05:39,242 - INFO - [processing] ✅ 记录 27 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,243 - INFO - [processing] 📊 Transaction ID 2935574855 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,243 - INFO - [processing] 🔍 处理Transaction ID 2935600985，找到 1 条匹配记录
2025-06-20 17:05:39,243 - INFO - [processing] ✅ 记录 26 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,244 - INFO - [processing] 📊 Transaction ID 2935600985 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,244 - INFO - [processing] 🔍 处理Transaction ID 2935688868，找到 1 条匹配记录
2025-06-20 17:05:39,244 - INFO - [processing] ✅ 记录 25 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,244 - INFO - [processing] 📊 Transaction ID 2935688868 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,244 - INFO - [processing] 🔍 处理Transaction ID 2935695372，找到 1 条匹配记录
2025-06-20 17:05:39,245 - INFO - [processing] ✅ 记录 24 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,245 - INFO - [processing] 📊 Transaction ID 2935695372 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,245 - INFO - [processing] 🔍 处理Transaction ID 2935722559，找到 1 条匹配记录
2025-06-20 17:05:39,245 - INFO - [processing] ✅ 记录 23 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,245 - INFO - [processing] 📊 Transaction ID 2935722559 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,245 - INFO - [processing] 🔍 处理Transaction ID 2935733252，找到 1 条匹配记录
2025-06-20 17:05:39,245 - INFO - [processing] ✅ 记录 22 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,246 - INFO - [processing] 📊 Transaction ID 2935733252 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,246 - INFO - [processing] 🔍 处理Transaction ID 2935739517，找到 1 条匹配记录
2025-06-20 17:05:39,246 - INFO - [processing] ✅ 记录 21 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,246 - INFO - [processing] 📊 Transaction ID 2935739517 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,246 - INFO - [processing] 🔍 处理Transaction ID 2935741740，找到 1 条匹配记录
2025-06-20 17:05:39,246 - INFO - [processing] ✅ 记录 20 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,246 - INFO - [processing] 📊 Transaction ID 2935741740 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,247 - INFO - [processing] 🔍 处理Transaction ID 2935756529，找到 1 条匹配记录
2025-06-20 17:05:39,247 - INFO - [processing] ✅ 记录 19 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,247 - INFO - [processing] 📊 Transaction ID 2935756529 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,247 - INFO - [processing] 🔍 处理Transaction ID 2935863218，找到 1 条匹配记录
2025-06-20 17:05:39,247 - INFO - [processing] ✅ 记录 18 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,247 - INFO - [processing] 📊 Transaction ID 2935863218 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,248 - INFO - [processing] 🔍 处理Transaction ID 2935868517，找到 1 条匹配记录
2025-06-20 17:05:39,248 - INFO - [processing] ✅ 记录 17 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,248 - INFO - [processing] 📊 Transaction ID 2935868517 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,248 - INFO - [processing] 🔍 处理Transaction ID 2935883217，找到 1 条匹配记录
2025-06-20 17:05:39,248 - INFO - [processing] ✅ 记录 16 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,248 - INFO - [processing] 📊 Transaction ID 2935883217 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,249 - INFO - [processing] 🔍 处理Transaction ID 2935890041，找到 1 条匹配记录
2025-06-20 17:05:39,249 - INFO - [processing] ✅ 记录 15 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,249 - INFO - [processing] 📊 Transaction ID 2935890041 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,249 - INFO - [processing] 🔍 处理Transaction ID 2935926521，找到 1 条匹配记录
2025-06-20 17:05:39,249 - INFO - [processing] ✅ 记录 14 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,249 - INFO - [processing] 📊 Transaction ID 2935926521 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,249 - INFO - [processing] 🔍 处理Transaction ID 2935940209，找到 1 条匹配记录
2025-06-20 17:05:39,250 - INFO - [processing] ✅ 记录 13 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,250 - INFO - [processing] 📊 Transaction ID 2935940209 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,250 - INFO - [processing] 🔍 处理Transaction ID 2935941452，找到 1 条匹配记录
2025-06-20 17:05:39,250 - INFO - [processing] ✅ 记录 12 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,250 - INFO - [processing] 📊 Transaction ID 2935941452 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,250 - INFO - [processing] 🔍 处理Transaction ID 2935971864，找到 1 条匹配记录
2025-06-20 17:05:39,250 - INFO - [processing] ✅ 记录 11 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,251 - INFO - [processing] 📊 Transaction ID 2935971864 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,251 - INFO - [processing] 🔍 处理Transaction ID 2935975077，找到 1 条匹配记录
2025-06-20 17:05:39,251 - INFO - [processing] ✅ 记录 10 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,251 - INFO - [processing] 📊 Transaction ID 2935975077 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,251 - INFO - [processing] 🔍 处理Transaction ID 2935991917，找到 1 条匹配记录
2025-06-20 17:05:39,251 - INFO - [processing] ✅ 记录 8 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,251 - INFO - [processing] 📊 Transaction ID 2935991917 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,251 - INFO - [processing] 🔍 处理Transaction ID 2936003823，找到 1 条匹配记录
2025-06-20 17:05:39,362 - INFO - [processing] 错误: 处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 47/47 [00:00<00:00, 571.14条/s][0m
2025-06-20 17:05:39,362 - INFO - [processing] ✅ 记录 7 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,362 - INFO - [processing] 📊 Transaction ID 2936003823 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,362 - INFO - [processing] 🔍 处理Transaction ID 2936004586，找到 1 条匹配记录
2025-06-20 17:05:39,363 - INFO - [processing] ✅ 记录 6 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,363 - INFO - [processing] 📊 Transaction ID 2936004586 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,363 - INFO - [processing] 🔍 处理Transaction ID 2936028752，找到 1 条匹配记录
2025-06-20 17:05:39,363 - INFO - [processing] ✅ 记录 5 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,363 - INFO - [processing] 📊 Transaction ID 2936028752 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,363 - INFO - [processing] 🔍 处理Transaction ID 2936051379，找到 1 条匹配记录
2025-06-20 17:05:39,363 - INFO - [processing] ✅ 记录 4 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,363 - INFO - [processing] 📊 Transaction ID 2936051379 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,363 - INFO - [processing] 🔍 处理Transaction ID 2936139750，找到 1 条匹配记录
2025-06-20 17:05:39,363 - INFO - [processing] ✅ 记录 3 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,363 - INFO - [processing] 📊 Transaction ID 2936139750 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,364 - INFO - [processing] 🔍 处理Transaction ID 2936200911，找到 1 条匹配记录
2025-06-20 17:05:39,364 - INFO - [processing] ✅ 记录 2 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,364 - INFO - [processing] 📊 Transaction ID 2936200911 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,364 - INFO - [processing] 🔍 处理Transaction ID 2936200995，找到 1 条匹配记录
2025-06-20 17:05:39,364 - INFO - [processing] ✅ 记录 1 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,364 - INFO - [processing] 📊 Transaction ID 2936200995 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,364 - INFO - [processing] 🔍 处理Transaction ID 2936301757，找到 1 条匹配记录
2025-06-20 17:05:39,364 - INFO - [processing] ✅ 记录 0 标记设置成功: Matched_Flag = True
2025-06-20 17:05:39,364 - INFO - [processing] 📊 Transaction ID 2936301757 匹配统计: 成功 1, 失败 0
2025-06-20 17:05:39,364 - INFO - [processing] ✅ 处理完成，耗时: 0.10秒
2025-06-20 17:05:39,364 - INFO - [processing] 📊 transaction_id 模式统计:
2025-06-20 17:05:39,365 - INFO - [processing] 处理: 47 条
2025-06-20 17:05:39,365 - INFO - [processing] 匹配: 47 条
2025-06-20 17:05:39,365 - INFO - [processing] 插入: 0 条
2025-06-20 17:05:39,365 - INFO - [processing] ✅ 模块化数据处理完成
2025-06-20 17:05:39,365 - INFO - [processing] 🔍 验证 - 实际匹配的记录数量: 47
2025-06-20 17:05:39,365 - INFO - [processing] 🔍 验证 - DataFrame中实际标记数量: 47
2025-06-20 17:05:39,365 - INFO - [processing] 🔍 调试 - Matched_Flag标记统计:
2025-06-20 17:05:39,365 - INFO - [processing] 总记录数: 48
2025-06-20 17:05:39,365 - INFO - [processing] 已标记记录数: 47
2025-06-20 17:05:39,365 - INFO - [processing] 未标记记录数: 1
2025-06-20 17:05:39,366 - INFO - [processing] 🔍 调试 - 按状态分组的Matched_Flag统计:
2025-06-20 17:05:39,366 - INFO - [processing] Matched_Flag  False  True
2025-06-20 17:05:39,366 - INFO - [processing] Order status
2025-06-20 17:05:39,366 - INFO - [processing] Finish            0     47
2025-06-20 17:05:39,366 - INFO - [processing] Refunded          1      0
2025-06-20 17:05:39,366 - INFO - [processing] 🔍 调试 - 未找到插入记录统计信息
2025-06-20 17:05:39,366 - INFO - [processing] 🔍 调试 - 金额差异分析:
2025-06-20 17:05:39,366 - INFO - [processing] 第一文件总金额（包含所有settled记录）: RM300.00
2025-06-20 17:05:39,366 - INFO - [processing] 第一文件没有Order types列，无法检查API订单
2025-06-20 17:05:39,367 - INFO - [processing] 第二文件总金额（排除API订单）: RM300.00
2025-06-20 17:05:39,367 - INFO - [processing] 金额差异（第一文件无法排除API订单）: RM0.00
2025-06-20 17:05:39,367 - INFO - [processing] Transaction ID匹配: 3104条
2025-06-20 17:05:39,367 - INFO - [processing] Transaction ID插入: 42条
2025-06-20 17:05:39,367 - INFO - [processing] 理论上应该完全匹配，差异应该为0
2025-06-20 17:05:39,367 - INFO - [processing] 执行数据恢复和补全...
2025-06-20 17:05:39,367 - INFO - [processing] 🔍 调试 - 数据恢复前已标记记录数: 47
2025-06-20 17:05:39,367 - INFO - [processing] 🔄 开始执行数据恢复...
2025-06-20 17:05:39,367 - INFO - [processing] ✅ Transaction Num修复: 0 条
2025-06-20 17:05:39,368 - INFO - [processing] ✅ Equipment信息恢复: 0 条
2025-06-20 17:05:39,368 - INFO - [processing] ✅ Order No.信息恢复: 0 条
2025-06-20 17:05:39,368 - INFO - [processing] 🔍 调试 - 数据恢复后已标记记录数: 47
2025-06-20 17:05:39,368 - INFO - [processing] 🔍 调试 - 处理Matched_Flag前的统计:
2025-06-20 17:05:39,368 - INFO - [processing] True: 47
2025-06-20 17:05:39,368 - INFO - [processing] False: 1
2025-06-20 17:05:39,368 - INFO - [processing] NaN: 0
2025-06-20 17:05:39,368 - INFO - [processing] 🔍 调试 - 处理Matched_Flag后的统计:
2025-06-20 17:05:39,369 - INFO - [processing] True: 47
2025-06-20 17:05:39,369 - INFO - [processing] False: 1
2025-06-20 17:05:39,369 - INFO - [processing] NaN: 0
2025-06-20 17:05:39,369 - INFO - [processing] 🔍 调试 - 第二文件Order status分布:
2025-06-20 17:05:39,369 - INFO - [processing] 'Finish': 47 条
2025-06-20 17:05:39,369 - INFO - [processing] 'Refunded': 1 条
2025-06-20 17:05:39,369 - INFO - [processing] 🔍 调试 - 第二文件总记录数: 48
2025-06-20 17:05:39,369 - INFO - [processing] 🔍 调试 - finish状态记录数: 47
2025-06-20 17:05:39,370 - INFO - [processing] 🔍 调试 - 第二文件没有API订单
2025-06-20 17:05:39,370 - INFO - [processing] 🔍 调试 - 排除API订单后记录数: 47
2025-06-20 17:05:39,370 - INFO - [processing] 🔍 调试 - df2_after最终记录数: 47
2025-06-20 17:05:39,370 - INFO - [processing] 🔍 调试 - df2_after总金额: RM300.00
2025-06-20 17:05:39,370 - INFO - [processing] Transaction ID匹配模式：金额差异在可接受范围内，跳过自动修正
2025-06-20 17:05:39,370 - INFO - [processing] Transaction ID匹配模式：数据已通过Transaction ID同步，无需自动修正
2025-06-20 17:05:39,370 - INFO - [processing] 处理完成！结果已写入 C:/Users/<USER>/Desktop/June/ZERO/160625 CHINA ZERO.xlsx
2025-06-20 17:05:39,370 - INFO - [processing] - 数据已写入 DATA sheet
2025-06-20 17:05:39,371 - INFO - [processing] - 日志已写入 LOG sheet
2025-06-20 17:05:39,371 - INFO - [processing] 第二文件最终总金额: RM300.00
2025-06-20 17:05:39,371 - INFO - [processing] 第一文件总金额: RM300.00
2025-06-20 17:05:39,371 - INFO - [processing] 金额差异: RM0.00
2025-06-20 17:05:39,371 - INFO - [processing] ✓ 金额匹配成功！
2025-06-20 17:05:39,371 - INFO - [processing] 使用指定的文件路径:
2025-06-20 17:05:39,372 - INFO - [processing] 第一文件: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_LISTING_FROM_DATE_16062025_TO_16062025_zeropowerstatio.xlsx
2025-06-20 17:05:39,372 - INFO - [processing] 第二文件: C:/Users/<USER>/Desktop/June/ZERO/160625 CHINA ZERO.xlsx
2025-06-20 17:05:39,372 - INFO - [processing] Sheet名称: transaction
2025-06-20 17:05:39,372 - INFO - [processing] 脚本执行完成
2025-06-20 17:05:39,458 - INFO - [processing] 文件处理成功完成
2025-06-20 17:17:06,348 - INFO - [import] 已选择1个导入文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:17:06,348 - INFO - [import] 自动识别为ZERO平台
2025-06-20 17:17:11,264 - INFO - [import] 选择的目标数据库: SQLite
2025-06-20 17:17:11,265 - INFO - [import] 开始导入数据，平台类型: ZERO, 订单类型: all
2025-06-20 17:17:11,265 - INFO - [import] 目标数据库: SQLite
2025-06-20 17:17:11,265 - INFO - [import] 正在备份数据库...
2025-06-20 17:17:11,359 - INFO - SQLite数据库备份成功: sqlite_backup_20250620_171711.db
2025-06-20 17:17:11,360 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250620_171711.db
2025-06-20 17:17:11,360 - INFO - [import] 处理文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:17:11,360 - INFO - [import] 数据导入脚本配置错误: data_import_script_optimized脚本不存在: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\scripts/data_import_optimized.py
请检查脚本文件路径配置。
2025-06-20 17:17:11,360 - INFO - [import] 文件 160625 CHINA ZERO.xlsx 导入失败
2025-06-20 17:17:11,361 - INFO - [import] 部分或全部文件导入失败
2025-06-20 17:17:37,144 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:17:37,880 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:17:47,988 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:17:48,475 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:17:48,651 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:23:13,532 - INFO - 双数据库模块加载成功
2025-06-20 17:23:13,729 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 17:23:13,729 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 17:23:13,729 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 17:23:14,182 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-20 17:23:14,189 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 17:23:14,189 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 17:23:14,315 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 17:23:14,315 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 17:23:14,345 - INFO - [general] 数据库设置已加载
2025-06-20 17:23:14,345 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 17:23:14,353 - INFO - [settings] 数据库设置已加载
2025-06-20 17:23:14,354 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 17:23:14,769 - INFO - [database_config] 数据库状态已刷新
2025-06-20 17:23:14,775 - INFO - 数据处理与导入应用已启动
2025-06-20 17:23:14,885 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 17:23:18,914 - INFO - [settings] 数据库备份成功: C:/Users/<USER>/Desktop/Day Report/database\backups\sales_reports_backup_20250620_172318.db
2025-06-20 17:23:19,801 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-20 17:23:25,755 - INFO - [import] 已选择1个导入文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:23:25,756 - INFO - [import] 自动识别为ZERO平台
2025-06-20 17:23:30,153 - INFO - [import] 选择的目标数据库: SQLite
2025-06-20 17:23:30,154 - INFO - [import] 开始导入数据，平台类型: ZERO, 订单类型: all
2025-06-20 17:23:30,154 - INFO - [import] 目标数据库: SQLite
2025-06-20 17:23:30,154 - INFO - [import] 正在备份数据库...
2025-06-20 17:23:30,241 - INFO - SQLite数据库备份成功: sqlite_backup_20250620_172330.db
2025-06-20 17:23:30,241 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250620_172330.db
2025-06-20 17:23:30,242 - INFO - [import] 处理文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:23:30,242 - INFO - [import] 数据导入脚本配置错误: data_import_script_optimized脚本不存在: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\scripts/data_import_optimized.py
请检查脚本文件路径配置。
2025-06-20 17:23:30,242 - INFO - [import] 文件 160625 CHINA ZERO.xlsx 导入失败
2025-06-20 17:23:30,242 - INFO - [import] 部分或全部文件导入失败
2025-06-20 17:23:30,242 - INFO - [import] 检测到导入失败，可以恢复到备份状态
2025-06-20 17:23:32,370 - WARNING - 日志控件未注册: error, 已注册的控件: ['general', 'processing', 'import', 'refund', 'settings', 'database_config']
2025-06-20 17:23:32,370 - INFO - [error] 恢复数据库失败: 无法恢复文件: C:/Users/<USER>/Desktop/Day Report/database\backups\sqlite_backup_20250620_172330.db
2025-06-20 17:23:32,370 - INFO - [import] 数据库恢复失败
2025-06-20 17:27:28,797 - INFO - 双数据库模块加载成功
2025-06-20 17:27:28,987 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 17:27:28,987 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 17:27:28,987 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 17:27:29,573 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-20 17:27:29,579 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 17:27:29,579 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 17:27:29,690 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 17:27:29,691 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 17:27:29,714 - INFO - [general] 数据库设置已加载
2025-06-20 17:27:29,714 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 17:27:29,714 - INFO - [settings] 数据库设置已加载
2025-06-20 17:27:29,714 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 17:27:29,989 - INFO - [database_config] 数据库状态已刷新
2025-06-20 17:27:29,994 - INFO - 数据处理与导入应用已启动
2025-06-20 17:27:30,090 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-20 17:27:34,597 - INFO - [import] 已选择1个导入文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:27:34,598 - INFO - [import] 自动识别为ZERO平台
2025-06-20 17:27:37,871 - INFO - [import] 选择的目标数据库: SQLite
2025-06-20 17:27:37,872 - INFO - [import] 开始导入数据，平台类型: ZERO, 订单类型: all
2025-06-20 17:27:37,872 - INFO - [import] 目标数据库: SQLite
2025-06-20 17:27:37,872 - INFO - [import] 正在备份数据库...
2025-06-20 17:27:37,919 - INFO - SQLite数据库备份成功: sqlite_backup_20250620_172737.db
2025-06-20 17:27:37,920 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250620_172737.db
2025-06-20 17:27:37,920 - INFO - [import] 处理文件: 160625 CHINA ZERO.xlsx
2025-06-20 17:27:37,920 - INFO - [import] 数据导入脚本配置错误: data_import_script_optimized脚本不存在: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\scripts/data_import_optimized.py
请检查脚本文件路径配置。
2025-06-20 17:27:37,920 - INFO - [import] 文件 160625 CHINA ZERO.xlsx 导入失败
2025-06-20 17:27:37,920 - INFO - [import] 部分或全部文件导入失败
2025-06-20 17:27:37,921 - INFO - [import] 检测到导入失败，可以恢复到备份状态
2025-06-20 17:27:39,061 - WARNING - 日志控件未注册: error, 已注册的控件: ['general', 'processing', 'import', 'refund', 'settings', 'database_config']
2025-06-20 17:27:39,061 - INFO - [error] 恢复数据库失败: 文件不存在: C:/Users/<USER>/Desktop/Day Report/database\backups\sqlite_backup_20250620_172737.db
请检查文件路径是否正确。
2025-06-20 17:27:39,061 - INFO - [import] 数据库恢复失败
