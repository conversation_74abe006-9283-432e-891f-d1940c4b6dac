# -*- coding: utf-8 -*-
"""
修复编码问题
彻底解决Windows控制台乱码问题
"""

import os
import sys
import subprocess
import locale

def fix_console_encoding():
    """修复控制台编码问题"""
    
    print("🔧 修复控制台编码问题...")
    print("=" * 60)
    
    # 1. 设置环境变量
    print("1. 设置环境变量...")
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    print("   ✅ PYTHONIOENCODING = utf-8")
    print("   ✅ PYTHONLEGACYWINDOWSSTDIO = 1")
    
    # 2. 设置控制台代码页
    print("\n2. 设置控制台代码页...")
    try:
        # 设置控制台为UTF-8
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        print("   ✅ 控制台代码页设置为 65001 (UTF-8)")
    except Exception as e:
        print(f"   ⚠️ 设置控制台代码页失败: {e}")
    
    # 3. 设置Python locale
    print("\n3. 设置Python locale...")
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        print("   ✅ locale设置为 zh_CN.UTF-8")
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
            print("   ✅ locale设置为 Chinese_China.UTF-8")
        except:
            try:
                locale.setlocale(locale.LC_ALL, '')
                print("   ✅ locale设置为系统默认")
            except Exception as e:
                print(f"   ⚠️ locale设置失败: {e}")
    
    # 4. 重新配置标准输出
    print("\n4. 重新配置标准输出...")
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
            print("   ✅ stdout/stderr重新配置为UTF-8")
        else:
            print("   ⚠️ 当前Python版本不支持reconfigure")
    except Exception as e:
        print(f"   ⚠️ 重新配置失败: {e}")
    
    # 5. 测试中文输出
    print("\n5. 测试中文输出...")
    test_strings = [
        "📊 插入统计: 82条记录, 总金额: RM488.00",
        "✅ 金额匹配成功！",
        "🔧 Transaction ID匹配功能已恢复",
        "📋 处理结果: 匹配: 26 条, 插入: 41 条"
    ]
    
    for i, test_str in enumerate(test_strings, 1):
        try:
            print(f"   {i}. {test_str}")
        except Exception as e:
            print(f"   {i}. 输出失败: {e}")
    
    # 6. 显示当前编码信息
    print("\n6. 当前编码信息:")
    print(f"   sys.stdout.encoding: {getattr(sys.stdout, 'encoding', 'Unknown')}")
    print(f"   sys.stderr.encoding: {getattr(sys.stderr, 'encoding', 'Unknown')}")
    print(f"   sys.getdefaultencoding(): {sys.getdefaultencoding()}")
    print(f"   locale.getpreferredencoding(): {locale.getpreferredencoding()}")
    
    return True

def create_encoding_fix_batch():
    """创建编码修复批处理文件"""
    
    print("\n🔧 创建编码修复批处理文件...")
    
    batch_content = """@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=1
echo 控制台编码已设置为UTF-8
echo 环境变量已设置
echo.
echo 现在可以运行Python脚本了
echo.
pause
"""
    
    batch_path = "修复编码.bat"
    
    try:
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print(f"   ✅ 批处理文件已创建: {batch_path}")
        print("   📋 使用方法: 运行此批处理文件后再运行数据处理程序")
        return True
    except Exception as e:
        print(f"   ❌ 创建批处理文件失败: {e}")
        return False

def test_encoding_in_subprocess():
    """在子进程中测试编码"""
    
    print("\n🔧 在子进程中测试编码...")
    
    test_script = """
import sys
import os

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 测试输出
test_strings = [
    "📊 插入统计: 82条记录, 总金额: RM488.00",
    "✅ 金额匹配成功！",
    "🔧 Transaction ID匹配功能已恢复"
]

print("子进程编码测试:")
for i, s in enumerate(test_strings, 1):
    try:
        print(f"{i}. {s}")
    except Exception as e:
        print(f"{i}. 输出失败: {e}")

print(f"子进程编码: {sys.stdout.encoding}")
"""
    
    try:
        # 创建临时测试脚本
        with open('temp_encoding_test.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        # 运行测试脚本
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
        
        result = subprocess.run(
            [sys.executable, 'temp_encoding_test.py'],
            capture_output=True,
            text=True,
            encoding='utf-8',
            env=env
        )
        
        print("   📋 子进程输出:")
        if result.stdout:
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"     {line}")
        
        if result.stderr:
            print("   ⚠️ 子进程错误:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    print(f"     {line}")
        
        # 清理临时文件
        try:
            os.remove('temp_encoding_test.py')
        except:
            pass
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"   ❌ 子进程测试失败: {e}")
        return False

def create_wrapper_script():
    """创建包装脚本来解决编码问题"""
    
    print("\n🔧 创建包装脚本...")
    
    wrapper_content = '''# -*- coding: utf-8 -*-
"""
数据处理包装脚本
解决编码问题的包装器
"""

import os
import sys
import subprocess
import locale

def setup_encoding():
    """设置编码环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # 设置控制台代码页
    try:
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except:
        pass
    
    # 设置locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
        except:
            pass
    
    # 重新配置标准输出
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except:
        pass

def main():
    """主函数"""
    # 设置编码
    setup_encoding()
    
    # 获取原始脚本路径
    original_script = r"数据处理应用系统\\数据处理系统\\数据处理应用系统_重构版\\01_主程序\\report 模块化设计 7.0.py"
    
    if not os.path.exists(original_script):
        print(f"错误: 找不到原始脚本 {original_script}")
        return 1
    
    # 传递所有命令行参数
    cmd = [sys.executable, original_script] + sys.argv[1:]
    
    # 设置环境
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # 运行原始脚本
    try:
        result = subprocess.run(cmd, env=env)
        return result.returncode
    except Exception as e:
        print(f"运行脚本时出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    wrapper_path = "数据处理应用系统\\数据处理系统\\数据处理应用系统_重构版\\01_主程序\\report_wrapper.py"
    
    try:
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
        print(f"   ✅ 包装脚本已创建: {wrapper_path}")
        print("   📋 使用方法: 在配置中将脚本路径改为wrapper脚本")
        return True
    except Exception as e:
        print(f"   ❌ 创建包装脚本失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🚀 编码问题修复工具")
    print("=" * 80)
    
    # 执行修复步骤
    steps = [
        ("修复控制台编码", fix_console_encoding),
        ("创建编码修复批处理", create_encoding_fix_batch),
        ("测试子进程编码", test_encoding_in_subprocess),
        ("创建包装脚本", create_wrapper_script)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {e}")
            results.append((step_name, False))
    
    # 汇总结果
    print("\n📊 修复结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {step_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📋 总体结果: {passed}/{total} 项修复成功")
    
    if passed >= 3:  # 至少3项成功
        print("\n🎉 编码问题修复基本成功！")
        print("\n📋 建议:")
        print("  1. 重启命令提示符或PowerShell")
        print("  2. 运行 修复编码.bat 批处理文件")
        print("  3. 然后再运行数据处理程序")
        print("  4. 或者使用包装脚本 report_wrapper.py")
        
    else:
        print(f"\n⚠️ 编码问题修复部分失败！")
        print(f"  需要手动设置控制台编码")
    
    return passed >= 3

if __name__ == "__main__":
    result = main()
    input(f"\n按回车键退出... (修复{'成功' if result else '失败'})")
