# -*- coding: utf-8 -*-
"""
PostgreSQL预检查脚本
检查PostgreSQL服务器状态并创建必要的数据库
"""

import psycopg2
import sys
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def check_postgresql_connection():
    """检查PostgreSQL连接"""
    print("🔍 检查PostgreSQL服务器连接...")
    
    # 先尝试连接到默认的postgres数据库
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='postgres',  # 默认数据库
            user='zerochon',
            password='zerochon'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        print("✅ PostgreSQL服务器连接成功")
        return conn
    except Exception as e:
        print(f"❌ PostgreSQL服务器连接失败: {e}")
        print("\n📋 请检查:")
        print("1. PostgreSQL服务器是否已安装并运行")
        print("2. 用户名和密码是否正确")
        print("3. 端口5432是否可访问")
        return None

def check_database_exists(conn, db_name):
    """检查数据库是否存在"""
    print(f"🔍 检查数据库 '{db_name}' 是否存在...")
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        exists = cursor.fetchone() is not None
        cursor.close()
        
        if exists:
            print(f"✅ 数据库 '{db_name}' 已存在")
        else:
            print(f"❌ 数据库 '{db_name}' 不存在")
        
        return exists
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

def create_database(conn, db_name):
    """创建数据库"""
    print(f"🔧 创建数据库 '{db_name}'...")
    
    try:
        cursor = conn.cursor()
        cursor.execute(f'CREATE DATABASE "{db_name}"')
        cursor.close()
        print(f"✅ 数据库 '{db_name}' 创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def test_target_database_connection():
    """测试目标数据库连接"""
    print("🔍 测试目标数据库连接...")
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='sales_reports',
            user='zerochon',
            password='zerochon'
        )
        conn.close()
        print("✅ 目标数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 目标数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PostgreSQL预检查")
    print("="*40)
    
    # 1. 检查PostgreSQL服务器连接
    conn = check_postgresql_connection()
    if not conn:
        return 1
    
    # 2. 检查目标数据库是否存在
    db_name = 'sales_reports'
    if not check_database_exists(conn, db_name):
        # 3. 创建数据库
        if not create_database(conn, db_name):
            conn.close()
            return 1
    
    conn.close()
    
    # 4. 测试目标数据库连接
    if not test_target_database_connection():
        return 1
    
    print("\n✅ PostgreSQL预检查完成！")
    print("📋 环境准备就绪，可以开始迁移")
    return 0

if __name__ == "__main__":
    sys.exit(main())
