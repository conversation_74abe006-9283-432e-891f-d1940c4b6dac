# -*- coding: utf-8 -*-
"""
测试Equipment_ID表同步功能
"""

import sqlite3
import psycopg2
import pandas as pd

def test_equipment_id_sync():
    """测试Equipment_ID表同步"""
    
    # 数据库配置
    sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
    pg_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    print("🔍 测试Equipment_ID表同步功能...")
    print("=" * 60)
    
    try:
        # 连接数据库
        sqlite_conn = sqlite3.connect(sqlite_path)
        sqlite_cursor = sqlite_conn.cursor()
        
        pg_conn = psycopg2.connect(**pg_config)
        pg_cursor = pg_conn.cursor()
        
        # 检查SQLite中的Equipment_ID表
        print("📊 检查SQLite Equipment_ID表...")
        sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name = 'Equipment_ID'")
        sqlite_has_table = sqlite_cursor.fetchone() is not None
        
        if sqlite_has_table:
            sqlite_cursor.execute("SELECT COUNT(*) FROM Equipment_ID")
            sqlite_count = sqlite_cursor.fetchone()[0]
            print(f"✅ SQLite Equipment_ID表存在: {sqlite_count:,} 行")
            
            # 显示表结构
            sqlite_cursor.execute("PRAGMA table_info(Equipment_ID)")
            columns = sqlite_cursor.fetchall()
            print(f"📋 表结构: {len(columns)} 列")
            for i, (cid, name, type_, notnull, default, pk) in enumerate(columns[:10]):
                print(f"  {i+1}. {name} ({type_})")
            if len(columns) > 10:
                print(f"  ... 还有 {len(columns) - 10} 列")
                
            # 显示前几行数据
            sqlite_cursor.execute("SELECT * FROM Equipment_ID LIMIT 3")
            sample_data = sqlite_cursor.fetchall()
            if sample_data:
                print(f"📋 样本数据 (前3行):")
                column_names = [col[1] for col in columns]
                for i, row in enumerate(sample_data):
                    print(f"  行{i+1}: {dict(zip(column_names[:5], row[:5]))}")
        else:
            print("❌ SQLite中不存在Equipment_ID表")
        
        # 检查PostgreSQL中的Equipment_ID表
        print("\n📊 检查PostgreSQL Equipment_ID表...")
        pg_cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Equipment_ID'
            )
        """)
        pg_has_table = pg_cursor.fetchone()[0]
        
        if pg_has_table:
            pg_cursor.execute('SELECT COUNT(*) FROM "Equipment_ID"')
            pg_count = pg_cursor.fetchone()[0]
            print(f"✅ PostgreSQL Equipment_ID表存在: {pg_count:,} 行")
            
            # 显示表结构
            pg_cursor.execute("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'Equipment_ID' 
                ORDER BY ordinal_position
            """)
            pg_columns = pg_cursor.fetchall()
            print(f"📋 表结构: {len(pg_columns)} 列")
            for i, (name, type_) in enumerate(pg_columns[:10]):
                print(f"  {i+1}. {name} ({type_})")
            if len(pg_columns) > 10:
                print(f"  ... 还有 {len(pg_columns) - 10} 列")
        else:
            print("❌ PostgreSQL中不存在Equipment_ID表")
        
        # 比较数据
        if sqlite_has_table and pg_has_table:
            print("\n📊 比较Equipment_ID表数据...")
            
            # 读取数据进行比较
            sqlite_df = pd.read_sql_query("SELECT * FROM Equipment_ID", sqlite_conn)
            pg_df = pd.read_sql_query('SELECT * FROM "Equipment_ID"', pg_conn)
            
            print(f"SQLite行数: {len(sqlite_df):,}")
            print(f"PostgreSQL行数: {len(pg_df):,}")
            print(f"差异: {abs(len(sqlite_df) - len(pg_df)):,} 行")
            
            if len(sqlite_df) == len(pg_df):
                print("✅ 行数一致")
            else:
                print("⚠️ 行数不一致，需要同步")
                
            # 检查列是否一致
            sqlite_columns = set(sqlite_df.columns)
            pg_columns = set(pg_df.columns)
            
            if sqlite_columns == pg_columns:
                print("✅ 列结构一致")
            else:
                print("⚠️ 列结构不一致")
                only_sqlite = sqlite_columns - pg_columns
                only_pg = pg_columns - sqlite_columns
                if only_sqlite:
                    print(f"  仅SQLite有: {', '.join(only_sqlite)}")
                if only_pg:
                    print(f"  仅PostgreSQL有: {', '.join(only_pg)}")
        
        # 模拟同步测试
        if sqlite_has_table and pg_has_table and sqlite_count > 0:
            print("\n🔄 模拟同步测试...")
            
            # 这里只是模拟，不实际执行
            print("1. 备份PostgreSQL数据...")
            print("2. 清空PostgreSQL Equipment_ID表...")
            print("3. 从SQLite复制数据到PostgreSQL...")
            print("4. 验证同步结果...")
            print("✅ 模拟同步测试完成")
            
            print(f"\n📋 同步计划:")
            print(f"  源: SQLite Equipment_ID表 ({sqlite_count:,} 行)")
            print(f"  目标: PostgreSQL Equipment_ID表 ({pg_count:,} 行)")
            print(f"  操作: 完全替换")
            print(f"  预期结果: PostgreSQL将有 {sqlite_count:,} 行")
        
        sqlite_conn.close()
        pg_conn.close()
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def simulate_equipment_id_sync():
    """模拟Equipment_ID表同步（安全测试，不实际修改数据）"""
    
    sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
    pg_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    print("\n🔄 模拟Equipment_ID表同步...")
    print("=" * 60)
    
    try:
        sqlite_conn = sqlite3.connect(sqlite_path)
        pg_conn = psycopg2.connect(**pg_config)
        
        # 读取数据
        sqlite_df = pd.read_sql_query("SELECT * FROM Equipment_ID", sqlite_conn)
        pg_df = pd.read_sql_query('SELECT * FROM "Equipment_ID"', pg_conn)
        
        print(f"📊 数据分析:")
        print(f"  SQLite Equipment_ID: {len(sqlite_df):,} 行")
        print(f"  PostgreSQL Equipment_ID: {len(pg_df):,} 行")
        
        if len(sqlite_df) > 0:
            print(f"\n📋 同步操作预览:")
            print(f"  1. 清空PostgreSQL Equipment_ID表 (删除 {len(pg_df):,} 行)")
            print(f"  2. 插入SQLite数据 (插入 {len(sqlite_df):,} 行)")
            print(f"  3. 最终结果: PostgreSQL将有 {len(sqlite_df):,} 行")
            
            # 计算批次
            batch_size = 1000
            batches = (len(sqlite_df) + batch_size - 1) // batch_size
            print(f"  4. 批次处理: {batches} 批，每批 {batch_size} 行")
            
            print(f"\n✅ 模拟同步完成，实际同步请使用PostgreSQL管理工具")
        else:
            print("⚪ SQLite Equipment_ID表为空，无需同步")
        
        sqlite_conn.close()
        pg_conn.close()
        
    except Exception as e:
        print(f"❌ 模拟同步失败: {e}")

if __name__ == "__main__":
    test_equipment_id_sync()
    simulate_equipment_id_sync()
    input("\n按回车键退出...")
