# -*- coding: utf-8 -*-
"""
PostgreSQL便携版管理工具
用于管理便携版PostgreSQL数据库，包括启动、停止、连接测试和数据库比较
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import psycopg2
import sqlite3
import pandas as pd
from datetime import datetime

class PostgreSQLManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PostgreSQL便携版管理工具")
        self.root.geometry("800x600")
        
        # PostgreSQL路径配置 - 使用系统安装的PostgreSQL
        self.pg_dir = r'C:\Program Files\PostgreSQL\17'  # 使用PostgreSQL 17
        self.pg_bin = os.path.join(self.pg_dir, 'bin')
        self.pg_data = r'C:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\PostgreSQL database\data'
        
        # SQLite路径
        self.sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
        
        # 连接配置
        self.pg_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'postgres',
            'user': 'postgres',
            'password': 'zerochon'
        }
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = ttk.Label(self.root, text="PostgreSQL便携版管理工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态框架
        status_frame = ttk.LabelFrame(self.root, text="数据库状态", padding="10")
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = ttk.Label(status_frame, text="检查中...", font=("Arial", 10))
        self.status_label.pack()
        
        # 操作按钮框架
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 第一行按钮
        row1 = ttk.Frame(button_frame)
        row1.pack(fill=tk.X, pady=2)
        
        ttk.Button(row1, text="🔄 检查状态", command=self.check_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1, text="▶️ 启动PostgreSQL", command=self.start_postgresql).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1, text="⏹️ 停止PostgreSQL", command=self.stop_postgresql).pack(side=tk.LEFT, padx=5)
        ttk.Button(row1, text="🔗 测试连接", command=self.test_connection).pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮
        row2 = ttk.Frame(button_frame)
        row2.pack(fill=tk.X, pady=2)

        ttk.Button(row2, text="📊 比较数据库", command=self.compare_databases).pack(side=tk.LEFT, padx=5)
        ttk.Button(row2, text="� 同步数据", command=self.sync_databases).pack(side=tk.LEFT, padx=5)

        # 第三行按钮
        row3 = ttk.Frame(button_frame)
        row3.pack(fill=tk.X, pady=2)

        ttk.Button(row3, text="🎯 Equipment_ID同步", command=self.equipment_id_sync).pack(side=tk.LEFT, padx=5)
        ttk.Button(row3, text="� 打开pgAdmin", command=self.open_pgadmin).pack(side=tk.LEFT, padx=5)
        ttk.Button(row3, text="� 打开psql", command=self.open_psql).pack(side=tk.LEFT, padx=5)
        ttk.Button(row3, text="🗂️ 打开数据目录", command=self.open_data_dir).pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(self.root, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 初始检查状态
        self.check_status()
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def check_status(self):
        """检查PostgreSQL状态"""
        self.log("检查PostgreSQL状态...")
        
        # 检查目录
        if not os.path.exists(self.pg_dir):
            self.status_label.config(text="❌ PostgreSQL目录不存在", foreground="red")
            self.log(f"错误: PostgreSQL目录不存在 - {self.pg_dir}")
            return
            
        # 检查进程
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq postgres.exe'], 
                                  capture_output=True, text=True, shell=True)
            if 'postgres.exe' in result.stdout:
                self.status_label.config(text="✅ PostgreSQL正在运行", foreground="green")
                self.log("PostgreSQL进程正在运行")
                
                # 测试连接
                try:
                    conn = psycopg2.connect(**self.pg_config)
                    conn.close()
                    self.log("✅ PostgreSQL连接测试成功")
                except Exception as e:
                    self.log(f"❌ PostgreSQL连接测试失败: {e}")
                    
            else:
                self.status_label.config(text="❌ PostgreSQL未运行", foreground="red")
                self.log("PostgreSQL进程未运行")
                
        except Exception as e:
            self.log(f"检查进程失败: {e}")
            
    def start_postgresql(self):
        """启动PostgreSQL"""
        self.log("启动PostgreSQL...")
        
        pg_ctl = os.path.join(self.pg_bin, 'pg_ctl.exe')
        if not os.path.exists(pg_ctl):
            self.log(f"错误: pg_ctl.exe不存在 - {pg_ctl}")
            return
            
        try:
            log_file = os.path.join(self.pg_dir, 'logs', 'postgresql.log')
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            cmd = [pg_ctl, '-D', self.pg_data, '-l', log_file, 'start']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log("✅ PostgreSQL启动成功")
                self.check_status()
            else:
                self.log(f"❌ PostgreSQL启动失败: {result.stderr}")
                
        except Exception as e:
            self.log(f"启动PostgreSQL时出错: {e}")
            
    def stop_postgresql(self):
        """停止PostgreSQL"""
        self.log("停止PostgreSQL...")
        
        pg_ctl = os.path.join(self.pg_bin, 'pg_ctl.exe')
        if not os.path.exists(pg_ctl):
            self.log(f"错误: pg_ctl.exe不存在 - {pg_ctl}")
            return
            
        try:
            cmd = [pg_ctl, '-D', self.pg_data, 'stop']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log("✅ PostgreSQL停止成功")
                self.check_status()
            else:
                self.log(f"❌ PostgreSQL停止失败: {result.stderr}")
                
        except Exception as e:
            self.log(f"停止PostgreSQL时出错: {e}")
            
    def test_connection(self):
        """测试数据库连接"""
        self.log("测试数据库连接...")
        
        # 测试PostgreSQL
        try:
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            self.log(f"✅ PostgreSQL连接成功: {version}")
            conn.close()
        except Exception as e:
            self.log(f"❌ PostgreSQL连接失败: {e}")
            
        # 测试SQLite
        try:
            conn = sqlite3.connect(self.sqlite_path)
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]
            self.log(f"✅ SQLite连接成功: 版本 {version}")
            conn.close()
        except Exception as e:
            self.log(f"❌ SQLite连接失败: {e}")
            
    def compare_databases(self):
        """比较两个数据库的表结构"""
        self.log("比较SQLite和PostgreSQL数据库...")
        
        try:
            # 获取SQLite表信息
            sqlite_conn = sqlite3.connect(self.sqlite_path)
            sqlite_cursor = sqlite_conn.cursor()
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            sqlite_tables = set([row[0] for row in sqlite_cursor.fetchall()])
            
            # 获取PostgreSQL表信息
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_cursor = pg_conn.cursor()
            pg_cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
            """)
            pg_tables = set([row[0] for row in pg_cursor.fetchall()])
            
            # 比较结果
            self.log(f"SQLite表数量: {len(sqlite_tables)}")
            self.log(f"PostgreSQL表数量: {len(pg_tables)}")
            
            # 只在SQLite中的表
            sqlite_only = sqlite_tables - pg_tables
            if sqlite_only:
                self.log(f"只在SQLite中的表: {', '.join(sqlite_only)}")
                
            # 只在PostgreSQL中的表
            pg_only = pg_tables - sqlite_tables
            if pg_only:
                self.log(f"只在PostgreSQL中的表: {', '.join(pg_only)}")
                
            # 共同的表
            common_tables = sqlite_tables & pg_tables
            self.log(f"共同的表: {len(common_tables)} 个")
            
            # 详细比较每个表的行数
            self.log("\n=== 表行数比较 ===")
            for table in sorted(common_tables):
                try:
                    # SQLite行数
                    sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                    sqlite_count = sqlite_cursor.fetchone()[0]
                    
                    # PostgreSQL行数
                    pg_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    pg_count = pg_cursor.fetchone()[0]
                    
                    if sqlite_count == pg_count:
                        self.log(f"✅ {table}: {sqlite_count} 行 (一致)")
                    else:
                        self.log(f"⚠️ {table}: SQLite={sqlite_count}, PostgreSQL={pg_count} (不一致)")
                        
                except Exception as e:
                    self.log(f"❌ 比较表 {table} 时出错: {e}")
            
            sqlite_conn.close()
            pg_conn.close()
            
        except Exception as e:
            self.log(f"比较数据库时出错: {e}")
            
    def sync_databases(self):
        """同步SQLite数据到PostgreSQL"""
        self.log("🔄 开始数据库同步...")

        # 确认操作
        from tkinter import messagebox
        result = messagebox.askyesnocancel(
            "确认同步",
            "这将把SQLite的数据同步到PostgreSQL数据库。\n\n"
            "⚠️ 警告：这会覆盖PostgreSQL中的现有数据！\n\n"
            "是否继续？\n\n"
            "是 = 完整同步（覆盖所有数据）\n"
            "否 = 增量同步（仅同步差异）\n"
            "取消 = 取消操作"
        )

        if result is None:  # 取消
            self.log("❌ 用户取消了同步操作")
            return

        full_sync = result  # True = 完整同步, False = 增量同步

        try:
            # 导入迁移模块
            import sys
            migration_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '05_迁移工具')
            if migration_dir not in sys.path:
                sys.path.insert(0, migration_dir)

            from direct_migration_executor import DirectMigrationExecutor

            # 创建迁移器实例
            migrator = DirectMigrationExecutor()

            # 更新PostgreSQL配置
            migrator.pg_config = self.pg_config.copy()
            migrator.pg_config['database'] = 'postgres'  # 使用postgres数据库

            if full_sync:
                self.log("📋 执行完整同步...")
                success = migrator.execute_migration()
            else:
                self.log("📋 执行增量同步...")
                success = self._execute_incremental_sync(migrator)

            if success:
                self.log("✅ 数据库同步完成！")
                # 重新比较数据库
                self.compare_databases()
            else:
                self.log("❌ 数据库同步失败")

        except Exception as e:
            self.log(f"❌ 同步过程中出错: {e}")

    def _execute_incremental_sync(self, migrator):
        """执行增量同步"""
        try:
            self.log("📊 分析数据差异...")

            # 连接两个数据库
            sqlite_conn = sqlite3.connect(self.sqlite_path)
            pg_conn = psycopg2.connect(**migrator.pg_config)

            sqlite_cursor = sqlite_conn.cursor()
            pg_cursor = pg_conn.cursor()

            # 获取SQLite表信息
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            sqlite_tables = [row[0] for row in sqlite_cursor.fetchall()]

            total_synced = 0

            for table_name in sqlite_tables:
                try:
                    # 检查PostgreSQL中是否存在该表
                    pg_cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = %s
                        )
                    """, (table_name,))

                    table_exists = pg_cursor.fetchone()[0]

                    if not table_exists:
                        self.log(f"  📋 表 {table_name} 在PostgreSQL中不存在，跳过")
                        continue

                    # 获取行数差异
                    sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    sqlite_count = sqlite_cursor.fetchone()[0]

                    pg_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                    pg_count = pg_cursor.fetchone()[0]

                    if sqlite_count > pg_count:
                        diff = sqlite_count - pg_count
                        self.log(f"  🔄 同步表 {table_name}: 需要添加 {diff} 行")

                        # 简单的增量同步：删除PostgreSQL中的数据，重新插入SQLite的数据
                        pg_cursor.execute(f'DELETE FROM "{table_name}"')

                        # 读取SQLite数据并插入PostgreSQL
                        df = pd.read_sql_query(f'SELECT * FROM `{table_name}`', sqlite_conn)

                        if len(df) > 0:
                            # 获取列信息
                            sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                            columns = [col[1] for col in sqlite_cursor.fetchall()]

                            # 批量插入
                            quoted_columns = [f'"{col}"' for col in columns]
                            placeholders = ', '.join(['%s'] * len(columns))
                            insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'

                            batch_size = 1000
                            for i in range(0, len(df), batch_size):
                                batch_df = df.iloc[i:i+batch_size]
                                batch_data = []
                                for _, row in batch_df.iterrows():
                                    row_data = [row[col] if col in row and not pd.isna(row[col]) else None for col in columns]
                                    batch_data.append(tuple(row_data))

                                pg_cursor.executemany(insert_sql, batch_data)
                                pg_conn.commit()

                        total_synced += diff
                        self.log(f"    ✅ 表 {table_name} 同步完成")

                    elif sqlite_count == pg_count:
                        self.log(f"  ✅ 表 {table_name}: 数据一致，无需同步")
                    else:
                        self.log(f"  ⚠️ 表 {table_name}: PostgreSQL数据更多，跳过同步")

                except Exception as e:
                    self.log(f"  ❌ 同步表 {table_name} 失败: {e}")

            sqlite_conn.close()
            pg_conn.close()

            self.log(f"✅ 增量同步完成，总计同步: {total_synced} 行")
            return True

        except Exception as e:
            self.log(f"❌ 增量同步失败: {e}")
            return False

    def open_pgadmin(self):
        """打开pgAdmin"""
        # 尝试多个可能的pgAdmin路径
        possible_paths = [
            # 系统安装的pgAdmin
            r"C:\Program Files\pgAdmin 4\bin\pgAdmin4.exe",
            r"C:\Program Files (x86)\pgAdmin 4\bin\pgAdmin4.exe",
            # PostgreSQL安装目录中的pgAdmin
            os.path.join(self.pg_bin, 'pgAdmin4.exe'),
            # 用户目录中的pgAdmin
            os.path.expanduser(r"~\AppData\Local\Programs\pgAdmin 4\pgAdmin4.exe"),
        ]

        pgadmin_found = False
        for pgadmin_path in possible_paths:
            if os.path.exists(pgadmin_path):
                try:
                    subprocess.Popen([pgadmin_path])
                    self.log(f"✅ 已启动pgAdmin4: {pgadmin_path}")
                    pgadmin_found = True
                    break
                except Exception as e:
                    self.log(f"启动pgAdmin4失败 ({pgadmin_path}): {e}")

        if not pgadmin_found:
            self.log("❌ 未找到pgAdmin4，尝试在浏览器中打开...")
            try:
                import webbrowser
                webbrowser.open('http://localhost:5050')
                self.log("✅ 已在浏览器中打开pgAdmin (http://localhost:5050)")
            except Exception as e:
                self.log(f"打开pgAdmin失败: {e}")
                # 尝试其他端口
                try:
                    webbrowser.open('http://127.0.0.1:5050')
                    self.log("✅ 已在浏览器中打开pgAdmin (http://127.0.0.1:5050)")
                except Exception as e2:
                    self.log(f"打开pgAdmin完全失败: {e2}")
                
    def open_psql(self):
        """打开psql命令行"""
        psql_path = os.path.join(self.pg_bin, 'psql.exe')
        if os.path.exists(psql_path):
            try:
                # 在新的命令提示符窗口中打开psql
                cmd = f'start cmd /k "cd /d "{self.pg_bin}" && psql.exe -h localhost -p 5432 -U postgres -d postgres"'
                subprocess.run(cmd, shell=True)
                self.log("✅ 已启动psql命令行")
            except Exception as e:
                self.log(f"启动psql失败: {e}")
        else:
            self.log(f"❌ psql.exe不存在: {psql_path}")
            
    def equipment_id_sync(self):
        """启动Equipment_ID智能同步工具"""
        try:
            import subprocess
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Equipment_ID智能同步.py")
            if os.path.exists(script_path):
                subprocess.Popen([sys.executable, script_path])
                self.log("✅ 已启动Equipment_ID智能同步工具")
            else:
                self.log(f"❌ Equipment_ID智能同步工具不存在: {script_path}")
        except Exception as e:
            self.log(f"启动Equipment_ID智能同步工具失败: {e}")

    def open_data_dir(self):
        """打开数据目录"""
        try:
            os.startfile(self.pg_dir)
            self.log(f"✅ 已打开PostgreSQL目录: {self.pg_dir}")
        except Exception as e:
            self.log(f"打开目录失败: {e}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = PostgreSQLManager()
    app.run()
