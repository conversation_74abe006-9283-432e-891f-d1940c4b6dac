# -*- coding: utf-8 -*-
"""
修复剩余的PostgreSQL视图
基于实际的表结构创建Equipment_Valid和Current_Equipment视图
"""

import psycopg2
import sys

def fix_remaining_views():
    """修复剩余的视图"""
    print("🔧 修复剩余的PostgreSQL视图")
    print("="*50)
    
    # PostgreSQL连接配置
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        # 连接PostgreSQL
        pg_conn = psycopg2.connect(**pg_config)
        pg_conn.autocommit = True
        cursor = pg_conn.cursor()
        
        print("✅ 连接PostgreSQL成功")
        
        # 修复的视图定义（基于实际表结构）
        views = [
            # 1. Equipment_Valid - 基于实际Equipment_ID表结构
            {
                'name': 'Equipment_Valid',
                'sql': '''
                CREATE VIEW "Equipment_Valid" AS 
                SELECT
                    "Chair_Serial_No",
                    "STATE",
                    "Location",
                    "Quantity",
                    "Layer",
                    "Company",
                    "Effective_From",
                    "Effective_To",
                    "Rental",
                    "SIMCARDID",
                    "Import_Date",
                    "Last_Updated",
                    "CurrentFlag",
                    "DATE",
                    "Sim_Card_Model",
                    "Sim_Card_No"
                FROM "Equipment_ID"
                WHERE "STATE" IS NOT NULL
                AND "Chair_Serial_No" IS NOT NULL
                '''
            },
            
            # 2. Current_Equipment - 基于修复后的Equipment_Valid
            {
                'name': 'Current_Equipment',
                'sql': '''
                CREATE VIEW "Current_Equipment" AS 
                SELECT
                    ev."Chair_Serial_No",
                    ev."STATE",
                    ev."Location",
                    ev."Quantity",
                    ev."Layer",
                    ev."Company",
                    ev."Effective_From",
                    ev."Effective_To",
                    ev."Rental",
                    ev."SIMCARDID",
                    ev."Import_Date",
                    ev."Last_Updated",
                    ev."CurrentFlag",
                    ev."DATE",
                    ev."Sim_Card_Model",
                    ev."Sim_Card_No"
                FROM "Equipment_Valid" AS ev
                WHERE ev."CurrentFlag" = '1'
                OR ev."CurrentFlag" = 'Y'
                OR ev."CurrentFlag" = 'True'
                OR (ev."CurrentFlag" IS NULL AND ev."Effective_To" IS NULL)
                '''
            }
        ]
        
        created_views = []
        failed_views = []
        
        # 创建视图
        for view in views:
            view_name = view['name']
            view_sql = view['sql']
            
            try:
                print(f"\n🔧 创建视图: {view_name}")
                
                # 删除视图如果存在
                cursor.execute(f'DROP VIEW IF EXISTS "{view_name}" CASCADE')
                
                # 创建视图
                cursor.execute(view_sql)
                
                print(f"✅ 视图 {view_name} 创建成功")
                created_views.append(view_name)
                
            except Exception as e:
                print(f"❌ 视图 {view_name} 创建失败: {e}")
                failed_views.append((view_name, str(e)))
        
        pg_conn.close()
        
        # 报告结果
        print(f"\n📊 视图修复结果:")
        print(f"✅ 成功创建: {len(created_views)} 个视图")
        for view_name in created_views:
            print(f"  ✅ {view_name}")
        
        if failed_views:
            print(f"\n❌ 创建失败: {len(failed_views)} 个视图")
            for view_name, error in failed_views:
                print(f"  ❌ {view_name}: {error}")
        
        return len(failed_views) == 0
        
    except Exception as e:
        print(f"❌ 修复PostgreSQL视图失败: {e}")
        return False

def verify_all_views():
    """验证所有视图是否正常工作"""
    print("\n🔍 验证所有视图功能...")
    
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        conn = psycopg2.connect(**pg_config)
        cursor = conn.cursor()
        
        # 获取所有视图
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        views = cursor.fetchall()
        
        print(f"验证 {len(views)} 个视图:")
        
        total_rows = 0
        working_views = 0
        
        for (view_name,) in views:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{view_name}"')
                count = cursor.fetchone()[0]
                print(f"  ✅ {view_name}: {count:,} 行")
                total_rows += count
                working_views += 1
            except Exception as e:
                print(f"  ❌ {view_name}: 查询失败 - {e}")
        
        print(f"\n📊 视图统计:")
        print(f"  正常工作的视图: {working_views}/{len(views)}")
        print(f"  视图总数据量: {total_rows:,} 行")
        
        conn.close()
        return working_views == len(views)
        
    except Exception as e:
        print(f"❌ 验证视图失败: {e}")
        return False

def test_view_queries():
    """测试一些常用的视图查询"""
    print("\n🧪 测试常用视图查询...")
    
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        conn = psycopg2.connect(**pg_config)
        cursor = conn.cursor()
        
        # 测试查询
        test_queries = [
            {
                'name': '当前有效设备数量',
                'sql': 'SELECT COUNT(*) FROM "Current_Equipment"'
            },
            {
                'name': '有效设备总数',
                'sql': 'SELECT COUNT(*) FROM "Equipment_Valid"'
            },
            {
                'name': '销售组合数据',
                'sql': 'SELECT COUNT(*) FROM "Sales_Combined" WHERE "Total_Price" > 0'
            },
            {
                'name': '日销售统计',
                'sql': 'SELECT COUNT(*) FROM "Daily_Sales"'
            },
            {
                'name': '退款关闭汇总',
                'sql': 'SELECT COUNT(*) FROM "Refunding_Close_Summary"'
            }
        ]
        
        for query in test_queries:
            try:
                cursor.execute(query['sql'])
                result = cursor.fetchone()[0]
                print(f"  ✅ {query['name']}: {result:,}")
            except Exception as e:
                print(f"  ❌ {query['name']}: 查询失败 - {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试查询失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 修复PostgreSQL剩余视图")
    print("基于实际表结构，确保完全兼容")
    print("="*50)
    
    # 修复视图
    success = fix_remaining_views()
    
    if success:
        print("\n✅ 所有剩余视图修复成功！")
    else:
        print("\n⚠️ 部分视图修复失败")
    
    # 验证所有视图
    verify_all_views()
    
    # 测试常用查询
    test_view_queries()
    
    print("\n📋 完成！")
    print("现在您可以在pgAdmin4中看到完整的视图集合了：")
    print("服务器 > postgres > Schemas > public > Views")
    print("\n✅ 可用的视图功能：")
    print("  📊 IOT_Daily_Sales - IOT平台日销售统计")
    print("  📊 ZERO_Daily_Sales - ZERO平台日销售统计") 
    print("  📊 APP_Daily_Sales - APP平台日销售统计")
    print("  📊 Sales_Combined - 综合销售数据")
    print("  📊 Valid_Sales - 有效销售数据")
    print("  📊 Daily_Sales - 日销售汇总")
    print("  📊 Daily_Equipment_Sales - 设备日销售明细")
    print("  📊 Refunding_Close_Summary - 退款关闭汇总")
    print("  🏭 Equipment_Valid - 有效设备信息")
    print("  🏭 Current_Equipment - 当前设备状态")
    
    print("\n🎉 PostgreSQL迁移和视图创建全部完成！")
    print("您的数据处理应用现在可以使用PostgreSQL的强大功能了！")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
