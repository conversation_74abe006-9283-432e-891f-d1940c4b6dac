# -*- coding: utf-8 -*-
"""
检查实际的数据库表结构
验证哪些表真实存在
"""

import sqlite3
import os

def check_actual_tables():
    """检查实际存在的数据库表"""
    
    print("🔍 检查实际数据库表结构...")
    print("=" * 60)
    
    db_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"✅ 数据库文件存在: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"\n📋 实际存在的表 (共 {len(tables)} 个):")
        
        # 分类显示表
        sales_tables = []
        refunding_tables = []
        close_tables = []
        other_tables = []
        
        for table in tables:
            if table.endswith('_Sales') and not ('_Refunding' in table or '_Close' in table):
                sales_tables.append(table)
            elif '_Refunding' in table:
                refunding_tables.append(table)
            elif '_Close' in table:
                close_tables.append(table)
            else:
                other_tables.append(table)
        
        # 显示主销售表
        if sales_tables:
            print("\n  📊 主销售表:")
            for table in sales_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    ✅ {table:<20} ({count:,} 行)")
        
        # 显示退款表
        if refunding_tables:
            print("\n  💰 退款表:")
            for table in refunding_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    ✅ {table:<20} ({count:,} 行)")
        else:
            print("\n  💰 退款表:")
            print("    ❌ 没有找到退款表")
        
        # 显示关闭表
        if close_tables:
            print("\n  🔒 关闭表:")
            for table in close_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    ✅ {table:<20} ({count:,} 行)")
        else:
            print("\n  🔒 关闭表:")
            print("    ❌ 没有找到关闭表")
        
        # 显示其他表
        if other_tables:
            print("\n  🗃️ 其他表:")
            for table in other_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    ✅ {table:<20} ({count:,} 行)")
        
        conn.close()
        
        # 检查是否有APP相关表
        app_tables = [t for t in tables if 'APP' in t]
        if app_tables:
            print(f"\n📱 APP相关表:")
            for table in app_tables:
                print(f"    ✅ {table}")
        else:
            print(f"\n📱 APP相关表:")
            print("    ❌ 没有找到APP相关表")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")
        return False

def check_api_order_logic():
    """检查Api order的处理逻辑"""
    
    print("\n🔍 检查Api order处理逻辑...")
    print("=" * 60)
    
    # 检查优化版脚本中的Api order处理
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts\data_import_optimized.py"
    
    if not os.path.exists(script_path):
        print(f"❌ 优化版脚本不存在: {script_path}")
        return False
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📋 Api order处理逻辑:")
        
        # 检查API订单分离逻辑
        if '_separate_api_orders' in content:
            print("  ✅ API订单分离功能: 存在")
            
            # 查找具体的分离条件
            if "Order_types == 'Api order'" in content:
                print("  ✅ 分离条件: Order_types == 'Api order'")
            else:
                print("  ❓ 分离条件: 需要进一步检查")
        else:
            print("  ❌ API订单分离功能: 不存在")
        
        # 检查API订单目标表
        if "API订单统一导入到APP_Sales表" in content:
            print("  ✅ 目标表: APP_Sales")
        elif "'APP_Sales'" in content:
            print("  ✅ 目标表: APP_Sales (从代码推断)")
        else:
            print("  ❓ 目标表: 需要进一步检查")
        
        # 检查是否有状态路由
        if "_determine_target_table" in content:
            print("  ✅ 状态路由: 支持 (API订单可能会进一步根据状态路由)")
        else:
            print("  ❌ 状态路由: 不支持")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查Api order逻辑失败: {e}")
        return False

def check_table_creation_scripts():
    """检查表创建脚本"""
    
    print("\n🔍 检查表创建脚本...")
    print("=" * 60)
    
    # 检查是否有创建Refunding和Close表的脚本
    scripts_to_check = [
        r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts\create_refunding_close_tables.py",
        r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts\create_refunding_close_tables_simple.py"
    ]
    
    for script_path in scripts_to_check:
        if os.path.exists(script_path):
            print(f"  ✅ 找到表创建脚本: {os.path.basename(script_path)}")
        else:
            print(f"  ❌ 脚本不存在: {os.path.basename(script_path)}")
    
    # 检查数据库模型定义
    models_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\database\models.py"
    
    if os.path.exists(models_path):
        print(f"  ✅ 数据库模型定义存在")
        
        try:
            with open(models_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查定义的表
            if 'APP_Sales_Refunding' in content:
                print("    ✅ APP_Sales_Refunding 已定义")
            else:
                print("    ❌ APP_Sales_Refunding 未定义")
            
            if 'APP_Sales_Close' in content:
                print("    ✅ APP_Sales_Close 已定义")
            else:
                print("    ❌ APP_Sales_Close 未定义")
                
        except Exception as e:
            print(f"    ❌ 读取模型定义失败: {e}")
    else:
        print(f"  ❌ 数据库模型定义不存在")
    
    return True

def main():
    """主函数"""
    
    print("🚀 实际表结构检查")
    print("=" * 80)
    
    # 执行检查
    tests = [
        ("实际数据库表", check_actual_tables),
        ("Api order处理逻辑", check_api_order_logic),
        ("表创建脚本", check_table_creation_scripts)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 检查时出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 检查结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"  {test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📋 总体结果: {passed}/{total} 项检查完成")
    
    print("\n📋 结论:")
    print("  1. 检查实际数据库中存在哪些表")
    print("  2. 确认Api order的实际处理流程")
    print("  3. 验证APP_Sales_Refunding和APP_Sales_Close是否真实存在")
    print("  4. 如果不存在，需要运行表创建脚本")
    
    return True

if __name__ == "__main__":
    result = main()
    input(f"\n按回车键退出... (检查完成)")
