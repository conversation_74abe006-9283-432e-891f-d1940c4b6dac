# -*- coding: utf-8 -*-
"""
基于Equipment_ID的智能数据同步工具
检测包含Equipment_ID的表，并基于Equipment_ID进行精确的数据同步
"""

import os
import sqlite3
import psycopg2
import pandas as pd
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import hashlib

class EquipmentIDSmartSync:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Equipment_ID智能同步工具")
        self.root.geometry("900x600")
        
        # 数据库配置
        self.sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
        self.pg_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'postgres',
            'user': 'postgres',
            'password': 'zerochon'
        }
        
        self.equipment_tables = []  # 包含Equipment_ID的表
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = ttk.Label(self.root, text="Equipment_ID智能同步工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 配置框架
        config_frame = ttk.LabelFrame(self.root, text="数据库配置", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # SQLite路径
        ttk.Label(config_frame, text="SQLite数据库:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.sqlite_var = tk.StringVar(value=self.sqlite_path)
        ttk.Entry(config_frame, textvariable=self.sqlite_var, width=60).grid(row=0, column=1, padx=5)
        
        # PostgreSQL配置
        ttk.Label(config_frame, text="PostgreSQL:").grid(row=1, column=0, sticky=tk.W, padx=5)
        pg_info = f"{self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}"
        ttk.Label(config_frame, text=pg_info).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 操作按钮
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="🔍 扫描Equipment_ID表", command=self.scan_equipment_tables).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📊 分析数据差异", command=self.analyze_differences).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 智能同步", command=self.smart_sync).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧹 清理重复", command=self.clean_duplicates).pack(side=tk.LEFT, padx=5)
        
        # Equipment_ID表列表
        tables_frame = ttk.LabelFrame(self.root, text="包含Equipment_ID的表", padding="10")
        tables_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.tables_listbox = tk.Listbox(tables_frame, height=4, selectmode=tk.MULTIPLE)
        self.tables_listbox.pack(fill=tk.X)
        
        # 日志区域
        log_frame = ttk.LabelFrame(self.root, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 初始扫描
        self.scan_equipment_tables()
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def scan_equipment_tables(self):
        """扫描包含Equipment_ID的表"""
        self.log("🔍 扫描包含Equipment_ID的表...")
        
        try:
            sqlite_conn = sqlite3.connect(self.sqlite_var.get())
            sqlite_cursor = sqlite_conn.cursor()
            
            # 获取所有表
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            all_tables = [row[0] for row in sqlite_cursor.fetchall()]
            
            self.equipment_tables = []
            
            for table_name in all_tables:
                try:
                    # 检查表是否包含Equipment_ID列
                    sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in sqlite_cursor.fetchall()]
                    
                    if 'Equipment_ID' in columns:
                        # 检查Equipment_ID列是否有数据
                        sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE Equipment_ID IS NOT NULL AND Equipment_ID != ''")
                        count = sqlite_cursor.fetchone()[0]
                        
                        if count > 0:
                            self.equipment_tables.append({
                                'name': table_name,
                                'columns': columns,
                                'equipment_count': count
                            })
                            self.log(f"  ✅ {table_name}: {count:,} 条Equipment_ID记录")
                        else:
                            self.log(f"  ⚪ {table_name}: 包含Equipment_ID列但无数据")
                    
                except Exception as e:
                    self.log(f"  ❌ 检查表 {table_name} 失败: {e}")
            
            sqlite_conn.close()
            
            # 更新列表框
            self.tables_listbox.delete(0, tk.END)
            for table_info in self.equipment_tables:
                display_text = f"{table_info['name']} ({table_info['equipment_count']:,} 条记录)"
                self.tables_listbox.insert(tk.END, display_text)
            
            # 默认选中所有表
            for i in range(len(self.equipment_tables)):
                self.tables_listbox.selection_set(i)
            
            self.log(f"✅ 扫描完成，找到 {len(self.equipment_tables)} 个包含Equipment_ID的表")
            
        except Exception as e:
            self.log(f"❌ 扫描失败: {e}")
            
    def analyze_differences(self):
        """分析数据差异"""
        selected_indices = self.tables_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要分析的表")
            return
            
        self.log("📊 分析Equipment_ID数据差异...")
        
        try:
            sqlite_conn = sqlite3.connect(self.sqlite_var.get())
            pg_conn = psycopg2.connect(**self.pg_config)
            
            total_differences = 0
            
            for index in selected_indices:
                table_info = self.equipment_tables[index]
                table_name = table_info['name']
                
                self.log(f"🔍 分析表: {table_name}")
                
                try:
                    # 检查PostgreSQL中是否存在该表
                    pg_cursor = pg_conn.cursor()
                    pg_cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = %s
                        )
                    """, (table_name,))
                    
                    if not pg_cursor.fetchone()[0]:
                        self.log(f"  ❌ 表 {table_name} 在PostgreSQL中不存在")
                        continue
                    
                    # 检查PostgreSQL表是否有Equipment_ID列
                    pg_cursor.execute("""
                        SELECT column_name FROM information_schema.columns 
                        WHERE table_name = %s AND column_name = 'Equipment_ID'
                    """, (table_name,))
                    
                    if not pg_cursor.fetchone():
                        self.log(f"  ❌ PostgreSQL表 {table_name} 没有Equipment_ID列")
                        continue
                    
                    # 读取两个数据库的数据
                    sqlite_df = pd.read_sql_query(f"SELECT * FROM `{table_name}` WHERE Equipment_ID IS NOT NULL AND Equipment_ID != ''", sqlite_conn)
                    pg_df = pd.read_sql_query(f'SELECT * FROM "{table_name}" WHERE "Equipment_ID" IS NOT NULL AND "Equipment_ID" != \'\'', pg_conn)
                    
                    # 基于Equipment_ID比较
                    sqlite_equipment_ids = set(sqlite_df['Equipment_ID'].astype(str))
                    pg_equipment_ids = set(pg_df['Equipment_ID'].astype(str))
                    
                    # 只在SQLite中的Equipment_ID
                    only_in_sqlite = sqlite_equipment_ids - pg_equipment_ids
                    # 只在PostgreSQL中的Equipment_ID
                    only_in_pg = pg_equipment_ids - sqlite_equipment_ids
                    # 共同的Equipment_ID
                    common_equipment_ids = sqlite_equipment_ids & pg_equipment_ids
                    
                    self.log(f"  📋 SQLite独有Equipment_ID: {len(only_in_sqlite)}")
                    self.log(f"  📋 PostgreSQL独有Equipment_ID: {len(only_in_pg)}")
                    self.log(f"  📋 共同Equipment_ID: {len(common_equipment_ids)}")
                    
                    # 检查共同Equipment_ID的数据差异
                    field_differences = 0
                    for equipment_id in common_equipment_ids:
                        sqlite_row = sqlite_df[sqlite_df['Equipment_ID'].astype(str) == equipment_id].iloc[0]
                        pg_row = pg_df[pg_df['Equipment_ID'].astype(str) == equipment_id].iloc[0]
                        
                        # 比较每个字段
                        for col in sqlite_df.columns:
                            if col in pg_df.columns:
                                sqlite_val = str(sqlite_row[col]) if pd.notna(sqlite_row[col]) else ''
                                pg_val = str(pg_row[col]) if pd.notna(pg_row[col]) else ''
                                
                                if sqlite_val != pg_val:
                                    field_differences += 1
                                    if field_differences <= 5:  # 只显示前5个差异
                                        self.log(f"    🔸 Equipment_ID {equipment_id}, 字段 {col}: SQLite='{sqlite_val}' vs PG='{pg_val}'")
                    
                    if field_differences > 5:
                        self.log(f"    ... 还有 {field_differences - 5} 个字段差异")
                    
                    table_total = len(only_in_sqlite) + len(only_in_pg) + field_differences
                    total_differences += table_total
                    
                    self.log(f"  📊 表 {table_name} 总差异: {table_total}")
                    
                except Exception as e:
                    self.log(f"  ❌ 分析表 {table_name} 失败: {e}")
            
            sqlite_conn.close()
            pg_conn.close()
            
            self.log(f"✅ 分析完成，总差异数: {total_differences}")
            
        except Exception as e:
            self.log(f"❌ 分析失败: {e}")
            
    def smart_sync(self):
        """智能同步"""
        selected_indices = self.tables_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要同步的表")
            return
            
        # 确认操作
        result = messagebox.askyesnocancel(
            "确认同步",
            "选择同步策略：\n\n"
            "是 = 以SQLite为准（SQLite数据覆盖PostgreSQL）\n"
            "否 = 智能合并（保留最新数据）\n"
            "取消 = 取消操作"
        )
        
        if result is None:
            self.log("❌ 用户取消了同步操作")
            return
            
        sqlite_priority = result
        
        self.log(f"🔄 开始智能同步 ({'SQLite优先' if sqlite_priority else '智能合并'})...")
        
        try:
            sqlite_conn = sqlite3.connect(self.sqlite_var.get())
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_conn.autocommit = False  # 使用事务
            
            total_synced = 0
            
            for index in selected_indices:
                table_info = self.equipment_tables[index]
                table_name = table_info['name']
                
                try:
                    self.log(f"🔄 同步表: {table_name}")
                    
                    # 检查PostgreSQL表
                    pg_cursor = pg_conn.cursor()
                    pg_cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = %s
                        )
                    """, (table_name,))
                    
                    if not pg_cursor.fetchone()[0]:
                        self.log(f"  ❌ PostgreSQL表 {table_name} 不存在，跳过")
                        continue
                    
                    # 读取数据
                    sqlite_df = pd.read_sql_query(f"SELECT * FROM `{table_name}` WHERE Equipment_ID IS NOT NULL AND Equipment_ID != ''", sqlite_conn)
                    pg_df = pd.read_sql_query(f'SELECT * FROM "{table_name}" WHERE "Equipment_ID" IS NOT NULL AND "Equipment_ID" != \'\'', pg_conn)
                    
                    if len(sqlite_df) == 0:
                        self.log(f"  ⚪ SQLite表 {table_name} 无Equipment_ID数据，跳过")
                        continue
                    
                    synced_count = 0
                    
                    # 获取列信息
                    columns = list(sqlite_df.columns)
                    quoted_columns = [f'"{col}"' for col in columns]
                    
                    for _, sqlite_row in sqlite_df.iterrows():
                        equipment_id = str(sqlite_row['Equipment_ID'])
                        
                        # 检查PostgreSQL中是否存在该Equipment_ID
                        pg_cursor.execute(f'SELECT * FROM "{table_name}" WHERE "Equipment_ID" = %s', (equipment_id,))
                        pg_existing = pg_cursor.fetchone()
                        
                        if pg_existing is None:
                            # 新增记录
                            placeholders = ', '.join(['%s'] * len(columns))
                            insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'
                            
                            values = [sqlite_row[col] if pd.notna(sqlite_row[col]) else None for col in columns]
                            pg_cursor.execute(insert_sql, values)
                            synced_count += 1
                            
                        else:
                            # 更新记录（如果有差异）
                            if sqlite_priority:
                                # SQLite优先：直接更新
                                set_clauses = [f'"{col}" = %s' for col in columns if col != 'Equipment_ID']
                                update_sql = f'UPDATE "{table_name}" SET {", ".join(set_clauses)} WHERE "Equipment_ID" = %s'
                                
                                values = [sqlite_row[col] if pd.notna(sqlite_row[col]) else None for col in columns if col != 'Equipment_ID']
                                values.append(equipment_id)
                                
                                pg_cursor.execute(update_sql, values)
                                synced_count += 1
                            else:
                                # 智能合并：检查是否有实际差异
                                has_difference = False
                                pg_row_dict = dict(zip([desc[0] for desc in pg_cursor.description], pg_existing))
                                
                                for col in columns:
                                    sqlite_val = str(sqlite_row[col]) if pd.notna(sqlite_row[col]) else ''
                                    pg_val = str(pg_row_dict.get(col, '')) if pg_row_dict.get(col) is not None else ''
                                    
                                    if sqlite_val != pg_val:
                                        has_difference = True
                                        break
                                
                                if has_difference:
                                    set_clauses = [f'"{col}" = %s' for col in columns if col != 'Equipment_ID']
                                    update_sql = f'UPDATE "{table_name}" SET {", ".join(set_clauses)} WHERE "Equipment_ID" = %s'
                                    
                                    values = [sqlite_row[col] if pd.notna(sqlite_row[col]) else None for col in columns if col != 'Equipment_ID']
                                    values.append(equipment_id)
                                    
                                    pg_cursor.execute(update_sql, values)
                                    synced_count += 1
                    
                    pg_conn.commit()
                    total_synced += synced_count
                    
                    self.log(f"  ✅ 表 {table_name} 同步完成: {synced_count} 条记录")
                    
                except Exception as e:
                    pg_conn.rollback()
                    self.log(f"  ❌ 同步表 {table_name} 失败: {e}")
            
            sqlite_conn.close()
            pg_conn.close()
            
            self.log(f"✅ 智能同步完成，总计: {total_synced} 条记录")
            
        except Exception as e:
            self.log(f"❌ 智能同步失败: {e}")
            
    def clean_duplicates(self):
        """清理重复的Equipment_ID"""
        if not messagebox.askyesno("确认清理", "是否清理PostgreSQL中重复的Equipment_ID记录？\n(保留最新的记录)"):
            return
            
        self.log("🧹 清理重复的Equipment_ID记录...")
        
        try:
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_cursor = pg_conn.cursor()
            
            total_cleaned = 0
            
            for table_info in self.equipment_tables:
                table_name = table_info['name']
                
                try:
                    # 查找重复的Equipment_ID
                    pg_cursor.execute(f'''
                        SELECT "Equipment_ID", COUNT(*) as count
                        FROM "{table_name}"
                        WHERE "Equipment_ID" IS NOT NULL AND "Equipment_ID" != ''
                        GROUP BY "Equipment_ID"
                        HAVING COUNT(*) > 1
                    ''')
                    
                    duplicates = pg_cursor.fetchall()
                    
                    if duplicates:
                        self.log(f"  🔍 表 {table_name} 发现 {len(duplicates)} 个重复Equipment_ID")
                        
                        for equipment_id, count in duplicates:
                            # 保留最新的记录，删除其他的
                            pg_cursor.execute(f'''
                                DELETE FROM "{table_name}"
                                WHERE "Equipment_ID" = %s
                                AND ctid NOT IN (
                                    SELECT ctid FROM "{table_name}"
                                    WHERE "Equipment_ID" = %s
                                    ORDER BY ctid DESC
                                    LIMIT 1
                                )
                            ''', (equipment_id, equipment_id))
                            
                            deleted_count = count - 1
                            total_cleaned += deleted_count
                            
                        self.log(f"    ✅ 清理了 {sum(count-1 for _, count in duplicates)} 条重复记录")
                    else:
                        self.log(f"  ✅ 表 {table_name} 无重复Equipment_ID")
                        
                except Exception as e:
                    self.log(f"  ❌ 清理表 {table_name} 失败: {e}")
            
            pg_conn.commit()
            pg_conn.close()
            
            self.log(f"✅ 清理完成，总计清理: {total_cleaned} 条重复记录")
            
        except Exception as e:
            self.log(f"❌ 清理失败: {e}")
            
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = EquipmentIDSmartSync()
    app.run()
