# -*- coding: utf-8 -*-
"""
编码修复脚本
修复中文输出乱码问题
"""

import re
import os

def fix_encoding_issues():
    """修复编码问题"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在文件开头添加更强的编码设置
    encoding_setup = '''# -*- coding: utf-8 -*-
"""
数据处理应用系统 - 模块化设计版本 7.0
功能：智能Transaction Num检测 + 模块化架构 + 日期分组处理
作者：数据处理应用系统
版本：7.0
日期：2025年
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import os
import warnings
import argparse
import sys
import io
import codecs
from typing import Dict, List, Tuple, Optional, Any
import time
from tqdm import tqdm  # 进度条显示

# ==================== 强化编码设置 ====================
import locale
import platform

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LANG'] = 'zh_CN.UTF-8'

# Windows系统特殊处理
if platform.system() == 'Windows':
    # 设置控制台代码页为UTF-8
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

# 设置locale
try:
    if platform.system() == 'Windows':
        locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
    else:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except:
        pass

# 重新配置标准输出和错误输出
def setup_utf8_output():
    """设置UTF-8输出"""
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass
    else:
        # 对于较老的Python版本
        try:
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        except:
            pass

setup_utf8_output()

# 设置pandas显示选项
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

# 自定义print函数，确保UTF-8输出
def safe_print(*args, **kwargs):
    """安全的中文打印函数"""
    try:
        # 将所有参数转换为字符串并确保UTF-8编码
        safe_args = []
        for arg in args:
            if isinstance(arg, str):
                safe_args.append(arg)
            else:
                safe_args.append(str(arg))
        
        # 使用原始print函数
        print(*safe_args, **kwargs)
        
        # 强制刷新输出缓冲区
        sys.stdout.flush()
    except UnicodeEncodeError:
        # 如果仍然有编码错误，使用ASCII安全模式
        safe_args = []
        for arg in args:
            if isinstance(arg, str):
                safe_args.append(arg.encode('ascii', 'replace').decode('ascii'))
            else:
                safe_args.append(str(arg).encode('ascii', 'replace').decode('ascii'))
        print(*safe_args, **kwargs)
    except Exception as e:
        # 最后的备用方案
        print(f"[输出错误: {e}]")

# 替换内置print函数
import builtins
builtins.print = safe_print

'''
    
    # 查找第一个import语句的位置
    import_match = re.search(r'^import\s+', content, re.MULTILINE)
    if import_match:
        # 替换从文件开头到第一个import语句之间的内容
        first_import_pos = import_match.start()
        # 找到文件开头的注释和导入部分
        header_end = content.find('# 设置输出编码为UTF-8，解决乱码问题')
        if header_end != -1:
            # 找到这个注释块的结束位置
            next_section = content.find('# ======================【日志系统优化】======================', header_end)
            if next_section != -1:
                # 替换整个编码设置部分
                new_content = encoding_setup + content[next_section:]
            else:
                new_content = encoding_setup + content[header_end:]
        else:
            new_content = encoding_setup + content[first_import_pos:]
    else:
        new_content = encoding_setup + content
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 编码修复完成！")
    print("已添加强化的UTF-8编码设置和安全打印函数")

def test_encoding():
    """测试编码是否正常"""
    test_strings = [
        "开始数据处理...",
        "第一文件加载完成: 27 条记录", 
        "Transaction ID列检测完成",
        "匹配模式: Transaction ID匹配",
        "数据处理完成"
    ]
    
    print("\n🧪 测试中文输出:")
    for test_str in test_strings:
        print(f"✓ {test_str}")
    
    print("\n如果上面的中文显示正常，说明编码修复成功！")

if __name__ == "__main__":
    print("开始修复编码问题...")
    
    # 1. 修复编码设置
    fix_encoding_issues()
    
    # 2. 测试编码
    test_encoding()
    
    print("\n🎉 编码修复完成！")
    print("现在重新运行应用程序应该不会出现中文乱码了。")
