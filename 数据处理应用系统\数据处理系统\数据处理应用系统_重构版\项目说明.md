# 数据处理应用系统 - 重构版

## 📁 目录结构

### 01_主程序
**描述**: 主要应用程序文件  
**文件数量**: 3 个  

### 02_PostgreSQL管理
**描述**: PostgreSQL安装、管理和迁移工具  
**文件数量**: 5 个  

### 03_配置文件
**描述**: 系统配置文件  
**文件数量**: 1 个  

### 04_测试文件
**描述**: 各种测试和验证脚本  
**文件数量**: 19 个  

### 05_迁移工具
**描述**: 数据迁移相关工具  
**文件数量**: 6 个  

### 06_数据库工具
**描述**: 数据库创建和修复工具  
**文件数量**: 5 个  

### 07_文档说明
**描述**: 文档和说明文件  
**文件数量**: 9 个  

### 08_备份文件
**描述**: 数据库备份文件  
**文件数量**: 23 个  

### 09_临时文件
**描述**: 临时和分析文件  
**文件数量**: 2 个  

### 10_整理工具
**描述**: 文件整理和管理工具  
**文件数量**: 2 个  

## 🚀 快速开始

### 主程序运行
```bash
cd 01_主程序
python 数据处理与导入应用_完整版.py
```

### PostgreSQL管理
```bash
cd 02_PostgreSQL管理
# 安装PostgreSQL
python postgresql_setup.py
# 启动PostgreSQL
python postgresql_manager.py start
# 迁移界面
python migration_ui.py
```

### 测试验证
```bash
cd 04_测试文件
python comprehensive_verification.py
```

## 📊 功能特点

- ✅ **双数据库支持**: SQLite + PostgreSQL
- ✅ **智能数据导入**: 自动识别平台和状态
- ✅ **智能迁移**: Equipment_ID精确匹配
- ✅ **自动备份**: 操作前自动备份
- ✅ **错误恢复**: 失败时自动回滚
- ✅ **完整日志**: 详细的操作记录

## 📋 数据状态

- **PostgreSQL**: 599,615行数据，21个表
- **SQLite**: 完全同步，作为本地备份
- **IOT设备**: 3,226个
- **ZERO设备**: 565个
- **数据范围**: 2025-03-01 到 2025-06-12

---
*最后整理时间: 2025-06-20 11:02:34*
