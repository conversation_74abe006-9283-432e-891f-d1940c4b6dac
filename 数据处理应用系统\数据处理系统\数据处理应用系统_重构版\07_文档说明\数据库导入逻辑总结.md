# 📊 数据库导入逻辑总结

## 🎯 **核心设计理念**

数据库导入系统采用**智能分表导入**策略，根据订单状态自动将数据路由到对应的表中，实现数据的分类管理和高效查询。

## 🏗️ **表结构设计**

### ✅ **分平台分状态表结构**

每个平台（IOT、ZERO、APP）都有对应的表组：

```
📁 IOT平台
├── IOT_Sales (正常完成订单)
├── IOT_Sales_Refunding (退款中订单)
└── IOT_Sales_Close (关闭订单)

📁 ZERO平台  
├── ZERO_Sales (正常完成订单)
├── ZERO_Sales_Refunding (退款中订单)
└── ZERO_Sales_Close (关闭订单)

📁 APP平台
├── APP_Sales (正常完成订单)
├── APP_Sales_Refunding (退款中订单)
└── APP_Sales_Close (关闭订单)
```

### ✅ **状态映射规则**

```python
STATUS_TABLE_MAPPING = {
    'IOT': {
        'Finish': 'IOT_Sales',           # 完成 → 主表
        'Finished': 'IOT_Sales',         # 已完成 → 主表
        'Refunded': 'IOT_Sales_Refunding',   # 已退款 → 退款表
        'Refunding': 'IOT_Sales_Refunding',  # 退款中 → 退款表
        'Close': 'IOT_Sales_Close',          # 关闭 → 关闭表
        'Closed': 'IOT_Sales_Close'          # 已关闭 → 关闭表
    },
    # ZERO和APP平台规则相同
}
```

## 🔄 **智能导入流程**

### **第1步：数据预处理**
```python
def load_and_validate_data(file_path, platform):
    # 1. 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl')
    
    # 2. 验证必需列
    required_columns = ['Order_price', 'Order_time', 'Equipment_ID']
    
    # 3. 数据清洗
    df = self._clean_data(df)
    
    # 4. 标准化日期格式
    df['Order_time'] = df['Order_time'].apply(self.standardize_date)
    
    # 5. 添加导入时间戳
    df['Import_Date'] = datetime.now().strftime("%Y-%m-%d")
    
    return df
```

### **第2步：重复数据检查**
```python
def check_duplicate_data(df, platform):
    # 基于关键字段检查重复：Order_time, Payment_date, Equipment_ID
    
    # 分类结果：
    # - 完全重复：所有字段都相同
    # - 部分重复：关键字段相同但价格不同
    # - 新数据：完全新的记录
    
    return fully_duplicate, partial_different, new_data
```

### **第3步：智能表路由**
```python
def _determine_target_table(platform, order_status):
    # 1. 清理和标准化状态
    status = str(order_status).strip()
    
    # 2. 获取平台的状态映射
    status_mapping = STATUS_TABLE_MAPPING.get(platform, {})
    
    # 3. 查找匹配的状态（不区分大小写）
    for status_key, table_name in status_mapping.items():
        if status.lower() == status_key.lower():
            return table_name
    
    # 4. 默认返回主表
    return f"{platform}_Sales"
```

### **第4步：数据分布分析**
```python
def _analyze_data_distribution(df, platform):
    distribution = {}
    
    # 统计每个表将要插入的记录数
    for _, row in df.iterrows():
        target_table = self._determine_target_table(platform, row.get('Order_status', ''))
        distribution[target_table] = distribution.get(target_table, 0) + 1
    
    # 输出：{'IOT_Sales': 100, 'IOT_Sales_Refunding': 5, 'IOT_Sales_Close': 2}
    return distribution
```

### **第5步：智能分表插入**
```python
def smart_insert_data(df, platform):
    # 1. 按目标表分组数据
    table_data = {}
    for _, row in df.iterrows():
        target_table = self._determine_target_table(platform, row.get('Order_status', ''))
        if target_table not in table_data:
            table_data[target_table] = []
        table_data[target_table].append(row)
    
    # 2. 分表插入数据
    insert_results = {}
    for table_name, rows in table_data.items():
        table_df = pd.DataFrame(rows)
        inserted_count = self._insert_to_table(table_df, table_name)
        insert_results[table_name] = inserted_count
    
    return insert_results
```

### **第6步：事务安全插入**
```python
def _insert_to_table(df, table_name):
    with get_connection() as conn:
        # 1. 开始事务
        conn.connection.execute("BEGIN")
        
        try:
            # 2. 分批插入（默认1000条一批）
            for i in range(0, len(df), self.batch_size):
                batch_df = df.iloc[i:i+self.batch_size]
                batch_df.to_sql(table_name, conn.connection, if_exists='append', index=False)
            
            # 3. 提交事务
            conn.connection.commit()
            
        except Exception as e:
            # 4. 失败时回滚
            conn.connection.rollback()
            raise DatabaseError(f"批量插入数据失败: {e}")
        
        return actual_inserted_count
```

## 📊 **导入结果示例**

### **输入数据**
```
文件：IOT_sales_20241218.xlsx
平台：IOT
记录数：107条
```

### **数据分布分析**
```
📊 数据分布分析: {
    'IOT_Sales': 95,           # 95条正常完成订单
    'IOT_Sales_Refunding': 8,  # 8条退款订单
    'IOT_Sales_Close': 4       # 4条关闭订单
}
```

### **插入结果**
```
✅ 表 IOT_Sales: 插入 95 条记录
✅ 表 IOT_Sales_Refunding: 插入 8 条记录  
✅ 表 IOT_Sales_Close: 插入 4 条记录
🎯 智能插入完成，总计插入: 107 条记录
```

## 🛡️ **安全保障机制**

### ✅ **数据完整性保障**
- **事务安全**：所有插入操作在事务中进行，失败时自动回滚
- **批量处理**：大数据集分批处理，避免内存溢出
- **重复检查**：插入前检查重复数据，避免数据冗余

### ✅ **错误处理机制**
- **文件验证**：检查文件格式、大小、必需列
- **数据验证**：验证数据类型、格式、完整性
- **异常捕获**：详细的异常信息和恢复建议

### ✅ **日志记录**
- **操作日志**：记录每个步骤的执行情况
- **性能统计**：记录处理时间和数据量
- **错误日志**：详细的错误信息和堆栈跟踪

## 🎯 **使用优势**

### ✅ **数据管理优势**
1. **分类存储**：不同状态的订单存储在不同表中，便于管理
2. **查询效率**：针对特定状态的查询更快速
3. **数据分析**：便于生成各种状态的统计报告

### ✅ **系统性能优势**
1. **并行处理**：可以并行处理不同表的数据
2. **索引优化**：每个表可以有针对性的索引策略
3. **存储优化**：减少单表数据量，提高查询性能

### ✅ **维护优势**
1. **备份策略**：可以针对不同表制定不同的备份策略
2. **数据清理**：可以独立清理特定状态的历史数据
3. **扩展性**：新增状态时只需添加映射规则

## 🚀 **使用方法**

### **命令行使用**
```bash
python scripts/data_import_optimized.py \
    --file "data/IOT_sales_20241218.xlsx" \
    --platform IOT \
    --db_path "database/sales_reports.db"
```

### **程序调用**
```python
from scripts.data_import_optimized import DataImportProcessor

# 创建处理器
processor = DataImportProcessor(db_path="database/sales_reports.db")

# 处理文件
result = processor.process_file("data/IOT_sales_20241218.xlsx", "IOT")

# 查看结果
print(f"总记录数: {result['total_records']}")
print(f"新数据: {result['new_records']}")
print(f"插入结果: {result['insert_results']}")
```

## 📋 **总结**

**智能数据库导入系统的核心特点：**

1. **🎯 智能路由**：根据订单状态自动选择目标表
2. **🔄 分批处理**：大数据集分批处理，确保性能和稳定性
3. **🛡️ 事务安全**：完整的事务管理，确保数据一致性
4. **📊 详细统计**：提供完整的导入统计和分析
5. **🔍 重复检查**：智能检测和处理重复数据
6. **📝 完整日志**：详细的操作日志和错误记录

**这套导入系统为数据管理提供了企业级的可靠性和灵活性！** ✨
