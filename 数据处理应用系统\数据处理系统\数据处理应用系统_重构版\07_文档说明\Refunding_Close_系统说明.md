# 🗄️ Refunding和Close表系统

## 📋 系统概述

Refunding和Close表系统是基于现有IOT_Sales和ZERO_Sales表创建的扩展系统，用于更精细地管理退款和关闭状态的订单数据。

## 🎯 核心功能

### 1. 智能表创建 🏗️

#### 基于现有表结构
- **自动检测**：检测现有IOT_Sales和ZERO_Sales表结构
- **完全复制**：新表与原表结构100%一致
- **数据安全**：原表数据完全不受影响

#### 创建的新表
```sql
-- IOT平台扩展表
IOT_Sales_Refunding  -- IOT平台退款中订单
IOT_Sales_Close      -- IOT平台关闭订单

-- ZERO平台扩展表  
ZERO_Sales_Refunding -- ZERO平台退款中订单
ZERO_Sales_Close     -- ZERO平台关闭订单
```

### 2. 智能数据迁移 📊

#### 迁移规则
| 原表 | 订单状态 | 目标表 |
|------|----------|--------|
| IOT_Sales | Refunded, Refunding | IOT_Sales_Refunding |
| IOT_Sales | Close, Closed | IOT_Sales_Close |
| ZERO_Sales | Refunded, Refunding | ZERO_Sales_Refunding |
| ZERO_Sales | Close, Closed | ZERO_Sales_Close |

#### 迁移特性
- **智能识别**：自动识别订单状态（不区分大小写）
- **完整复制**：复制所有字段信息
- **统计报告**：详细的迁移统计信息

### 3. 高级统计视图 📈

#### 主统计视图：Refunding_Close_Statistics
```sql
-- 类似您提供的复杂SQL结构
WITH 
  -- 1) 预聚合Refunding和Close数据
  Refunding_Close_Aggs AS (
    SELECT 
      Equipment_ID AS Chair_Serial_No,
      DATE(Order_time) AS Sale_Date,
      -- IOT平台统计
      SUM(CASE WHEN Source = 'IOT_Refunding' THEN Price ELSE 0 END) AS IOT_Refunding_Price,
      SUM(CASE WHEN Source = 'IOT_Close' THEN Price ELSE 0 END) AS IOT_Close_Price,
      SUM(CASE WHEN Source = 'IOT_Refunding' THEN 1 ELSE 0 END) AS IOT_Refunding_Count,
      SUM(CASE WHEN Source = 'IOT_Close' THEN 1 ELSE 0 END) AS IOT_Close_Count,
      -- ZERO平台统计
      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN Price ELSE 0 END) AS ZERO_Refunding_Price,
      SUM(CASE WHEN Source = 'ZERO_Close' THEN Price ELSE 0 END) AS ZERO_Close_Price,
      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN 1 ELSE 0 END) AS ZERO_Refunding_Count,
      SUM(CASE WHEN Source = 'ZERO_Close' THEN 1 ELSE 0 END) AS ZERO_Close_Count,
      -- 总计统计
      SUM(CASE WHEN Source IN ('IOT_Refunding', 'ZERO_Refunding') THEN Price ELSE 0 END) AS Total_Refunding_Price,
      SUM(CASE WHEN Source IN ('IOT_Refunding', 'ZERO_Refunding') THEN 1 ELSE 0 END) AS Total_Refunding_Count,
      SUM(CASE WHEN Source IN ('IOT_Close', 'ZERO_Close') THEN Price ELSE 0 END) AS Total_Close_Price,
      SUM(CASE WHEN Source IN ('IOT_Close', 'ZERO_Close') THEN 1 ELSE 0 END) AS Total_Close_Count
    FROM (
      SELECT Equipment_ID, Order_time, Order_price AS Price, 'IOT_Refunding' AS Source FROM IOT_Sales_Refunding
      UNION ALL
      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Close' FROM IOT_Sales_Close
      UNION ALL
      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Refunding' FROM ZERO_Sales_Refunding
      UNION ALL
      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Close' FROM ZERO_Sales_Close
    ) AS AllRefundingClose
    GROUP BY Equipment_ID, DATE(Order_time)
  ),
  
  -- 2) 与Current_Equipment表关联（如果存在）
  Main_Join AS (
    SELECT
      e.Chair_Serial_No,
      e.STATE, e.Location, e.Quantity, e.Layer,
      e.Effective_From, e.Effective_To, e.Rental, e.DATE,
      d.Sale_Date,
      -- 所有统计字段
      COALESCE(a.IOT_Refunding_Price, 0) AS IOT_Refunding_Price,
      COALESCE(a.IOT_Close_Price, 0) AS IOT_Close_Price,
      COALESCE(a.ZERO_Refunding_Price, 0) AS ZERO_Refunding_Price,
      COALESCE(a.ZERO_Close_Price, 0) AS ZERO_Close_Price,
      COALESCE(a.Total_Refunding_Price, 0) AS Total_Refunding_Price,
      COALESCE(a.Total_Close_Price, 0) AS Total_Close_Price
    FROM Current_Equipment e
    CROSS JOIN All_Dates d
    LEFT JOIN Refunding_Close_Aggs a ON e.Chair_Serial_No = a.Chair_Serial_No AND d.Sale_Date = a.Sale_Date
    WHERE d.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')
  )
```

#### 汇总统计视图：Refunding_Close_Summary
```sql
-- 各表统计汇总
SELECT
  Table_Name,           -- 表名
  Platform,             -- 平台 (IOT/ZERO/ALL)
  Status_Type,          -- 状态类型 (Refunding/Close)
  Record_Count,         -- 记录数
  Total_Sales,          -- 总销售额
  Average_Sales,        -- 平均销售额
  Min_Sales,            -- 最小销售额
  Max_Sales,            -- 最大销售额
  Unique_Equipment_Count, -- 唯一设备数
  Unique_Date_Count     -- 唯一日期数
FROM (
  -- IOT_Sales_Refunding统计
  -- IOT_Sales_Close统计
  -- ZERO_Sales_Refunding统计
  -- ZERO_Sales_Close统计
  -- Total_Refunding统计 (IOT + ZERO)
  -- Total_Close统计 (IOT + ZERO)
)
```

## 🔧 使用方法

### 命令行使用
```bash
# 创建所有表和视图
python scripts/create_refunding_close_tables.py

# 指定数据库路径
python scripts/create_refunding_close_tables.py --db_path "custom_path.db"

# 只迁移数据（表已存在）
python scripts/create_refunding_close_tables.py --migrate_data
```

### 执行结果示例
```
✅ Refunding和Close表系统创建成功
📊 数据迁移统计:
  总计迁移: 45 条记录
  • IOT_Sales_Refunding: 12 条记录
  • IOT_Sales_Close: 8 条记录
  • ZERO_Sales_Refunding: 15 条记录
  • ZERO_Sales_Close: 10 条记录

🎯 创建的表:
  • IOT_Sales_Refunding
  • IOT_Sales_Close
  • ZERO_Sales_Refunding
  • ZERO_Sales_Close

📋 创建的视图:
  • Refunding_Close_Statistics - 详细统计视图（与Current_Equipment关联）
  • Refunding_Close_Summary - 汇总统计视图（各表统计信息）
```

## 📊 数据查询示例

### 查询详细统计
```sql
-- 查看所有设备的Refunding和Close统计
SELECT 
  Chair_Serial_No,
  Location,
  Sale_Date,
  IOT_Refunding_Price,
  IOT_Close_Price,
  ZERO_Refunding_Price,
  ZERO_Close_Price,
  Total_Refunding_Price,
  Total_Close_Price
FROM Refunding_Close_Statistics
WHERE Sale_Date >= '2024-06-01'
ORDER BY Chair_Serial_No, Sale_Date;
```

### 查询汇总统计
```sql
-- 查看各表的汇总统计
SELECT 
  Table_Name,
  Platform,
  Status_Type,
  Record_Count,
  Total_Sales,
  Average_Sales
FROM Refunding_Close_Summary
ORDER BY Platform, Status_Type;
```

### 查询平台对比
```sql
-- 对比IOT和ZERO平台的Refunding情况
SELECT 
  Platform,
  SUM(CASE WHEN Status_Type = 'Refunding' THEN Total_Sales ELSE 0 END) AS Refunding_Sales,
  SUM(CASE WHEN Status_Type = 'Close' THEN Total_Sales ELSE 0 END) AS Close_Sales,
  SUM(CASE WHEN Status_Type = 'Refunding' THEN Record_Count ELSE 0 END) AS Refunding_Count,
  SUM(CASE WHEN Status_Type = 'Close' THEN Record_Count ELSE 0 END) AS Close_Count
FROM Refunding_Close_Summary
WHERE Platform IN ('IOT', 'ZERO')
GROUP BY Platform;
```

## 🛡️ 安全保证

### 数据安全
- **原表保护**：IOT_Sales和ZERO_Sales表数据完全不受影响
- **自动备份**：操作前自动创建数据库备份
- **事务保护**：所有操作在事务中执行，失败自动回滚
- **错误恢复**：操作失败时可一键恢复到操作前状态

### 操作安全
- **智能检测**：自动检测源表是否存在
- **结构验证**：确保新表结构与源表一致
- **数据验证**：迁移后验证数据完整性
- **日志记录**：完整的操作日志和统计信息

## 🔍 故障排除

### 常见问题

**Q: 源表不存在怎么办？**
A: 脚本会自动检测并报错，请确保IOT_Sales和ZERO_Sales表已存在。

**Q: 表已存在会重复创建吗？**
A: 不会，使用`CREATE TABLE IF NOT EXISTS`，已存在的表不会被影响。

**Q: 数据会重复迁移吗？**
A: 每次运行都会重新插入数据，如果需要避免重复，请先清空目标表。

**Q: 视图查询为空怎么办？**
A: 检查源表是否有对应状态的数据，以及Current_Equipment表是否存在。

### 日志查看
- 详细日志存储在 `logs/` 目录下
- 备份文件存储在 `database/backups/` 目录下
- 可通过日志追踪完整的操作过程

## 🚀 扩展功能

### 计划功能
1. **增量迁移**：只迁移新增的Refunding和Close数据
2. **定时同步**：定期自动同步数据
3. **更多状态**：支持更多订单状态的分类
4. **数据清理**：自动清理过期的Refunding和Close数据

### 性能优化
1. **索引优化**：为常用查询字段添加索引
2. **分区表**：按日期分区提升查询性能
3. **物化视图**：预计算统计结果提升查询速度

---

**Refunding和Close表系统 - 让订单状态管理更精细、更高效！** 🎉

## 📁 相关文件

- `scripts/create_refunding_close_tables.py` - 主创建脚本
- `database/models.py` - 表结构定义
- `database/smart_backup_manager.py` - 智能备份管理
- `Refunding_Close_系统说明.md` - 本说明文档
