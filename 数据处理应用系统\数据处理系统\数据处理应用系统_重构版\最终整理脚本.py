# -*- coding: utf-8 -*-
"""
最终整理脚本
清理重复文件，完善目录结构
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def final_cleanup():
    """最终清理"""
    base_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    
    print("🧹 开始最终清理...")
    
    # 1. 处理重复的config.ini
    main_config = base_dir / "01_主程序" / "config.ini"
    if main_config.exists():
        try:
            main_config.unlink()
            print("🗑️ 删除重复的config.ini")
        except Exception as e:
            print(f"⚠️ 删除config.ini失败: {e}")
    
    # 2. 移动重复的database和logs目录
    main_database = base_dir / "01_主程序" / "database"
    main_logs = base_dir / "01_主程序" / "logs"
    
    if main_database.exists():
        try:
            shutil.rmtree(main_database)
            print("🗑️ 删除重复的database目录")
        except Exception as e:
            print(f"⚠️ 删除database目录失败: {e}")
    
    if main_logs.exists():
        try:
            shutil.rmtree(main_logs)
            print("🗑️ 删除重复的logs目录")
        except Exception as e:
            print(f"⚠️ 删除logs目录失败: {e}")
    
    # 3. 清理__pycache__目录
    for pycache_dir in base_dir.rglob("__pycache__"):
        try:
            shutil.rmtree(pycache_dir)
            print(f"🗑️ 删除缓存目录: {pycache_dir.relative_to(base_dir)}")
        except Exception as e:
            print(f"⚠️ 删除缓存目录失败: {e}")
    
    # 4. 移动整理脚本到工具目录
    tools_dir = base_dir / "10_整理工具"
    tools_dir.mkdir(exist_ok=True)
    
    scripts_to_move = [
        "文件整理脚本.py",
        "处理未分类文件.py",
        "最终整理脚本.py"
    ]
    
    for script in scripts_to_move:
        source = base_dir / script
        target = tools_dir / script
        if source.exists() and source != Path(__file__):
            try:
                shutil.move(str(source), str(target))
                print(f"📁 {script} → 10_整理工具")
            except Exception as e:
                print(f"⚠️ 移动脚本失败 {script}: {e}")
    
    # 5. 创建工具目录说明
    tools_readme = tools_dir / "说明.txt"
    with open(tools_readme, 'w', encoding='utf-8') as f:
        f.write("10_整理工具\n")
        f.write("=" * 50 + "\n\n")
        f.write("描述: 文件整理和管理工具\n")
        f.write("包含文件:\n")
        f.write("- 文件整理脚本.py: 主要的文件分类整理脚本\n")
        f.write("- 处理未分类文件.py: 处理剩余未分类文件\n")
        f.write("- 最终整理脚本.py: 最终清理和优化脚本\n")
        f.write(f"\n创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print("✅ 创建整理工具目录")

def create_main_readme():
    """创建主目录说明文件"""
    base_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    readme_file = base_dir / "项目说明.md"
    
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write("# 数据处理应用系统 - 重构版\n\n")
        f.write("## 📁 目录结构\n\n")
        
        categories = {
            "01_主程序": "主要应用程序文件",
            "02_PostgreSQL管理": "PostgreSQL安装、管理和迁移工具",
            "03_配置文件": "系统配置文件",
            "04_测试文件": "各种测试和验证脚本",
            "05_迁移工具": "数据迁移相关工具",
            "06_数据库工具": "数据库创建和修复工具",
            "07_文档说明": "文档和说明文件",
            "08_备份文件": "数据库备份文件",
            "09_临时文件": "临时和分析文件",
            "10_整理工具": "文件整理和管理工具"
        }
        
        for category, description in categories.items():
            category_dir = base_dir / category
            if category_dir.exists():
                files = [f for f in category_dir.iterdir() if f.is_file() and f.name != "说明.txt"]
                f.write(f"### {category}\n")
                f.write(f"**描述**: {description}  \n")
                f.write(f"**文件数量**: {len(files)} 个  \n\n")
        
        f.write("## 🚀 快速开始\n\n")
        f.write("### 主程序运行\n")
        f.write("```bash\n")
        f.write("cd 01_主程序\n")
        f.write("python 数据处理与导入应用_完整版.py\n")
        f.write("```\n\n")
        
        f.write("### PostgreSQL管理\n")
        f.write("```bash\n")
        f.write("cd 02_PostgreSQL管理\n")
        f.write("# 安装PostgreSQL\n")
        f.write("python postgresql_setup.py\n")
        f.write("# 启动PostgreSQL\n")
        f.write("python postgresql_manager.py start\n")
        f.write("# 迁移界面\n")
        f.write("python migration_ui.py\n")
        f.write("```\n\n")
        
        f.write("### 测试验证\n")
        f.write("```bash\n")
        f.write("cd 04_测试文件\n")
        f.write("python comprehensive_verification.py\n")
        f.write("```\n\n")
        
        f.write("## 📊 功能特点\n\n")
        f.write("- ✅ **双数据库支持**: SQLite + PostgreSQL\n")
        f.write("- ✅ **智能数据导入**: 自动识别平台和状态\n")
        f.write("- ✅ **智能迁移**: Equipment_ID精确匹配\n")
        f.write("- ✅ **自动备份**: 操作前自动备份\n")
        f.write("- ✅ **错误恢复**: 失败时自动回滚\n")
        f.write("- ✅ **完整日志**: 详细的操作记录\n\n")
        
        f.write("## 📋 数据状态\n\n")
        f.write("- **PostgreSQL**: 599,615行数据，21个表\n")
        f.write("- **SQLite**: 完全同步，作为本地备份\n")
        f.write("- **IOT设备**: 3,226个\n")
        f.write("- **ZERO设备**: 565个\n")
        f.write("- **数据范围**: 2025-03-01 到 2025-06-12\n\n")
        
        f.write(f"---\n")
        f.write(f"*最后整理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
    
    print(f"📋 创建项目说明文件: {readme_file}")

def generate_final_report():
    """生成最终整理报告"""
    base_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    report_file = base_dir / "最终整理报告.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("最终整理报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"整理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 统计各目录文件数量
        f.write("目录统计:\n")
        f.write("-" * 30 + "\n")
        
        total_files = 0
        categories = [
            "01_主程序", "02_PostgreSQL管理", "03_配置文件", "04_测试文件",
            "05_迁移工具", "06_数据库工具", "07_文档说明", "08_备份文件",
            "09_临时文件", "10_整理工具"
        ]
        
        for category in categories:
            category_dir = base_dir / category
            if category_dir.exists():
                files = [f for f in category_dir.iterdir() if f.is_file() and f.name != "说明.txt"]
                file_count = len(files)
                total_files += file_count
                f.write(f"{category}: {file_count} 个文件\n")
        
        f.write(f"\n总计: {total_files} 个文件\n\n")
        
        # 核心目录说明
        f.write("核心目录说明:\n")
        f.write("-" * 30 + "\n")
        f.write("01_主程序: 运行主应用程序\n")
        f.write("02_PostgreSQL管理: PostgreSQL相关工具\n")
        f.write("03_配置文件: 系统配置\n")
        f.write("04_测试文件: 测试和验证\n")
        f.write("其他目录: 支持工具和文档\n\n")
        
        f.write("使用建议:\n")
        f.write("-" * 30 + "\n")
        f.write("1. 从01_主程序开始使用\n")
        f.write("2. 需要PostgreSQL时使用02_PostgreSQL管理\n")
        f.write("3. 测试功能时使用04_测试文件\n")
        f.write("4. 查看文档时使用07_文档说明\n")
        f.write("5. 备份文件在08_备份文件中\n")
    
    print(f"📋 生成最终整理报告: {report_file}")

def main():
    """主函数"""
    print("🚀 开始最终整理...")
    print("=" * 50)
    
    final_cleanup()
    create_main_readme()
    generate_final_report()
    
    print("\n" + "=" * 50)
    print("🎉 最终整理完成！")
    print("\n📁 项目结构已完全整理：")
    print("  01_主程序 - 运行主应用")
    print("  02_PostgreSQL管理 - PostgreSQL工具")
    print("  03_配置文件 - 系统配置")
    print("  04_测试文件 - 测试验证")
    print("  05-10_其他目录 - 支持工具")
    print("\n📋 查看 '项目说明.md' 了解详细信息")

if __name__ == "__main__":
    main()
