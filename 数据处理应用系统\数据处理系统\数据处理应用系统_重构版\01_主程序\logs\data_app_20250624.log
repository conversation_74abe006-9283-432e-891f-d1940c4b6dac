2025-06-24 08:43:40,307 - INFO - 双数据库模块加载成功
2025-06-24 08:43:40,818 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:43:40,818 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:43:40,818 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:43:41,158 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:43:41,165 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:43:41,165 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:43:41,297 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:43:41,297 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:43:41,324 - INFO - [general] 数据库设置已加载
2025-06-24 08:43:41,324 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:43:41,324 - INFO - [settings] 数据库设置已加载
2025-06-24 08:43:41,324 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:43:42,452 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:43:42,458 - INFO - 数据处理与导入应用已启动
2025-06-24 08:43:42,581 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:43:46,364 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:43:48,332 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:52,054 - INFO - [processing] 开始处理文件...
2025-06-24 08:43:52,055 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:43:52,056 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:43:52,057 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:43:52,923 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬äºæä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ä½¿ç¨sheet: 170625
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶è¯»åçååï¼ ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶åæ°ï¼13
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: â° æ£æµå°ç¬ç«çTimeå
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º 13ï¼é¢æä¸º27åãå°ç»§ç»­å¤çï¼ä½è¯·æ£æ¥æä»¶æ ¼å¼æ¯å¦æ­£ç¡®ã
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937110200', '2937652905', '2936662760', '2937558305', '2937761338']
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937110200', '2937652905', '2936662760', '2937558305', '2938190334']
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937110200', '2937652905', '2936662760', '2937558305', '2937761338']
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:43:52,963 - INFO - [processing] 错误: 处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
2025-06-24 08:43:52,964 - INFO - [processing] 错误: 处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 668.95条/s][0m
2025-06-24 08:43:52,984 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,998 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,998 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,998 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,999 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,999 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:53,000 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:53,000 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:53,000 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:53,001 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:53,001 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:53,001 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.08ç§
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:43:53,008 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:43:53,008 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:43:53,101 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:53,104 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:43:53,104 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:43:53,181 - INFO - [processing] 文件处理成功完成
2025-06-24 08:47:16,474 - INFO - 双数据库模块加载成功
2025-06-24 08:47:16,749 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:47:16,750 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:47:16,750 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:47:17,155 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:47:17,163 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:47:17,163 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:47:17,293 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:47:17,294 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:47:17,320 - INFO - [general] 数据库设置已加载
2025-06-24 08:47:17,320 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:47:17,321 - INFO - [settings] 数据库设置已加载
2025-06-24 08:47:17,321 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:47:17,641 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:47:17,646 - INFO - 数据处理与导入应用已启动
2025-06-24 08:47:17,753 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:47:21,722 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:47:23,503 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:28,540 - INFO - [processing] 开始处理文件...
2025-06-24 08:47:28,541 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:47:28,542 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:47:28,543 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬äºæä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ä½¿ç¨sheet: 170625
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶è¯»åçååï¼ ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶åæ°ï¼13
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: â° æ£æµå°ç¬ç«çTimeå
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º 13ï¼é¢æä¸º27åãå°ç»§ç»­å¤çï¼ä½è¯·æ£æ¥æä»¶æ ¼å¼æ¯å¦æ­£ç¡®ã
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937110200', '2937665773', '2936768998', '2937697214', '2937960660']
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937110200', '2937697214', '2937960660', '2937665773', '2936768998']
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937110200', '2937665773', '2936768998', '2937960660', '2937697214']
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:47:29,562 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 746.12条/s][0m
2025-06-24 08:47:29,582 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:47:29,582 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,583 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,583 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:47:29,583 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,584 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,584 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,584 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,586 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,586 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,586 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,587 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,587 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,592 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,592 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,592 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.06ç§
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:47:29,734 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:47:29,871 - INFO - [processing] 文件处理成功完成
2025-06-24 08:50:40,120 - INFO - 双数据库模块加载成功
2025-06-24 08:50:40,297 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:50:40,297 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:50:40,297 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:50:40,651 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:50:40,657 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:50:40,657 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:50:40,883 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:50:40,883 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:50:40,906 - INFO - [general] 数据库设置已加载
2025-06-24 08:50:40,906 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:50:40,906 - INFO - [settings] 数据库设置已加载
2025-06-24 08:50:40,907 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:50:41,165 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:50:41,170 - INFO - 数据处理与导入应用已启动
2025-06-24 08:50:41,270 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:50:44,815 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:50:46,927 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:51,496 - INFO - [processing] 开始处理文件...
2025-06-24 08:50:51,497 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:50:51,497 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:50:51,497 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬äºæä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ä½¿ç¨sheet: 170625
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶è¯»åçååï¼ ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶åæ°ï¼13
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: â° æ£æµå°ç¬ç«çTimeå
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º 13ï¼é¢æä¸º27åãå°ç»§ç»­å¤çï¼ä½è¯·æ£æ¥æä»¶æ ¼å¼æ¯å¦æ­£ç¡®ã
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2936647600', '2936769108', '2937106579', '2937802660', '2938190334']
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2936769108', '2937106579', '2937802660', '2938190334', '2937770418']
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2936769108', '2937106579', '2937802660', '2938190334', '2937770418']
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:50:52,342 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 810.41条/s][0m
2025-06-24 08:50:52,368 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:50:52,368 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,368 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.04ç§
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:50:52,571 - INFO - [processing] 文件处理成功完成
2025-06-24 08:54:42,965 - INFO - 双数据库模块加载成功
2025-06-24 08:54:43,171 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:54:43,171 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:54:43,171 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:54:43,551 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:54:43,560 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:54:43,561 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:54:43,687 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:54:43,687 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:54:43,713 - INFO - [general] 数据库设置已加载
2025-06-24 08:54:43,714 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:54:43,714 - INFO - [settings] 数据库设置已加载
2025-06-24 08:54:43,714 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:54:44,125 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:54:44,130 - INFO - 数据处理与导入应用已启动
2025-06-24 08:54:44,228 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:54:48,050 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:54:50,300 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:54,756 - INFO - [processing] 开始处理文件...
2025-06-24 08:54:54,757 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:54:54,757 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:54:54,758 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:54:55,592 - INFO - [processing] 📋 输出: File1 path: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:54:55,592 - INFO - [processing] 📋 输出: File2 path: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:55,592 - INFO - [processing] 📋 输出: Using sheet: 170625
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: File1 columns: ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: File1 column count: 13
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: Time column detected
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: Warning: File1 has 13 columns, expected 27. Will continue processing.
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: Found 'Transaction ID' column, will use for matching
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2936647600', '2937802660', '2937652905', '2937230887', '2936768998']
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937802660', '2937652905', '2937230887', '2936768998', '2937888689']
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937802660', '2937652905', '2937230887', '2936768998', '2937888689']
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:54:55,628 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 739.41条/s][0m
2025-06-24 08:54:55,649 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.05ç§
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:54:55,752 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:54:55,755 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:54:55,841 - INFO - [processing] 文件处理成功完成
2025-06-24 08:59:44,824 - INFO - [processing] 开始处理文件...
2025-06-24 08:59:44,825 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:59:44,826 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:59:44,827 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:59:45,805 - INFO - [processing] 📋 输出: File1 path: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:59:45,805 - INFO - [processing] 📋 输出: File2 path: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:59:45,805 - INFO - [processing] 📋 输出: Using sheet: 170625
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: File1 columns: ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: File1 column count: 13
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Time column detected
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Warning: File1 has 13 columns, expected 27. Will continue processing.
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Found 'Transaction ID' column, will use for matching
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Processing datetime data...
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Date consistency check passed: 2025-06-17
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: File1 9-digit ID count: 27
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: Checking Transaction ID consistency...
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: File2 standardized columns: ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937162972', '2937665684', '2937761946', '2936768998', '2937461430']
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937665684', '2937761946', '2937162972', '2936768998', '2936727961']
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937162972', '2937665684', '2937761946', '2936768998', '2937461430']
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:59:45,845 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 679.79条/s][0m
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,878 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,879 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,879 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.06ç§
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:59:45,974 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:59:45,974 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:59:45,977 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:59:45,977 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:59:46,078 - INFO - [processing] 文件处理成功完成
