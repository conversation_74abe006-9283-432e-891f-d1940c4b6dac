2025-06-24 08:43:40,307 - INFO - 双数据库模块加载成功
2025-06-24 08:43:40,818 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:43:40,818 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:43:40,818 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:43:41,158 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:43:41,165 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:43:41,165 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:43:41,297 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:43:41,297 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:43:41,324 - INFO - [general] 数据库设置已加载
2025-06-24 08:43:41,324 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:43:41,324 - INFO - [settings] 数据库设置已加载
2025-06-24 08:43:41,324 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:43:42,452 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:43:42,458 - INFO - 数据处理与导入应用已启动
2025-06-24 08:43:42,581 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:43:46,364 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:43:48,332 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:52,054 - INFO - [processing] 开始处理文件...
2025-06-24 08:43:52,055 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:43:52,056 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:43:52,057 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:43:52,923 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬äºæä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ä½¿ç¨sheet: 170625
2025-06-24 08:43:52,924 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶è¯»åçååï¼ ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶åæ°ï¼13
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: â° æ£æµå°ç¬ç«çTimeå
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º 13ï¼é¢æä¸º27åãå°ç»§ç»­å¤çï¼ä½è¯·æ£æ¥æä»¶æ ¼å¼æ¯å¦æ­£ç¡®ã
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:43:52,925 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:43:52,926 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937110200', '2937652905', '2936662760', '2937558305', '2937761338']
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937110200', '2937652905', '2936662760', '2937558305', '2938190334']
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937110200', '2937652905', '2936662760', '2937558305', '2937761338']
2025-06-24 08:43:52,927 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:43:52,928 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:43:52,963 - INFO - [processing] 错误: 处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
2025-06-24 08:43:52,964 - INFO - [processing] 错误: 处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 668.95条/s][0m
2025-06-24 08:43:52,984 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,985 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,986 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,987 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,988 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,989 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,990 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,991 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,992 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,993 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,994 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,995 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,996 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,997 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,998 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,998 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:52,998 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:52,999 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:52,999 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:53,000 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:53,000 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:53,000 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:53,001 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:53,001 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:43:53,001 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.08ç§
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:43:53,002 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:43:53,003 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:43:53,004 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:43:53,005 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:43:53,006 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:43:53,007 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:43:53,008 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:43:53,008 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:43:53,101 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:43:53,102 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:43:53,103 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:43:53,104 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:43:53,104 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:43:53,181 - INFO - [processing] 文件处理成功完成
2025-06-24 08:47:16,474 - INFO - 双数据库模块加载成功
2025-06-24 08:47:16,749 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:47:16,750 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:47:16,750 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:47:17,155 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:47:17,163 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:47:17,163 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:47:17,293 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:47:17,294 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:47:17,320 - INFO - [general] 数据库设置已加载
2025-06-24 08:47:17,320 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:47:17,321 - INFO - [settings] 数据库设置已加载
2025-06-24 08:47:17,321 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:47:17,641 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:47:17,646 - INFO - 数据处理与导入应用已启动
2025-06-24 08:47:17,753 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:47:21,722 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:47:23,503 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:28,540 - INFO - [processing] 开始处理文件...
2025-06-24 08:47:28,541 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:47:28,542 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:47:28,543 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬äºæä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ä½¿ç¨sheet: 170625
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶è¯»åçååï¼ ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:47:29,527 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶åæ°ï¼13
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: â° æ£æµå°ç¬ç«çTimeå
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º 13ï¼é¢æä¸º27åãå°ç»§ç»­å¤çï¼ä½è¯·æ£æ¥æä»¶æ ¼å¼æ¯å¦æ­£ç¡®ã
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:47:29,528 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:47:29,529 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937110200', '2937665773', '2936768998', '2937697214', '2937960660']
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937110200', '2937697214', '2937960660', '2937665773', '2936768998']
2025-06-24 08:47:29,530 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937110200', '2937665773', '2936768998', '2937960660', '2937697214']
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:47:29,531 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:47:29,562 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 746.12条/s][0m
2025-06-24 08:47:29,582 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:47:29,582 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,583 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,583 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:47:29,583 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,584 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,584 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,584 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,585 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,586 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,586 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,586 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,587 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,587 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,588 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,589 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,590 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,591 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,592 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,592 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,592 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,593 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,594 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,595 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,596 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,597 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,598 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,599 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,600 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,601 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:47:29,602 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.06ç§
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:47:29,603 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:47:29,604 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:47:29,605 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:47:29,606 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:47:29,607 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:47:29,608 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:29,731 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:47:29,732 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:47:29,733 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:47:29,734 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:47:29,871 - INFO - [processing] 文件处理成功完成
2025-06-24 08:50:40,120 - INFO - 双数据库模块加载成功
2025-06-24 08:50:40,297 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:50:40,297 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:50:40,297 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:50:40,651 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:50:40,657 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:50:40,657 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:50:40,883 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:50:40,883 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:50:40,906 - INFO - [general] 数据库设置已加载
2025-06-24 08:50:40,906 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:50:40,906 - INFO - [settings] 数据库设置已加载
2025-06-24 08:50:40,907 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:50:41,165 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:50:41,170 - INFO - 数据处理与导入应用已启动
2025-06-24 08:50:41,270 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:50:44,815 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:50:46,927 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:51,496 - INFO - [processing] 开始处理文件...
2025-06-24 08:50:51,497 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:50:51,497 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:50:51,497 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ä½¿ç¨ç¬¬äºæä»¶è·¯å¾: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ä½¿ç¨sheet: 170625
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶è¯»åçååï¼ ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶åæ°ï¼13
2025-06-24 08:50:52,309 - INFO - [processing] 📋 输出: â° æ£æµå°ç¬ç«çTimeå
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º 13ï¼é¢æä¸º27åãå°ç»§ç»­å¤çï¼ä½è¯·æ£æ¥æä»¶æ ¼å¼æ¯å¦æ­£ç¡®ã
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:50:52,310 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2936647600', '2936769108', '2937106579', '2937802660', '2938190334']
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2936769108', '2937106579', '2937802660', '2938190334', '2937770418']
2025-06-24 08:50:52,311 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2936769108', '2937106579', '2937802660', '2938190334', '2937770418']
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:50:52,312 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:50:52,342 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 810.41条/s][0m
2025-06-24 08:50:52,368 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:50:52,368 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,368 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,369 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,370 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,371 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:50:52,372 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,373 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,374 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,375 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,376 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,377 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,378 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,379 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:50:52,380 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.04ç§
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:50:52,381 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:50:52,382 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:50:52,383 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:50:52,384 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:50:52,385 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:50:52,386 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:50:52,471 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:50:52,472 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:50:52,571 - INFO - [processing] 文件处理成功完成
2025-06-24 08:54:42,965 - INFO - 双数据库模块加载成功
2025-06-24 08:54:43,171 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 08:54:43,171 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:54:43,171 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 08:54:43,551 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 08:54:43,560 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 08:54:43,561 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 08:54:43,687 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:54:43,687 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 08:54:43,713 - INFO - [general] 数据库设置已加载
2025-06-24 08:54:43,714 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:54:43,714 - INFO - [settings] 数据库设置已加载
2025-06-24 08:54:43,714 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 08:54:44,125 - INFO - [database_config] 数据库状态已刷新
2025-06-24 08:54:44,130 - INFO - 数据处理与导入应用已启动
2025-06-24 08:54:44,228 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 08:54:48,050 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:54:50,300 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:54,756 - INFO - [processing] 开始处理文件...
2025-06-24 08:54:54,757 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:54:54,757 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:54:54,758 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:54:55,592 - INFO - [processing] 📋 输出: File1 path: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:54:55,592 - INFO - [processing] 📋 输出: File2 path: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:55,592 - INFO - [processing] 📋 输出: Using sheet: 170625
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: File1 columns: ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: File1 column count: 13
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: Time column detected
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: Warning: File1 has 13 columns, expected 27. Will continue processing.
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: Found 'Transaction ID' column, will use for matching
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: â° å¤çæ¥ææ¶é´æ°æ®...
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: ð æ¥æä¸è´æ§æ£æ¥éè¿: 2025-06-17
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶9ä½IDæ°éï¼27
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: æ£æ¥Transaction IDä¸è´æ§...
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:54:55,593 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ åååååï¼ ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:54:55,594 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2936647600', '2937802660', '2937652905', '2937230887', '2936768998']
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937802660', '2937652905', '2937230887', '2936768998', '2937888689']
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937802660', '2937652905', '2937230887', '2936768998', '2937888689']
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:54:55,595 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:54:55,596 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:54:55,628 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 739.41条/s][0m
2025-06-24 08:54:55,649 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:54:55,650 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,651 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,652 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:54:55,653 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,654 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,655 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,656 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,657 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,658 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,659 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,660 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.05ç§
2025-06-24 08:54:55,661 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:54:55,662 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:54:55,663 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:54:55,664 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:54:55,665 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:54:55,666 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:54:55,752 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:54:55,753 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:54:55,754 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:54:55,755 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:54:55,841 - INFO - [processing] 文件处理成功完成
2025-06-24 08:59:44,824 - INFO - [processing] 开始处理文件...
2025-06-24 08:59:44,825 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 08:59:44,826 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 08:59:44,827 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 08:59:45,805 - INFO - [processing] 📋 输出: File1 path: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:59:45,805 - INFO - [processing] 📋 输出: File2 path: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:59:45,805 - INFO - [processing] 📋 输出: Using sheet: 170625
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: File1 columns: ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', 'Actual Amt', 'App Code', 'Status', 'Billing Name', 'Currency', 'Merchant / Sub Merchant ID', 'Agent Name', 'Reference']
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: File1 column count: 13
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Time column detected
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Warning: File1 has 13 columns, expected 27. Will continue processing.
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Found 'Transaction ID' column, will use for matching
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Processing datetime data...
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: Date consistency check passed: 2025-06-17
2025-06-24 08:59:45,806 - INFO - [processing] 📋 输出: File1 9-digit ID count: 27
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: Checking Transaction ID consistency...
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: File2 standardized columns: ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: â æ£æµå° Transaction Num å
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 08:59:45,807 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937162972', '2937665684', '2937761946', '2936768998', '2937461430']
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937665684', '2937761946', '2937162972', '2936768998', '2936727961']
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937162972', '2937665684', '2937761946', '2936768998', '2937461430']
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 08:59:45,808 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 08:59:45,809 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 08:59:45,845 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 679.79条/s][0m
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,867 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,868 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,869 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 08:59:45,870 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,871 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,872 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,873 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,874 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,875 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,876 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,877 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,878 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,879 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,879 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,880 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 08:59:45,881 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.06ç§
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 08:59:45,882 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 08:59:45,883 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: Order status
2025-06-24 08:59:45,884 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 08:59:45,885 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 08:59:45,886 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:59:45,887 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: True: 27
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: False: 2
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 08:59:45,888 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 08:59:45,974 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 08:59:45,974 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 08:59:45,975 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 08:59:45,976 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 08:59:45,977 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 08:59:45,977 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 08:59:46,078 - INFO - [processing] 文件处理成功完成
2025-06-24 09:24:17,048 - INFO - 双数据库模块加载成功
2025-06-24 09:24:17,235 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 09:24:17,235 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:24:17,235 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 09:24:17,535 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 09:24:17,542 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:24:17,542 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 09:24:17,664 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:24:17,664 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:24:17,686 - INFO - [general] 数据库设置已加载
2025-06-24 09:24:17,686 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:24:17,687 - INFO - [settings] 数据库设置已加载
2025-06-24 09:24:17,687 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:24:18,054 - INFO - [database_config] 数据库状态已刷新
2025-06-24 09:24:18,059 - INFO - 数据处理与导入应用已启动
2025-06-24 09:24:18,154 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 09:24:22,332 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 09:24:23,784 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:24:27,853 - INFO - [processing] 开始处理文件...
2025-06-24 09:24:27,853 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 09:24:27,854 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 09:24:27,854 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 09:24:28,618 - INFO - [processing] 📋 输出: ð å¼å§æ°æ®å¤ç...
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶å è½½å®æ: 27 æ¡è®°å½
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: ð Transaction IDåæ£æµå®æ
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: Processing datetime data...
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: Date consistency check passed: 2025-06-17
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: File1 9-digit ID count: 27
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: Checking Transaction ID consistency...
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: File2 standardized columns: ['Serial number', 'Copartner name', 'Merchant name', 'Order No.', 'Transaction Num', 'Order types', 'Ticke code', 'Order status', 'Payment type', 'Order price', 'Payment', 'Monetary unit', 'Order time', 'Equipment type', 'Equipment Model', 'Equipment ID', 'Equipment name', 'Branch name', 'Area type', 'Payment date', 'User name']
2025-06-24 09:24:28,619 - INFO - [processing] 📋 输出: ð å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937888689', '2937665684', '2937652905', '2937779767', '2936814575']
2025-06-24 09:24:28,620 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937888689', '2937665684', '2937652905', '2937779767', '2936814575']
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937888689', '2937665684', '2937652905', '2937779767', '2936814575']
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 09:24:28,621 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 09:24:28,654 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 752.75条/s][0m
2025-06-24 09:24:28,683 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 09:24:28,683 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:24:28,683 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:24:28,684 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 09:24:28,684 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:24:28,684 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:24:28,684 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,684 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,685 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,685 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:24:28,685 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:24:28,685 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,685 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,686 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,686 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:24:28,686 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:24:28,686 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,686 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,686 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:24:28,687 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,688 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,689 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,690 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,691 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,692 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,693 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,694 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,694 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,694 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,694 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,694 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,694 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,695 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.04ç§
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 09:24:28,696 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 09:24:28,697 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: Order status
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 09:24:28,698 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 09:24:28,699 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: True: 27
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: False: 2
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: True: 27
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: False: 2
2025-06-24 09:24:28,700 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 09:24:28,701 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 09:24:28,701 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 09:24:28,793 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 09:24:28,794 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 09:24:28,794 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:24:28,794 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 09:24:28,794 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 09:24:28,795 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 09:24:28,795 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 09:24:28,795 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 09:24:28,795 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 09:24:28,795 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 09:24:28,796 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 09:24:28,796 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:24:28,796 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 09:24:28,796 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 09:24:28,888 - INFO - [processing] 文件处理成功完成
2025-06-24 09:31:12,746 - INFO - 双数据库模块加载成功
2025-06-24 09:31:12,956 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 09:31:12,956 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:31:12,956 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 09:31:13,403 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 09:31:13,409 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:31:13,410 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 09:31:13,538 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:31:13,538 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:31:13,563 - INFO - [general] 数据库设置已加载
2025-06-24 09:31:13,563 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:31:13,563 - INFO - [settings] 数据库设置已加载
2025-06-24 09:31:13,564 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:31:13,960 - INFO - [database_config] 数据库状态已刷新
2025-06-24 09:31:13,964 - INFO - 数据处理与导入应用已启动
2025-06-24 09:31:14,067 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 09:31:17,128 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 09:31:19,144 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:31:22,734 - INFO - [processing] 开始处理文件...
2025-06-24 09:31:22,734 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 09:31:22,735 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 09:31:22,736 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 09:31:24,052 - INFO - [processing] 📋 输出: å¼å§æ°æ®å¤ç...
2025-06-24 09:31:24,052 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶å è½½å®æ: 27 æ¡è®°å½
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: Transaction IDåæ£æµå®æ
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: â ææTransaction IDé½æ¯å¯ä¸ç
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°: 28
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°: 28
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é: 28
2025-06-24 09:31:24,053 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç: 100.0%
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å...
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶ææTransaction IDæ°é: 27
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é: 28
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬: ['2937665684', '2937008365', '2937558305', '2937802660', '2937779767']
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ð ç¬¬äºæä»¶Transaction Numæ ·æ¬: ['2937665684', '2937008365', '2937558305', '2937802660', '2937779767']
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ð å¯å¹éçTransaction ID/Numæ°é: 26
2025-06-24 09:31:24,054 - INFO - [processing] 📋 输出: ð å¹éçTransaction IDæ ·æ¬: ['2937665684', '2937008365', '2937558305', '2937802660', '2937779767']
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: ð å¹éç: 96.3%
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: â Transaction Numå·å¤å¹éè½å
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: ð å¯å¨æ¨¡ååæ°æ®å¤çå¨...
2025-06-24 09:31:24,055 - INFO - [processing] 📋 输出: ðï¸ å¼å§ææ¥æåç»å¤ç...
2025-06-24 09:31:24,056 - INFO - [processing] 📋 输出: ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 09:31:24,056 - INFO - [processing] 📋 输出: ð åç° 1 ä¸ªæ¥æåç»
2025-06-24 09:31:24,099 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 564.32条/s][0m
2025-06-24 09:31:24,132 - INFO - [processing] 📋 输出: ð å¤çæ¥æ: 2025-06-17 (27 æ¡è®°å½)
2025-06-24 09:31:24,132 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:31:24,133 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:31:24,134 - INFO - [processing] 📋 输出: â Transaction ID 2936647600 å¨ç¬¬äºæä»¶ä¸­æªæ¾å°å¹éè®°å½
2025-06-24 09:31:24,134 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:31:24,134 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:31:24,134 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936662760ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,135 - INFO - [processing] 📋 输出: â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,135 - INFO - [processing] 📋 输出: ð Transaction ID 2936662760 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,135 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:31:24,135 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:31:24,135 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936728235ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,136 - INFO - [processing] 📋 输出: â è®°å½ 24 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,136 - INFO - [processing] 📋 输出: ð Transaction ID 2936728235 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,136 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:31:24,136 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:31:24,136 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936768998ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,137 - INFO - [processing] 📋 输出: â è®°å½ 23 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,137 - INFO - [processing] 📋 输出: ð Transaction ID 2936768998 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,137 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:31:24,137 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:31:24,137 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936769108ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,137 - INFO - [processing] 📋 输出: â è®°å½ 22 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,138 - INFO - [processing] 📋 输出: ð Transaction ID 2936769108 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,138 - INFO - [processing] 📋 输出: ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0
2025-06-24 09:31:24,138 - INFO - [processing] 📋 输出: â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID
2025-06-24 09:31:24,138 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2936814575ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,138 - INFO - [processing] 📋 输出: â è®°å½ 21 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,138 - INFO - [processing] 📋 输出: ð Transaction ID 2936814575 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,139 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937008365ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,139 - INFO - [processing] 📋 输出: â è®°å½ 20 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,139 - INFO - [processing] 📋 输出: ð Transaction ID 2937008365 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,139 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937069558ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,139 - INFO - [processing] 📋 输出: â è®°å½ 19 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,140 - INFO - [processing] 📋 输出: ð Transaction ID 2937069558 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,140 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937106579ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,140 - INFO - [processing] 📋 输出: â è®°å½ 18 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,140 - INFO - [processing] 📋 输出: ð Transaction ID 2937106579 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,140 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937110200ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,140 - INFO - [processing] 📋 输出: â è®°å½ 17 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,141 - INFO - [processing] 📋 输出: ð Transaction ID 2937110200 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,141 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937162972ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,141 - INFO - [processing] 📋 输出: â è®°å½ 16 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,141 - INFO - [processing] 📋 输出: ð Transaction ID 2937162972 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,141 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937230887ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,141 - INFO - [processing] 📋 输出: â è®°å½ 15 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,142 - INFO - [processing] 📋 输出: ð Transaction ID 2937230887 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,142 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937461430ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,143 - INFO - [processing] 📋 输出: â è®°å½ 14 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,144 - INFO - [processing] 📋 输出: ð Transaction ID 2937461430 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,144 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937558305ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,144 - INFO - [processing] 📋 输出: â è®°å½ 13 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,144 - INFO - [processing] 📋 输出: ð Transaction ID 2937558305 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,145 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937652905ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,145 - INFO - [processing] 📋 输出: â è®°å½ 12 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,145 - INFO - [processing] 📋 输出: ð Transaction ID 2937652905 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,145 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665684ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,146 - INFO - [processing] 📋 输出: â è®°å½ 11 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,146 - INFO - [processing] 📋 输出: ð Transaction ID 2937665684 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,146 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937665773ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,146 - INFO - [processing] 📋 输出: â è®°å½ 10 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,146 - INFO - [processing] 📋 输出: ð Transaction ID 2937665773 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,147 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937697214ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,147 - INFO - [processing] 📋 输出: â è®°å½ 9 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,147 - INFO - [processing] 📋 输出: ð Transaction ID 2937697214 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,147 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761338ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,147 - INFO - [processing] 📋 输出: â è®°å½ 8 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,147 - INFO - [processing] 📋 输出: ð Transaction ID 2937761338 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,148 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937761946ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,148 - INFO - [processing] 📋 输出: â è®°å½ 7 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,148 - INFO - [processing] 📋 输出: ð Transaction ID 2937761946 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,148 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937770418ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,148 - INFO - [processing] 📋 输出: â è®°å½ 6 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,149 - INFO - [processing] 📋 输出: ð Transaction ID 2937770418 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,149 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937779767ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,149 - INFO - [processing] 📋 输出: â è®°å½ 5 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,149 - INFO - [processing] 📋 输出: ð Transaction ID 2937779767 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,149 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937802660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,149 - INFO - [processing] 📋 输出: â è®°å½ 4 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,150 - INFO - [processing] 📋 输出: ð Transaction ID 2937802660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,150 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937888689ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,150 - INFO - [processing] 📋 输出: â è®°å½ 3 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,150 - INFO - [processing] 📋 输出: ð Transaction ID 2937888689 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,150 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937960660ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,151 - INFO - [processing] 📋 输出: â è®°å½ 2 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,151 - INFO - [processing] 📋 输出: ð Transaction ID 2937960660 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,151 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2937962057ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,152 - INFO - [processing] 📋 输出: â è®°å½ 1 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,152 - INFO - [processing] 📋 输出: ð Transaction ID 2937962057 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,152 - INFO - [processing] 📋 输出: ð å¤çTransaction ID 2938190334ï¼æ¾å° 1 æ¡å¹éè®°å½
2025-06-24 09:31:24,153 - INFO - [processing] 📋 输出: â è®°å½ 0 æ è®°è®¾ç½®æå: Matched_Flag = True
2025-06-24 09:31:24,153 - INFO - [processing] 📋 输出: ð Transaction ID 2938190334 å¹éç»è®¡: æå 1, å¤±è´¥ 0
2025-06-24 09:31:24,153 - INFO - [processing] 📋 输出: â å¤çå®æï¼èæ¶: 0.07ç§
2025-06-24 09:31:24,153 - INFO - [processing] 📋 输出: ð transaction_id æ¨¡å¼ç»è®¡:
2025-06-24 09:31:24,153 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 09:31:24,154 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 09:31:24,154 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 09:31:24,154 - INFO - [processing] 📋 输出: â æ¨¡ååæ°æ®å¤çå®æ
2025-06-24 09:31:24,154 - INFO - [processing] 📋 输出: ð éªè¯ - å®éå¹éçè®°å½æ°é: 26
2025-06-24 09:31:24,154 - INFO - [processing] 📋 输出: ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é: 27
2025-06-24 09:31:24,154 - INFO - [processing] 📋 输出: ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼
2025-06-24 09:31:24,155 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 09:31:24,155 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 09:31:24,155 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 09:31:24,155 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 09:31:24,155 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 09:31:24,155 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 09:31:24,156 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 09:31:24,156 - INFO - [processing] 📋 输出: Order status
2025-06-24 09:31:24,156 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 09:31:24,156 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 09:31:24,156 - INFO - [processing] 📋 输出: ð æå¥ç»è®¡: 2æ¡è®°å½, æ»éé¢: RM10.00
2025-06-24 09:31:24,156 - INFO - [processing] 📋 输出: 1. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 09:31:24,157 - INFO - [processing] 📋 输出: 2. TXN:2936647600, Order:603010580, RM5.00
2025-06-24 09:31:24,157 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½ï¼: RM200.00
2025-06-24 09:31:24,157 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å
2025-06-24 09:31:24,157 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼: RM200.00
2025-06-24 09:31:24,157 - INFO - [processing] 📋 输出: éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼: RM0.00
2025-06-24 09:31:24,158 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 09:31:24,158 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 09:31:24,158 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 09:31:24,158 - INFO - [processing] 📋 输出: æ§è¡æ°æ®æ¢å¤åè¡¥å¨...
2025-06-24 09:31:24,158 - INFO - [processing] 📋 输出: ð å¼å§æ§è¡æ°æ®æ¢å¤...
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: â Transaction Numä¿®å¤: 0 æ¡
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: â Equipmentä¿¡æ¯æ¢å¤: 1 æ¡
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: â Order No.ä¿¡æ¯æ¢å¤: 0 æ¡
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: True: 27
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: False: 2
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 09:31:24,159 - INFO - [processing] 📋 输出: True: 27
2025-06-24 09:31:24,160 - INFO - [processing] 📋 输出: False: 2
2025-06-24 09:31:24,160 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 09:31:24,160 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 09:31:24,160 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 09:31:24,333 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£
2025-06-24 09:31:24,334 - INFO - [processing] 📋 输出: Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£
2025-06-24 09:31:24,334 - INFO - [processing] 📋 输出: å¤çå®æï¼ç»æå·²åå¥ C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:31:24,334 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 09:31:24,334 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 09:31:24,334 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶æç»æ»éé¢: RM200.00
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶æ»éé¢: RM200.00
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: éé¢å·®å¼: RM0.00
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: â éé¢å¹éæåï¼
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: ä½¿ç¨æå®çæä»¶è·¯å¾:
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: ç¬¬äºæä»¶: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:31:24,335 - INFO - [processing] 📋 输出: Sheetåç§°: 170625
2025-06-24 09:31:24,336 - INFO - [processing] 📋 输出: èæ¬æ§è¡å®æ
2025-06-24 09:31:24,542 - INFO - [processing] 文件处理成功完成
2025-06-24 09:48:47,321 - INFO - 双数据库模块加载成功
2025-06-24 09:48:47,819 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 09:48:47,820 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:48:47,820 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 09:48:48,154 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 09:48:48,162 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:48:48,162 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 09:48:48,268 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:48:48,268 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:48:48,289 - INFO - [general] 数据库设置已加载
2025-06-24 09:48:48,289 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:48:48,290 - INFO - [settings] 数据库设置已加载
2025-06-24 09:48:48,290 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:48:48,549 - INFO - [database_config] 数据库状态已刷新
2025-06-24 09:48:48,554 - INFO - 数据处理与导入应用已启动
2025-06-24 09:48:48,662 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 09:48:59,252 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 09:49:17,471 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:49:21,533 - INFO - [processing] 开始处理文件...
2025-06-24 09:49:21,534 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 09:49:21,535 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 09:49:21,535 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 09:49:21,635 - INFO - [processing] 错误: File "c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py", line 2076
2025-06-24 09:49:21,636 - INFO - [processing] 错误: else:
2025-06-24 09:49:21,636 - INFO - [processing] 错误: IndentationError: expected an indented block after 'for' statement on line 2074
2025-06-24 09:49:21,640 - INFO - [processing] 文件处理失败，返回码: 1
2025-06-24 09:56:53,719 - INFO - [processing] 开始处理文件...
2025-06-24 09:56:53,720 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 09:56:53,720 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 09:56:53,721 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 09:56:54,826 - INFO - [processing] 📋 输出: å¼å§æ°æ®å¤ç...
2025-06-24 09:56:54,827 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶å è½½å®æ: 27 æ¡è®°å½
2025-06-24 09:56:54,827 - INFO - [processing] 📋 输出: Transaction IDåæ£æµå®æ
2025-06-24 09:56:54,827 - INFO - [processing] 错误: Traceback (most recent call last):
2025-06-24 09:56:54,827 - INFO - [processing] 📋 输出: å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 09:56:54,827 - INFO - [processing] 📋 输出: Transaction Numå·å¤å¹éè½å
2025-06-24 09:56:54,828 - INFO - [processing] 📋 输出: å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 09:56:54,829 - INFO - [processing] 错误: File "c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py", line 1520, in <module>
2025-06-24 09:56:54,830 - INFO - [processing] 错误: class UnifiedDataProcessor:
2025-06-24 09:56:54,830 - INFO - [processing] 错误: ...<288 lines>...
2025-06-24 09:56:54,830 - INFO - [processing] 错误: })
2025-06-24 09:56:54,830 - INFO - [processing] 错误: File "c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py", line 1796, in UnifiedDataProcessor
2025-06-24 09:56:54,830 - INFO - [processing] 错误: if post_insert_marked_count != pre_insert_marked_count + 1:  # 应该增加1（新插入的记录）
2025-06-24 09:56:54,830 - INFO - [processing] 错误: ^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-24 09:56:54,830 - INFO - [processing] 错误: NameError: name 'post_insert_marked_count' is not defined
2025-06-24 09:56:54,978 - INFO - [processing] 文件处理失败，返回码: 1
2025-06-24 09:56:58,274 - INFO - 双数据库模块加载成功
2025-06-24 09:56:58,511 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 09:56:58,512 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:56:58,513 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 09:56:59,028 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 09:56:59,036 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 09:56:59,036 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 09:56:59,166 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:56:59,166 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 09:56:59,191 - INFO - [general] 数据库设置已加载
2025-06-24 09:56:59,192 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:56:59,192 - INFO - [settings] 数据库设置已加载
2025-06-24 09:56:59,192 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 09:56:59,473 - INFO - [database_config] 数据库状态已刷新
2025-06-24 09:56:59,479 - INFO - 数据处理与导入应用已启动
2025-06-24 09:56:59,589 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 09:57:04,659 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 09:57:07,190 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 09:57:11,880 - INFO - [processing] 开始处理文件...
2025-06-24 09:57:11,881 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 09:57:11,881 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 09:57:11,882 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 09:57:12,790 - INFO - [processing] 📋 输出: å¼å§æ°æ®å¤ç...
2025-06-24 09:57:12,791 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶å è½½å®æ: 27 æ¡è®°å½
2025-06-24 09:57:12,791 - INFO - [processing] 📋 输出: Transaction IDåæ£æµå®æ
2025-06-24 09:57:12,791 - INFO - [processing] 错误: Traceback (most recent call last):
2025-06-24 09:57:12,791 - INFO - [processing] 📋 输出: å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 09:57:12,791 - INFO - [processing] 📋 输出: Transaction Numå·å¤å¹éè½å
2025-06-24 09:57:12,791 - INFO - [processing] 📋 输出: å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 09:57:12,794 - INFO - [processing] 错误: File "c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py", line 1520, in <module>
2025-06-24 09:57:12,794 - INFO - [processing] 错误: class UnifiedDataProcessor:
2025-06-24 09:57:12,794 - INFO - [processing] 错误: ...<288 lines>...
2025-06-24 09:57:12,794 - INFO - [processing] 错误: })
2025-06-24 09:57:12,794 - INFO - [processing] 错误: File "c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py", line 1796, in UnifiedDataProcessor
2025-06-24 09:57:12,794 - INFO - [processing] 错误: if post_insert_marked_count != pre_insert_marked_count + 1:  # 应该增加1（新插入的记录）
2025-06-24 09:57:12,794 - INFO - [processing] 错误: ^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-24 09:57:12,794 - INFO - [processing] 错误: NameError: name 'post_insert_marked_count' is not defined
2025-06-24 09:57:12,954 - INFO - [processing] 文件处理失败，返回码: 1
2025-06-24 10:02:33,933 - INFO - [processing] 开始处理文件...
2025-06-24 10:02:33,933 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 10:02:33,934 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 10:02:33,934 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 10:02:34,882 - INFO - [processing] 📋 输出: å¼å§æ°æ®å¤ç...
2025-06-24 10:02:34,882 - INFO - [processing] 📋 输出: ç¬¬ä¸æä»¶å è½½å®æ: 27 æ¡è®°å½
2025-06-24 10:02:34,882 - INFO - [processing] 📋 输出: Transaction IDåæ£æµå®æ
2025-06-24 10:02:34,883 - INFO - [processing] 📋 输出: å¼å§æºè½æ£æµTransaction Numå¹éè½å...
2025-06-24 10:02:34,883 - INFO - [processing] 📋 输出: Transaction Numå·å¤å¹éè½å
2025-06-24 10:02:34,883 - INFO - [processing] 📋 输出: å¹éæ¨¡å¼: Transaction IDå¹é
2025-06-24 10:02:34,883 - INFO - [processing] 📋 输出: å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id
2025-06-24 10:02:34,969 - INFO - [processing] 错误: 处理数据记录:   0%|[32m                                                          [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段:   0%|[32m                                                     [0m| 0/27 [00:00<?, ?条/s][0m
处理 9_digit 阶段: 100%|[32m███████████████████████████████████████████[0m| 27/27 [00:00<00:00, 309.01条/s][0m
2025-06-24 10:02:35,136 - INFO - [processing] 📋 输出: å¤ç: 27 æ¡
2025-06-24 10:02:35,137 - INFO - [processing] 📋 输出: å¹é: 26 æ¡
2025-06-24 10:02:35,137 - INFO - [processing] 📋 输出: æå¥: 1 æ¡
2025-06-24 10:02:35,137 - INFO - [processing] 📋 输出: æ°æ®å¤çå®æ
2025-06-24 10:02:35,137 - INFO - [processing] 📋 输出: matched_indices: 26
2025-06-24 10:02:35,137 - INFO - [processing] 📋 输出: DataFrameæ è®°: 27
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: å·®å¼: -1
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: æ»è®°å½æ°: 29
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: å·²æ è®°è®°å½æ°: 27
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: æªæ è®°è®°å½æ°: 2
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: Matched_Flag  False  True
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: Order status
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: Finish            0     27
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: Refunded          2      0
2025-06-24 10:02:35,138 - INFO - [processing] 📋 输出: Transaction IDå¹é: 3104æ¡
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: Transaction IDæå¥: 42æ¡
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: True: 27
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: False: 2
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: True: 27
2025-06-24 10:02:35,139 - INFO - [processing] 📋 输出: False: 2
2025-06-24 10:02:35,140 - INFO - [processing] 📋 输出: NaN: 0
2025-06-24 10:02:35,140 - INFO - [processing] 📋 输出: 'Finish': 27 æ¡
2025-06-24 10:02:35,140 - INFO - [processing] 📋 输出: 'Refunded': 2 æ¡
2025-06-24 10:02:35,140 - INFO - [processing] 📋 输出: - æ°æ®å·²åå¥ DATA sheet
2025-06-24 10:02:35,140 - INFO - [processing] 📋 输出: - æ¥å¿å·²åå¥ LOG sheet
2025-06-24 10:02:35,305 - INFO - [processing] 文件处理成功完成
2025-06-24 10:06:13,087 - INFO - [processing] 开始处理文件...
2025-06-24 10:06:13,089 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 10:06:13,090 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 10:06:13,092 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 10:06:30,893 - INFO - 双数据库模块加载成功
2025-06-24 10:06:31,141 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 10:06:31,142 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:06:31,142 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 10:06:31,675 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 10:06:31,684 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:06:31,684 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 10:06:31,923 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:06:31,924 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:06:31,952 - INFO - [general] 数据库设置已加载
2025-06-24 10:06:31,953 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:06:31,953 - INFO - [settings] 数据库设置已加载
2025-06-24 10:06:31,953 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:06:32,278 - INFO - [database_config] 数据库状态已刷新
2025-06-24 10:06:32,283 - INFO - 数据处理与导入应用已启动
2025-06-24 10:06:32,394 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 10:06:41,785 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 10:06:43,769 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 10:06:48,376 - INFO - [processing] 开始处理文件...
2025-06-24 10:06:48,379 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 10:06:48,380 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 10:06:48,381 - INFO - [processing] 使用指定的Sheet名称: 170625
2025-06-24 10:07:38,751 - INFO - [processing] 已清空文件选择
2025-06-24 10:07:39,519 - INFO - [processing] 已清空文件选择
2025-06-24 10:07:39,696 - INFO - [processing] 已清空文件选择
2025-06-24 10:07:43,333 - INFO - 双数据库模块加载成功
2025-06-24 10:07:43,604 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 10:07:43,605 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:07:43,605 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 10:07:44,149 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 10:07:44,160 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:07:44,160 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 10:07:44,332 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:07:44,332 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:07:44,374 - INFO - [general] 数据库设置已加载
2025-06-24 10:07:44,375 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:07:44,375 - INFO - [settings] 数据库设置已加载
2025-06-24 10:07:44,375 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:07:44,934 - INFO - [database_config] 数据库状态已刷新
2025-06-24 10:07:44,943 - INFO - 数据处理与导入应用已启动
2025-06-24 10:07:45,073 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 10:07:50,415 - INFO - 双数据库模块加载成功
2025-06-24 10:07:50,688 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 10:07:50,689 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:07:50,689 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 10:07:51,196 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 10:07:51,206 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:07:51,207 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 10:07:51,382 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:07:51,383 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:07:51,423 - INFO - [general] 数据库设置已加载
2025-06-24 10:07:51,424 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:07:51,424 - INFO - [settings] 数据库设置已加载
2025-06-24 10:07:51,424 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:07:51,972 - INFO - [database_config] 数据库状态已刷新
2025-06-24 10:07:51,979 - INFO - 数据处理与导入应用已启动
2025-06-24 10:07:52,107 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 10:08:00,996 - INFO - 双数据库模块加载成功
2025-06-24 10:08:01,253 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-24 10:08:01,253 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:08:01,253 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-24 10:08:01,727 - INFO - [general] 可用数据库: SQLite, PostgreSQL
2025-06-24 10:08:01,741 - INFO - [general] 请使用浏览按钮选择文件
2025-06-24 10:08:01,742 - INFO - [import] 请使用浏览按钮选择文件
2025-06-24 10:08:01,894 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:08:01,895 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-24 10:08:01,940 - INFO - [general] 数据库设置已加载
2025-06-24 10:08:01,940 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:08:01,941 - INFO - [settings] 数据库设置已加载
2025-06-24 10:08:01,941 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-24 10:08:02,448 - INFO - [database_config] 数据库状态已刷新
2025-06-24 10:08:02,454 - INFO - 数据处理与导入应用已启动
2025-06-24 10:08:02,577 - INFO - [settings] 已刷新备份列表，共11个备份文件
2025-06-24 10:08:06,582 - INFO - [processing] 已选择第一文件: TRANSACTION_17 TO_19062025_ZERO.xlsx
2025-06-24 10:08:08,847 - INFO - [processing] 已选择第二文件: 170625 CHINA ZERO - Copy.xlsx
2025-06-24 10:08:14,055 - INFO - [processing] 开始处理文件...
2025-06-24 10:08:14,056 - INFO - [processing] 使用模块化设计脚本: report 模块化设计 7.0.py
2025-06-24 10:08:14,056 - INFO - [processing] 脚本路径: c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
2025-06-24 10:08:14,057 - INFO - [processing] 使用指定的Sheet名称: 170625
