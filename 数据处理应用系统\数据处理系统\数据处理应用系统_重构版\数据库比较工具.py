# -*- coding: utf-8 -*-
"""
数据库比较工具
快速比较SQLite和PostgreSQL数据库的表结构和数据
"""

import sqlite3
import psycopg2
import pandas as pd

def compare_databases():
    """比较SQLite和PostgreSQL数据库"""
    
    # 连接配置
    sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
    pg_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    print('🔍 比较SQLite和PostgreSQL数据库表结构...')
    print('=' * 60)
    
    try:
        # 连接数据库
        sqlite_conn = sqlite3.connect(sqlite_path)
        sqlite_cursor = sqlite_conn.cursor()
        
        pg_conn = psycopg2.connect(**pg_config)
        pg_cursor = pg_conn.cursor()
        
        # 获取SQLite表信息
        sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        sqlite_tables = set([row[0] for row in sqlite_cursor.fetchall()])
        
        # 获取PostgreSQL表信息
        pg_cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
        """)
        pg_tables = set([row[0] for row in pg_cursor.fetchall()])
        
        print(f'📊 SQLite表数量: {len(sqlite_tables)}')
        print(f'📊 PostgreSQL表数量: {len(pg_tables)}')
        print()
        
        # 只在SQLite中的表
        sqlite_only = sqlite_tables - pg_tables
        if sqlite_only:
            print(f'🔸 只在SQLite中的表 ({len(sqlite_only)}个):')
            for table in sorted(sqlite_only):
                print(f'  - {table}')
            print()
            
        # 只在PostgreSQL中的表
        pg_only = pg_tables - sqlite_tables
        if pg_only:
            print(f'🔹 只在PostgreSQL中的表 ({len(pg_only)}个):')
            for table in sorted(pg_only):
                print(f'  - {table}')
            print()
            
        # 共同的表
        common_tables = sqlite_tables & pg_tables
        print(f'🔗 共同的表: {len(common_tables)} 个')
        print()
        
        # 详细比较每个表的行数
        print('📈 表行数比较:')
        print('-' * 50)
        
        total_sqlite_rows = 0
        total_pg_rows = 0
        
        for table in sorted(common_tables):
            try:
                # SQLite行数
                sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                sqlite_count = sqlite_cursor.fetchone()[0]
                total_sqlite_rows += sqlite_count
                
                # PostgreSQL行数
                pg_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                pg_count = pg_cursor.fetchone()[0]
                total_pg_rows += pg_count
                
                if sqlite_count == pg_count:
                    print(f'✅ {table:<25} {sqlite_count:>10,} 行 (一致)')
                else:
                    print(f'⚠️  {table:<25} SQLite={sqlite_count:,}, PostgreSQL={pg_count:,} (不一致)')
                    
            except Exception as e:
                print(f'❌ {table:<25} 比较失败: {e}')
        
        print('-' * 50)
        print(f'📊 总行数统计:')
        print(f'   SQLite总行数: {total_sqlite_rows:,}')
        print(f'   PostgreSQL总行数: {total_pg_rows:,}')
        print(f'   差异: {abs(total_sqlite_rows - total_pg_rows):,} 行')
        
        # 检查PostgreSQL中的额外表详情
        if pg_only:
            print(f'\n🔍 PostgreSQL额外表详情:')
            for table in sorted(pg_only):
                try:
                    pg_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    count = pg_cursor.fetchone()[0]
                    print(f'  📋 {table}: {count:,} 行')
                    
                    # 查看表结构
                    pg_cursor.execute(f"""
                        SELECT column_name, data_type 
                        FROM information_schema.columns 
                        WHERE table_name = '{table}' 
                        ORDER BY ordinal_position
                    """)
                    columns = pg_cursor.fetchall()
                    print(f'     列数: {len(columns)}')
                    if len(columns) <= 5:  # 只显示少量列
                        for col_name, col_type in columns:
                            print(f'     - {col_name} ({col_type})')
                    else:
                        print(f'     - 前3列: {", ".join([col[0] for col in columns[:3]])}...')
                        
                except Exception as e:
                    print(f'  ❌ 检查表 {table} 失败: {e}')
        
        sqlite_conn.close()
        pg_conn.close()
        
        print('\n✅ 数据库比较完成!')
        
    except Exception as e:
        print(f'❌ 比较数据库时出错: {e}')

def check_postgresql_extra_tables():
    """专门检查PostgreSQL中的额外表"""
    
    pg_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        pg_conn = psycopg2.connect(**pg_config)
        pg_cursor = pg_conn.cursor()
        
        # 获取所有表及其信息
        pg_cursor.execute("""
            SELECT 
                t.table_name,
                t.table_type,
                COALESCE(s.n_tup_ins, 0) as estimated_rows
            FROM information_schema.tables t
            LEFT JOIN pg_stat_user_tables s ON t.table_name = s.relname
            WHERE t.table_schema = 'public'
            ORDER BY t.table_name
        """)
        
        tables_info = pg_cursor.fetchall()
        
        print('🔍 PostgreSQL数据库所有表信息:')
        print('=' * 60)
        
        for table_name, table_type, est_rows in tables_info:
            # 获取准确的行数
            try:
                pg_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                actual_rows = pg_cursor.fetchone()[0]
                print(f'📋 {table_name:<25} {actual_rows:>10,} 行 ({table_type})')
            except Exception as e:
                print(f'❌ {table_name:<25} 查询失败: {e}')
        
        pg_conn.close()
        
    except Exception as e:
        print(f'❌ 检查PostgreSQL表时出错: {e}')

if __name__ == "__main__":
    print("🚀 启动数据库比较工具")
    print()
    
    # 基本比较
    compare_databases()
    
    print("\n" + "="*60)
    
    # PostgreSQL详细信息
    check_postgresql_extra_tables()
