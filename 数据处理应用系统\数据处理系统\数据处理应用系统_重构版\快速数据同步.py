# -*- coding: utf-8 -*-
"""
快速数据同步工具
将SQLite数据同步到PostgreSQL
"""

import os
import sqlite3
import psycopg2
import pandas as pd
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

class QuickDataSync:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("快速数据同步工具")
        self.root.geometry("700x500")
        
        # 数据库配置
        self.sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
        self.pg_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'postgres',
            'user': 'postgres',
            'password': 'zerochon'
        }
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = ttk.Label(self.root, text="快速数据同步工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 配置框架
        config_frame = ttk.LabelFrame(self.root, text="数据库配置", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # SQLite路径
        ttk.Label(config_frame, text="SQLite数据库:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.sqlite_var = tk.StringVar(value=self.sqlite_path)
        ttk.Entry(config_frame, textvariable=self.sqlite_var, width=60).grid(row=0, column=1, padx=5)
        
        # PostgreSQL配置
        ttk.Label(config_frame, text="PostgreSQL:").grid(row=1, column=0, sticky=tk.W, padx=5)
        pg_info = f"{self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}"
        ttk.Label(config_frame, text=pg_info).grid(row=1, column=1, sticky=tk.W, padx=5)
        
        # 操作按钮
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="🔍 检查连接", command=self.check_connections).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📊 比较数据", command=self.compare_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 同步数据", command=self.sync_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧹 清理测试表", command=self.clean_test_tables).pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(self.root, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 初始检查
        self.check_connections()
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def check_connections(self):
        """检查数据库连接"""
        self.log("🔍 检查数据库连接...")
        
        # 检查SQLite
        try:
            if os.path.exists(self.sqlite_var.get()):
                conn = sqlite3.connect(self.sqlite_var.get())
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                conn.close()
                self.log(f"✅ SQLite连接成功，包含 {table_count} 个表")
            else:
                self.log(f"❌ SQLite文件不存在: {self.sqlite_var.get()}")
                return
        except Exception as e:
            self.log(f"❌ SQLite连接失败: {e}")
            return
            
        # 检查PostgreSQL
        try:
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            table_count = cursor.fetchone()[0]
            conn.close()
            self.log(f"✅ PostgreSQL连接成功，包含 {table_count} 个表")
        except Exception as e:
            self.log(f"❌ PostgreSQL连接失败: {e}")
            
    def compare_data(self):
        """比较数据"""
        self.log("📊 比较SQLite和PostgreSQL数据...")
        
        try:
            # 连接数据库
            sqlite_conn = sqlite3.connect(self.sqlite_var.get())
            sqlite_cursor = sqlite_conn.cursor()
            
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_cursor = pg_conn.cursor()
            
            # 获取表信息
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            sqlite_tables = set([row[0] for row in sqlite_cursor.fetchall()])
            
            pg_cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            pg_tables = set([row[0] for row in pg_cursor.fetchall()])
            
            # 比较结果
            common_tables = sqlite_tables & pg_tables
            sqlite_only = sqlite_tables - pg_tables
            pg_only = pg_tables - sqlite_tables
            
            self.log(f"📋 共同表: {len(common_tables)} 个")
            self.log(f"📋 仅SQLite: {len(sqlite_only)} 个")
            self.log(f"📋 仅PostgreSQL: {len(pg_only)} 个")
            
            if pg_only:
                self.log(f"PostgreSQL独有表: {', '.join(sorted(pg_only))}")
            
            # 比较行数
            total_diff = 0
            for table in sorted(common_tables):
                try:
                    sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                    sqlite_count = sqlite_cursor.fetchone()[0]
                    
                    pg_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    pg_count = pg_cursor.fetchone()[0]
                    
                    diff = sqlite_count - pg_count
                    total_diff += abs(diff)
                    
                    if diff == 0:
                        self.log(f"✅ {table}: {sqlite_count:,} 行 (一致)")
                    elif diff > 0:
                        self.log(f"⚠️ {table}: SQLite多 {diff:,} 行 (SQLite={sqlite_count:,}, PG={pg_count:,})")
                    else:
                        self.log(f"⚠️ {table}: PostgreSQL多 {abs(diff):,} 行 (SQLite={sqlite_count:,}, PG={pg_count:,})")
                        
                except Exception as e:
                    self.log(f"❌ 比较表 {table} 失败: {e}")
            
            self.log(f"📊 总差异: {total_diff:,} 行")
            
            sqlite_conn.close()
            pg_conn.close()
            
        except Exception as e:
            self.log(f"❌ 比较数据失败: {e}")
            
    def sync_data(self):
        """同步数据"""
        # 确认操作
        result = messagebox.askyesnocancel(
            "确认同步",
            "选择同步方式：\n\n"
            "是 = 完整同步（删除PostgreSQL所有数据，重新导入SQLite数据）\n"
            "否 = 增量同步（仅同步差异数据）\n"
            "取消 = 取消操作"
        )
        
        if result is None:
            self.log("❌ 用户取消了同步操作")
            return
            
        full_sync = result
        
        if full_sync:
            self._full_sync()
        else:
            self._incremental_sync()
            
    def _full_sync(self):
        """完整同步"""
        self.log("🔄 开始完整同步...")
        
        try:
            sqlite_conn = sqlite3.connect(self.sqlite_var.get())
            sqlite_cursor = sqlite_conn.cursor()
            
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_cursor = pg_conn.cursor()
            
            # 获取SQLite表
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in sqlite_cursor.fetchall()]
            
            total_synced = 0
            
            for table_name in tables:
                try:
                    self.log(f"🔄 同步表: {table_name}")
                    
                    # 检查PostgreSQL中是否存在该表
                    pg_cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = %s
                        )
                    """, (table_name,))
                    
                    if not pg_cursor.fetchone()[0]:
                        self.log(f"  ⚠️ 表 {table_name} 在PostgreSQL中不存在，跳过")
                        continue
                    
                    # 清空PostgreSQL表
                    pg_cursor.execute(f'DELETE FROM "{table_name}"')
                    
                    # 读取SQLite数据
                    df = pd.read_sql_query(f'SELECT * FROM `{table_name}`', sqlite_conn)
                    
                    if len(df) == 0:
                        self.log(f"  ✅ 表 {table_name} 为空，跳过")
                        continue
                    
                    # 获取列信息
                    sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in sqlite_cursor.fetchall()]
                    
                    # 批量插入
                    quoted_columns = [f'"{col}"' for col in columns]
                    placeholders = ', '.join(['%s'] * len(columns))
                    insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'
                    
                    batch_size = 1000
                    for i in range(0, len(df), batch_size):
                        batch_df = df.iloc[i:i+batch_size]
                        batch_data = []
                        for _, row in batch_df.iterrows():
                            row_data = [row[col] if col in row and not pd.isna(row[col]) else None for col in columns]
                            batch_data.append(tuple(row_data))
                        
                        pg_cursor.executemany(insert_sql, batch_data)
                        pg_conn.commit()
                        
                        progress = min(i + batch_size, len(df))
                        self.log(f"    进度: {progress:,}/{len(df):,}")
                    
                    total_synced += len(df)
                    self.log(f"  ✅ 表 {table_name} 同步完成: {len(df):,} 行")
                    
                except Exception as e:
                    self.log(f"  ❌ 同步表 {table_name} 失败: {e}")
            
            sqlite_conn.close()
            pg_conn.close()
            
            self.log(f"✅ 完整同步完成，总计: {total_synced:,} 行")
            
        except Exception as e:
            self.log(f"❌ 完整同步失败: {e}")
            
    def _incremental_sync(self):
        """增量同步"""
        self.log("🔄 开始增量同步...")
        self.log("⚠️ 增量同步功能正在开发中，暂时执行完整同步")
        self._full_sync()
        
    def clean_test_tables(self):
        """清理测试表"""
        if not messagebox.askyesno("确认清理", "是否删除PostgreSQL中的测试表？\n(TEST_Table_Both, TEST_Table_PostgreSQL)"):
            return
            
        try:
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_cursor = pg_conn.cursor()
            
            test_tables = ['TEST_Table_Both', 'TEST_Table_PostgreSQL']
            
            for table in test_tables:
                try:
                    pg_cursor.execute(f'DROP TABLE IF EXISTS "{table}" CASCADE')
                    self.log(f"✅ 已删除测试表: {table}")
                except Exception as e:
                    self.log(f"❌ 删除测试表 {table} 失败: {e}")
            
            pg_conn.commit()
            pg_conn.close()
            
            self.log("✅ 测试表清理完成")
            
        except Exception as e:
            self.log(f"❌ 清理测试表失败: {e}")
            
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = QuickDataSync()
    app.run()
