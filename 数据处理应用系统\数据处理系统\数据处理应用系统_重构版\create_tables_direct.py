# -*- coding: utf-8 -*-
"""
直接创建Refunding和Close表
最简化版本，直接执行SQL
"""

import os
import sqlite3

def main():
    # 数据库路径
    db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    
    print("🎯 直接创建Refunding和Close表")
    print(f"📁 数据库路径: {db_path}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查源表...")
        
        # 检查IOT_Sales表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='IOT_Sales'")
        iot_exists = cursor.fetchone() is not None
        print(f"IOT_Sales表存在: {iot_exists}")
        
        # 检查ZERO_Sales表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ZERO_Sales'")
        zero_exists = cursor.fetchone() is not None
        print(f"ZERO_Sales表存在: {zero_exists}")
        
        if not iot_exists or not zero_exists:
            print("❌ 源表不存在，无法创建Refunding和Close表")
            conn.close()
            return
        
        # 获取IOT_Sales表结构
        cursor.execute("PRAGMA table_info(IOT_Sales)")
        columns = cursor.fetchall()
        
        # 构建CREATE TABLE语句
        column_defs = []
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            not_null = " NOT NULL" if col[3] else ""
            default_val = f" DEFAULT {col[4]}" if col[4] is not None else ""
            pk = " PRIMARY KEY AUTOINCREMENT" if col[5] and col_name == "ID" else ""
            
            column_def = f"{col_name} {col_type}{pk}{not_null}{default_val}"
            column_defs.append(column_def)
        
        columns_sql = ",\n    ".join(column_defs)
        
        # 创建四个新表
        tables = [
            "IOT_Sales_Refunding",
            "IOT_Sales_Close", 
            "ZERO_Sales_Refunding",
            "ZERO_Sales_Close"
        ]
        
        print("🏗️ 创建新表...")
        for table_name in tables:
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {columns_sql}
            )
            """
            cursor.execute(create_sql)
            print(f"✅ 创建表: {table_name}")
        
        # 迁移数据
        print("📊 迁移数据...")
        
        # IOT_Sales -> IOT_Sales_Refunding
        cursor.execute("""
            INSERT OR IGNORE INTO IOT_Sales_Refunding 
            SELECT * FROM IOT_Sales 
            WHERE LOWER(Order_status) IN ('refunded', 'refunding')
        """)
        iot_refunding_count = cursor.rowcount
        print(f"  IOT_Sales_Refunding: {iot_refunding_count} 条")
        
        # IOT_Sales -> IOT_Sales_Close
        cursor.execute("""
            INSERT OR IGNORE INTO IOT_Sales_Close 
            SELECT * FROM IOT_Sales 
            WHERE LOWER(Order_status) IN ('close', 'closed')
        """)
        iot_close_count = cursor.rowcount
        print(f"  IOT_Sales_Close: {iot_close_count} 条")
        
        # ZERO_Sales -> ZERO_Sales_Refunding
        cursor.execute("""
            INSERT OR IGNORE INTO ZERO_Sales_Refunding 
            SELECT * FROM ZERO_Sales 
            WHERE LOWER(Order_status) IN ('refunded', 'refunding')
        """)
        zero_refunding_count = cursor.rowcount
        print(f"  ZERO_Sales_Refunding: {zero_refunding_count} 条")
        
        # ZERO_Sales -> ZERO_Sales_Close
        cursor.execute("""
            INSERT OR IGNORE INTO ZERO_Sales_Close 
            SELECT * FROM ZERO_Sales 
            WHERE LOWER(Order_status) IN ('close', 'closed')
        """)
        zero_close_count = cursor.rowcount
        print(f"  ZERO_Sales_Close: {zero_close_count} 条")
        
        # 创建复杂统计视图（按您的要求格式）
        print("📈 创建统计视图...")
        cursor.execute("DROP VIEW IF EXISTS Refunding_Close_Statistics")

        view_sql = """
        CREATE VIEW Refunding_Close_Statistics AS
        WITH
          -- 1) 预聚合Refunding和Close数据
          Refunding_Close_Aggs AS (
            SELECT
              Equipment_ID AS Chair_Serial_No,
              DATE(Order_time) AS Sale_Date,
              SUM(CASE WHEN Source = 'IOT_Refunding' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Refund,
              SUM(CASE WHEN Source = 'IOT_Close' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Close,
              SUM(CASE WHEN Source = 'ZERO_Refunding' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Refund,
              SUM(CASE WHEN Source = 'ZERO_Close' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Close,
              SUM(CASE WHEN Source = 'IOT_Refunding' THEN 1 ELSE 0 END) AS IOT_Count_Refund,
              SUM(CASE WHEN Source = 'IOT_Close' THEN 1 ELSE 0 END) AS IOT_Count_Close,
              SUM(CASE WHEN Source = 'ZERO_Refunding' THEN 1 ELSE 0 END) AS ZERO_Count_Refund,
              SUM(CASE WHEN Source = 'ZERO_Close' THEN 1 ELSE 0 END) AS ZERO_Count_Close,
              SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Price
            FROM (
              SELECT Equipment_ID, Order_time, Order_price, 'IOT_Refunding' AS Source FROM IOT_Sales_Refunding
              UNION ALL
              SELECT Equipment_ID, Order_time, Order_price, 'IOT_Close' FROM IOT_Sales_Close
              UNION ALL
              SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Refunding' FROM ZERO_Sales_Refunding
              UNION ALL
              SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Close' FROM ZERO_Sales_Close
            ) AS AllRefundingClose
            WHERE Equipment_ID IS NOT NULL AND Order_time IS NOT NULL
            GROUP BY Equipment_ID, DATE(Order_time)
          ),

          -- 2) 构建日期维度
          All_Dates AS (
            SELECT DISTINCT Sale_Date FROM Refunding_Close_Aggs
          ),

          -- 3) 主关联逻辑（如果Current_Equipment表存在）
          Main_Join AS (
            SELECT
              COALESCE(e.Chair_Serial_No, a.Chair_Serial_No) AS Chair_Serial_No,
              e.STATE,
              e.Location,
              e.Quantity,
              e.Layer,
              e.Effective_From,
              e.Effective_To,
              e.Rental,
              e.DATE,
              a.Sale_Date,
              COALESCE(a.IOT_Price_Refund, 0) AS IOT_Price_Refund,
              COALESCE(a.IOT_Price_Close, 0) AS IOT_Price_Close,
              COALESCE(a.ZERO_Price_Refund, 0) AS ZERO_Price_Refund,
              COALESCE(a.ZERO_Price_Close, 0) AS ZERO_Price_Close,
              COALESCE(a.IOT_Count_Refund, 0) AS IOT_Count_Refund,
              COALESCE(a.IOT_Count_Close, 0) AS IOT_Count_Close,
              COALESCE(a.ZERO_Count_Refund, 0) AS ZERO_Count_Refund,
              COALESCE(a.ZERO_Count_Close, 0) AS ZERO_Count_Close,
              COALESCE(a.Total_Price, 0) AS Total_Price
            FROM Refunding_Close_Aggs a
            LEFT JOIN (
              SELECT * FROM Current_Equipment
              WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='Current_Equipment')
            ) e ON a.Chair_Serial_No = e.Chair_Serial_No
              AND a.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')
          )

        -- 最终结果
        SELECT * FROM Main_Join
        ORDER BY Chair_Serial_No, Sale_Date
        """
        
        cursor.execute(view_sql)
        print("✅ 创建视图: Refunding_Close_Statistics")
        
        # 提交更改
        conn.commit()
        
        # 验证结果
        print("\n📊 验证结果...")
        cursor.execute("SELECT Chair_Serial_No, Sale_Date, IOT_Price_Refund, IOT_Price_Close, ZERO_Price_Refund, ZERO_Price_Close, Total_Price FROM Refunding_Close_Statistics LIMIT 5")
        results = cursor.fetchall()
        
        total_migrated = iot_refunding_count + iot_close_count + zero_refunding_count + zero_close_count
        
        print(f"\n🎉 创建完成！")
        print(f"📊 总计迁移: {total_migrated} 条记录")
        print(f"📋 统计视图记录数: {len(results)}")

        if results:
            print(f"\n📋 视图数据示例（前5条）:")
            print(f"Chair_Serial_No | Sale_Date | IOT_Refund | IOT_Close | ZERO_Refund | ZERO_Close | Total")
            print("-" * 80)
            for row in results:
                print(f"{row[0]:<15} | {row[1]:<10} | {row[2]:<10} | {row[3]:<9} | {row[4]:<12} | {row[5]:<10} | {row[6]:<5}")
        else:
            print("📋 视图暂无数据（可能是因为没有Refunding或Close状态的记录）")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
