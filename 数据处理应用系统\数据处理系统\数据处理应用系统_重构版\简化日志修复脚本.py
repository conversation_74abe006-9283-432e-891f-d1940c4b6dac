# -*- coding: utf-8 -*-
"""
简化日志修复脚本
批量替换所有emoji和冗余输出为简化的中文版本
"""

import re
import os

def simplify_logging_output():
    """简化日志输出 - 批量替换emoji和冗余信息"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换规则 - 将详细输出改为简化版本
    replacements = [
        # 1. 移除或简化emoji输出
        (r'print\(f"📊[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"🔍[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"✅[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"❌[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"⚠️[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"🔧[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"🚨[^"]*"\)', 'log_manager.log_detailed'),
        
        # 2. 简化特定的详细输出
        (r'print\(f"🔍 调试[^"]*Transaction ID[^"]*"\)', '# 调试信息已简化'),
        (r'print\(f"🔍 修复前匹配数[^"]*"\)', '# 调试信息已简化'),
        (r'print\(f"🔍 处理Transaction ID[^"]*"\)', '# 处理信息已简化'),
        (r'print\(f"✅ 记录[^"]*标记设置成功[^"]*"\)', '# 标记信息已简化'),
        (r'print\(f"📊 Transaction ID[^"]*匹配统计[^"]*"\)', '# 统计信息已简化'),
        
        # 3. 移除详细的调试验证
        (r'print\(f"🔍 验证[^"]*"\)', '# 验证信息已简化'),
        (r'print\(f"🔍 实时标记验证[^"]*"\)', '# 验证信息已简化'),
        (r'print\(f"🔍 样本检查[^"]*"\)', '# 检查信息已简化'),
        
        # 4. 简化统计输出
        (r'print\(f"📊 第[^"]*文件[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"📊 匹配[^"]*"\)', 'log_manager.log_detailed'),
        (r'print\(f"📊 插入统计[^"]*"\)', 'log_manager.log_summary'),
        
        # 5. 移除冗余的进度信息
        (r'print\(f"🔍 总体进度[^"]*"\)', '# 进度信息已简化'),
        (r'print\(f"🔍 调试 - [^"]*"\)', '# 调试信息已简化'),
        
        # 6. 简化错误信息
        (r'print\(f"❌ Transaction ID[^"]*在第二文件中未找到匹配记录"\)', 'log_manager.log_detailed("Transaction ID未找到匹配记录")'),
        (r'print\(f"⚠️ Transaction ID仍无匹配[^"]*"\)', 'log_manager.log_detailed("Transaction ID无匹配")'),
        
        # 7. 移除详细的插入和更新日志
        (r'# print\(f"🔍 Transaction同步插入[^"]*"\)', '# 插入信息已简化'),
        (r'# print\(f"🔍 插入新记录[^"]*"\)', '# 插入信息已简化'),
        (r'# print\(f"🔍 插入前标记数量[^"]*"\)', '# 标记信息已简化'),
        (r'# print\(f"🔍 插入后标记数量[^"]*"\)', '# 标记信息已简化'),
        
        # 8. 简化最终统计
        (r'print\("✅ 模块化数据处理完成"\)', 'log_manager.log_summary("数据处理完成")'),
        (r'print\("✅ 所有Transaction ID都是唯一的"\)', 'log_manager.log_detailed("所有Transaction ID都是唯一的")'),
        (r'print\("✅ Transaction Num具备匹配能力"\)', 'log_manager.log_summary("Transaction Num具备匹配能力")'),
        
        # 9. 移除特定的emoji字符
        (r'📊', ''),
        (r'🔍', ''),
        (r'✅', ''),
        (r'❌', ''),
        (r'⚠️', ''),
        (r'🔧', ''),
        (r'🚨', ''),
        (r'🔄', ''),
        (r'🔴', ''),
        (r'📋', ''),
        (r'🎯', ''),
        (r'📈', ''),
        (r'⭐', ''),
        (r'🌟', ''),
        (r'💡', ''),
        (r'🎉', ''),
        (r'🚀', ''),
        (r'⚡', ''),
        (r'🔥', ''),
        (r'💪', ''),
        (r'👍', ''),
        (r'🎊', ''),
        (r'🎈', ''),
        (r'🎁', ''),
        (r'🏆', ''),
        (r'🥇', ''),
        (r'🎖️', ''),
        (r'🏅', ''),
        (r'🎗️', ''),
        (r'🎀', ''),
        (r'🎪', ''),
        (r'🎭', ''),
        (r'🎨', ''),
        (r'🎬', ''),
        (r'🎤', ''),
        (r'🎧', ''),
        (r'🎼', ''),
        (r'🎵', ''),
        (r'🎶', ''),
        (r'🎯', ''),
        (r'🎲', ''),
        (r'🎰', ''),
        (r'🎳', ''),
    ]
    
    # 应用替换
    modified_content = content
    replacement_count = 0
    
    for pattern, replacement in replacements:
        matches = re.findall(pattern, modified_content)
        if matches:
            modified_content = re.sub(pattern, replacement, modified_content)
            replacement_count += len(matches)
            print(f"替换了 {len(matches)} 个匹配项: {pattern[:50]}...")
    
    # 特殊处理：将剩余的详细print语句改为log_detailed
    detailed_patterns = [
        r'print\(f"第[^"]*文件[^"]*数量[^"]*"\)',
        r'print\(f"匹配[^"]*"\)',
        r'print\(f"处理[^"]*"\)',
        r'print\(f"验证[^"]*"\)',
        r'print\(f"调试[^"]*"\)',
    ]
    
    for pattern in detailed_patterns:
        matches = re.findall(pattern, modified_content)
        if matches:
            modified_content = re.sub(pattern, 'log_manager.log_detailed', modified_content)
            replacement_count += len(matches)
            print(f"转换为详细日志: {len(matches)} 个匹配项")
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"\n✅ 日志简化完成！")
    print(f"总共处理了 {replacement_count} 个替换")
    print(f"文件已更新: {script_path}")

def create_summary_output_version():
    """创建摘要输出版本 - 只保留关键统计信息"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加摘要输出函数
    summary_function = '''
# ======================【摘要输出函数】======================
def print_processing_summary(processor, df1_filtered, df2, total_bill_amt):
    """打印处理摘要 - 只显示关键统计信息"""
    
    # 获取统计信息
    if hasattr(processor, 'stats') and processor.stats.current_mode:
        stats = processor.stats.mode_stats[processor.stats.current_mode]
        
        log_manager.log_summary(f"处理完成: 匹配 {stats['matched']} 条，插入 {stats['inserted']} 条")
        
        # 金额验证
        df2_finish = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
                        (~df2["Order types"].str.strip().str.lower().str.contains("api", na=False))]
        final_total = df2_finish["Order price"].sum()
        
        if abs(total_bill_amt - final_total) < 0.01:
            log_manager.log_summary(f"金额验证: RM{total_bill_amt:.2f} 完全匹配")
        else:
            log_manager.log_summary(f"金额差异: RM{abs(total_bill_amt - final_total):.2f}")
        
        # 数据恢复统计
        if hasattr(processor, 'inserted_records') and processor.inserted_records:
            log_manager.log_summary(f"数据恢复: {len(processor.inserted_records)} 条记录已恢复")
    
    log_manager.log_summary("处理完成")

'''
    
    # 在文件末尾添加摘要函数调用
    if 'print_processing_summary' not in content:
        # 找到合适的位置插入函数
        insert_pos = content.find('# ======================【模块化核心类】======================')
        if insert_pos != -1:
            content = content[:insert_pos] + summary_function + content[insert_pos:]
        
        # 在处理完成后调用摘要函数
        final_summary_call = '''
# 显示处理摘要
print_processing_summary(processor, df1_filtered, df2, total_bill_amt)
'''
        
        # 找到合适的位置添加摘要调用
        insert_pos = content.find('print("脚本执行完成")')
        if insert_pos != -1:
            content = content[:insert_pos] + final_summary_call + content[insert_pos:]
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 摘要输出版本创建完成！")

if __name__ == "__main__":
    print("开始简化日志输出...")
    
    # 1. 简化现有的详细输出
    simplify_logging_output()
    
    # 2. 创建摘要输出版本
    create_summary_output_version()
    
    print("\n🎉 日志系统优化完成！")
    print("现在运行应用程序应该会看到简洁的中文界面显示。")
