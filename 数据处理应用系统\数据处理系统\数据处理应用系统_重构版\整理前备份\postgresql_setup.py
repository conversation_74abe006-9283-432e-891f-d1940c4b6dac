# -*- coding: utf-8 -*-
"""
便携式PostgreSQL安装和配置脚本
自动下载、安装和配置便携式PostgreSQL到指定目录
"""

import os
import sys
import zipfile
import shutil
import subprocess
import urllib.request
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

class PostgreSQLPortableInstaller:
    """便携式PostgreSQL安装器"""
    
    def __init__(self, base_dir: str = None):
        if base_dir is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        self.base_dir = Path(base_dir)
        self.postgresql_dir = self.base_dir / "PostgreSQL database"
        self.backup_dir = self.base_dir / "backups" / "postgresql"
        self.config_file = self.postgresql_dir / "config.json"
        
        # PostgreSQL便携版下载信息
        self.postgresql_version = "15.4"
        self.download_url = "https://get.enterprisedb.com/postgresql/postgresql-15.4-1-windows-x64-binaries.zip"
        self.download_filename = "postgresql-portable.zip"
        
    def create_directory_structure(self):
        """创建目录结构"""
        directories = [
            self.postgresql_dir,
            self.postgresql_dir / "data",
            self.postgresql_dir / "logs",
            self.postgresql_dir / "bin",
            self.backup_dir,
            self.base_dir / "migration_logs"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
    
    def download_postgresql(self) -> bool:
        """下载便携式PostgreSQL"""
        download_path = self.postgresql_dir / self.download_filename
        
        if download_path.exists():
            print(f"📦 PostgreSQL文件已存在: {download_path}")
            return True
        
        try:
            print(f"🔄 开始下载PostgreSQL {self.postgresql_version}...")
            print(f"📥 下载地址: {self.download_url}")
            
            # 创建下载进度回调
            def progress_callback(block_num, block_size, total_size):
                downloaded = block_num * block_size
                if total_size > 0:
                    percent = min(100, (downloaded * 100) // total_size)
                    print(f"\r📊 下载进度: {percent}% ({downloaded // (1024*1024)}MB / {total_size // (1024*1024)}MB)", end="")
            
            urllib.request.urlretrieve(self.download_url, download_path, progress_callback)
            print(f"\n✅ PostgreSQL下载完成: {download_path}")
            return True
            
        except Exception as e:
            print(f"❌ PostgreSQL下载失败: {e}")
            return False
    
    def extract_postgresql(self) -> bool:
        """解压PostgreSQL"""
        download_path = self.postgresql_dir / self.download_filename
        extract_path = self.postgresql_dir / "pgsql"
        
        if extract_path.exists():
            print(f"📂 PostgreSQL已解压: {extract_path}")
            return True
        
        try:
            print("🔄 开始解压PostgreSQL...")
            with zipfile.ZipFile(download_path, 'r') as zip_ref:
                zip_ref.extractall(self.postgresql_dir)
            
            # 重命名解压后的目录
            extracted_dirs = [d for d in self.postgresql_dir.iterdir() if d.is_dir() and d.name.startswith('pgsql')]
            if extracted_dirs:
                if extracted_dirs[0] != extract_path:
                    extracted_dirs[0].rename(extract_path)
            
            print(f"✅ PostgreSQL解压完成: {extract_path}")
            return True
            
        except Exception as e:
            print(f"❌ PostgreSQL解压失败: {e}")
            return False
    
    def initialize_database(self) -> bool:
        """初始化PostgreSQL数据库"""
        pgsql_dir = self.postgresql_dir / "pgsql"
        data_dir = self.postgresql_dir / "data"
        initdb_exe = pgsql_dir / "bin" / "initdb.exe"
        
        if not initdb_exe.exists():
            print(f"❌ 找不到initdb.exe: {initdb_exe}")
            return False
        
        if (data_dir / "postgresql.conf").exists():
            print(f"📂 数据库已初始化: {data_dir}")
            return True
        
        try:
            print("🔄 开始初始化PostgreSQL数据库...")
            
            # 初始化数据库
            cmd = [
                str(initdb_exe),
                "-D", str(data_dir),
                "-U", "postgres",
                "--auth-local=trust",
                "--auth-host=md5",
                "--encoding=UTF8",
                "--locale=C"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(pgsql_dir))
            
            if result.returncode == 0:
                print("✅ PostgreSQL数据库初始化成功")
                return True
            else:
                print(f"❌ 数据库初始化失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 数据库初始化异常: {e}")
            return False
    
    def configure_postgresql(self) -> bool:
        """配置PostgreSQL"""
        data_dir = self.postgresql_dir / "data"
        postgresql_conf = data_dir / "postgresql.conf"
        pg_hba_conf = data_dir / "pg_hba.conf"
        
        try:
            print("🔄 配置PostgreSQL...")
            
            # 配置postgresql.conf
            if postgresql_conf.exists():
                with open(postgresql_conf, 'r', encoding='utf-8') as f:
                    config = f.read()
                
                # 修改配置
                config_changes = {
                    "#port = 5432": "port = 5432",
                    "#listen_addresses = 'localhost'": "listen_addresses = 'localhost'",
                    "#log_destination = 'stderr'": "log_destination = 'stderr'",
                    "#logging_collector = off": "logging_collector = on",
                    "#log_directory = 'log'": f"log_directory = '{self.postgresql_dir / 'logs'}'",
                    "#log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'": "log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'"
                }
                
                for old, new in config_changes.items():
                    config = config.replace(old, new)
                
                with open(postgresql_conf, 'w', encoding='utf-8') as f:
                    f.write(config)
            
            # 配置pg_hba.conf（允许本地连接）
            if pg_hba_conf.exists():
                with open(pg_hba_conf, 'a', encoding='utf-8') as f:
                    f.write("\n# 允许本地应用连接\n")
                    f.write("host    all             all             127.0.0.1/32            trust\n")
                    f.write("host    all             all             ::1/128                 trust\n")
            
            print("✅ PostgreSQL配置完成")
            return True
            
        except Exception as e:
            print(f"❌ PostgreSQL配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置信息"""
        config = {
            "version": self.postgresql_version,
            "install_path": str(self.postgresql_dir),
            "data_path": str(self.postgresql_dir / "data"),
            "bin_path": str(self.postgresql_dir / "pgsql" / "bin"),
            "log_path": str(self.postgresql_dir / "logs"),
            "backup_path": str(self.backup_dir),
            "port": 5432,
            "host": "localhost",
            "database": "postgres",
            "user": "postgres",
            "password": "",
            "installed": True,
            "install_date": str(datetime.now())
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 配置文件已保存: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False
    
    def install(self) -> bool:
        """执行完整安装流程"""
        print("🚀 开始安装便携式PostgreSQL...")
        print(f"📁 安装目录: {self.postgresql_dir}")
        
        steps = [
            ("创建目录结构", self.create_directory_structure),
            ("下载PostgreSQL", self.download_postgresql),
            ("解压PostgreSQL", self.extract_postgresql),
            ("初始化数据库", self.initialize_database),
            ("配置PostgreSQL", self.configure_postgresql),
            ("保存配置", self.save_config)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 步骤: {step_name}")
            if not step_func():
                print(f"❌ 安装失败于步骤: {step_name}")
                return False
        
        print("\n🎉 便携式PostgreSQL安装完成！")
        print(f"📁 安装位置: {self.postgresql_dir}")
        print(f"💾 数据目录: {self.postgresql_dir / 'data'}")
        print(f"📋 配置文件: {self.config_file}")
        
        return True

def main():
    """主函数"""
    installer = PostgreSQLPortableInstaller()
    
    if installer.install():
        print("\n✅ 安装成功！")
        print("💡 提示：请运行 postgresql_manager.py 来启动和管理PostgreSQL服务")
        return 0
    else:
        print("\n❌ 安装失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
