# -*- coding: utf-8 -*-
"""
修复脚本路径关联工具
检查并修复应用中所有脚本的路径关联问题
"""

import os
import shutil
import configparser
from pathlib import Path

def main():
    """主函数"""
    print("🔧 开始检查和修复脚本路径关联...")
    
    # 获取重构版根目录
    base_dir = Path(__file__).parent.parent
    print(f"📁 重构版根目录: {base_dir}")
    
    # 定义目录结构
    dirs = {
        "主程序": base_dir / "01_主程序",
        "配置文件": base_dir / "03_配置文件", 
        "数据库工具": base_dir / "06_数据库工具",
        "scripts": base_dir / "scripts",
        "整理前备份": base_dir / "整理前备份",
        "数据处理系统": base_dir.parent / "数据处理系统"
    }
    
    # 检查缺失的脚本文件
    missing_scripts = []
    
    # 1. 检查 report 脚本 3.0.py
    script_3_0_path = dirs["主程序"] / "report 脚本 3.0.py"
    if not script_3_0_path.exists():
        # 在数据处理系统目录中查找
        source_path = dirs["数据处理系统"] / "report 脚本 3.0.py"
        if source_path.exists():
            print(f"📋 找到 report 脚本 3.0.py，正在复制到主程序目录...")
            shutil.copy2(source_path, script_3_0_path)
            print(f"✅ 已复制: {script_3_0_path}")
        else:
            missing_scripts.append("report 脚本 3.0.py")
            print(f"❌ 未找到: report 脚本 3.0.py")
    else:
        print(f"✅ 已存在: report 脚本 3.0.py")
    
    # 2. 检查数据导入脚本.py
    data_import_path = dirs["主程序"] / "数据导入脚本.py"
    if not data_import_path.exists():
        # 在整理前备份中查找
        source_path = dirs["整理前备份"] / "数据导入脚本.py"
        if source_path.exists():
            print(f"📋 找到 数据导入脚本.py，正在复制到主程序目录...")
            shutil.copy2(source_path, data_import_path)
            print(f"✅ 已复制: {data_import_path}")
        else:
            # 在数据库工具目录中查找
            source_path = dirs["数据库工具"] / "数据导入脚本.py"
            if source_path.exists():
                print(f"📋 找到 数据导入脚本.py，正在复制到主程序目录...")
                shutil.copy2(source_path, data_import_path)
                print(f"✅ 已复制: {data_import_path}")
            else:
                missing_scripts.append("数据导入脚本.py")
                print(f"❌ 未找到: 数据导入脚本.py")
    else:
        print(f"✅ 已存在: 数据导入脚本.py")
    
    # 3. 检查其他必要脚本
    required_scripts = [
        "report 模块化设计 7.0.py",
        "Refund_process_修复版.py"
    ]
    
    for script_name in required_scripts:
        script_path = dirs["主程序"] / script_name
        if script_path.exists():
            print(f"✅ 已存在: {script_name}")
        else:
            missing_scripts.append(script_name)
            print(f"❌ 缺失: {script_name}")
    
    # 4. 检查优化版脚本
    optimized_scripts = [
        "data_import_optimized.py",
        "refund_process_optimized.py",
        "dual_database_import.py",
        "dual_database_refund.py"
    ]
    
    for script_name in optimized_scripts:
        script_path = dirs["scripts"] / script_name
        if script_path.exists():
            print(f"✅ 已存在: scripts/{script_name}")
        else:
            missing_scripts.append(f"scripts/{script_name}")
            print(f"❌ 缺失: scripts/{script_name}")
    
    # 5. 检查配置文件
    config_path = dirs["配置文件"] / "config.ini"
    if config_path.exists():
        print(f"✅ 配置文件存在: {config_path}")
        
        # 读取并检查配置
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        print("\n📋 检查配置文件中的脚本路径:")
        if config.has_section('Scripts'):
            for key, value in config['Scripts'].items():
                script_path = dirs["主程序"] / value if not value.startswith('scripts/') else dirs["scripts"] / value.replace('scripts/', '')
                if script_path.exists():
                    print(f"✅ {key}: {value}")
                else:
                    print(f"❌ {key}: {value} (文件不存在)")
        else:
            print("❌ 配置文件中缺少 Scripts 节")
    else:
        print(f"❌ 配置文件不存在: {config_path}")
    
    # 6. 生成修复报告
    print("\n" + "="*60)
    print("📊 脚本路径关联检查报告")
    print("="*60)
    
    if missing_scripts:
        print(f"\n❌ 发现 {len(missing_scripts)} 个缺失的脚本:")
        for script in missing_scripts:
            print(f"   - {script}")
        
        print("\n🔧 修复建议:")
        print("1. 检查这些脚本是否在其他目录中")
        print("2. 如果找到，请复制到正确的位置")
        print("3. 如果确实缺失，请从备份中恢复或重新创建")
        
        return False
    else:
        print("\n✅ 所有脚本路径关联正常!")
        print("应用可以正常运行")
        return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 脚本路径关联检查完成，一切正常!")
    else:
        print("\n⚠️ 发现问题，请根据建议进行修复")
    
    input("\n按回车键退出...")
