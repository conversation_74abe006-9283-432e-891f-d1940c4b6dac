# -*- coding: utf-8 -*-
"""
文件整理脚本
将项目文件按功能分类并使用中文命名
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class FileOrganizer:
    """文件整理器"""
    
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.backup_dir = self.base_dir / "整理前备份"
        
        # 定义文件分类规则
        self.file_categories = {
            "01_主程序": {
                "files": [
                    "数据处理与导入应用_完整版.py",
                    "report 模块化设计 7.0.py",
                    "Refund_process_修复版.py"
                ],
                "description": "主要应用程序文件"
            },
            "02_PostgreSQL管理": {
                "files": [
                    "postgresql_setup.py",
                    "postgresql_manager.py", 
                    "intelligent_migration.py",
                    "migration_ui.py",
                    "migration_config.json"
                ],
                "description": "PostgreSQL安装、管理和迁移工具"
            },
            "03_配置文件": {
                "files": [
                    "config.ini"
                ],
                "description": "系统配置文件"
            },
            "04_测试文件": {
                "files": [
                    "test_*.py",
                    "simple_*.py", 
                    "comprehensive_*.py",
                    "check_*.py",
                    "创建测试Excel.py",
                    "完整代码逻辑检查.py",
                    "快速Unicode测试.py",
                    "智能状态检测演示.py",
                    "检查数据库.py",
                    "测试*.py",
                    "表导入验证脚本.py",
                    "*.xlsx"
                ],
                "description": "各种测试和验证脚本"
            },
            "05_迁移工具": {
                "files": [
                    "*migration*.py",
                    "database_migration_analyzer.py",
                    "direct_migration_executor.py",
                    "fixed_migration.py",
                    "interactive_migration.py",
                    "postgresql_migrator.py",
                    "postgresql_precheck.py"
                ],
                "description": "数据迁移相关工具"
            },
            "06_数据库工具": {
                "files": [
                    "create_*.py",
                    "fix_*.py",
                    "recreate_*.py",
                    "数据导入脚本.py"
                ],
                "description": "数据库创建和修复工具"
            },
            "07_文档说明": {
                "files": [
                    "*.md",
                    "*.txt",
                    "verification_report_*.txt"
                ],
                "description": "文档和说明文件"
            },
            "08_备份文件": {
                "files": [
                    "*.sql",
                    "*.db",
                    "*backup*",
                    "config_before_*.ini"
                ],
                "description": "数据库备份文件"
            },
            "09_临时文件": {
                "files": [
                    "*.json",
                    "complete_*.py"
                ],
                "description": "临时和分析文件"
            }
        }
    
    def create_backup(self):
        """创建整理前的备份"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        self.backup_dir.mkdir(exist_ok=True)
        
        print(f"📦 创建整理前备份到: {self.backup_dir}")
        
        # 备份所有文件（除了目录）
        for item in self.base_dir.iterdir():
            if item.is_file() and item.name != "文件整理脚本.py":
                try:
                    shutil.copy2(item, self.backup_dir / item.name)
                except Exception as e:
                    print(f"⚠️ 备份文件失败 {item.name}: {e}")
    
    def create_directories(self):
        """创建分类目录"""
        print("📁 创建分类目录...")
        
        for category, info in self.file_categories.items():
            category_dir = self.base_dir / category
            category_dir.mkdir(exist_ok=True)
            
            # 创建说明文件
            readme_file = category_dir / "说明.txt"
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(f"{category}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"描述: {info['description']}\n\n")
                f.write("包含文件:\n")
                for pattern in info['files']:
                    f.write(f"- {pattern}\n")
                f.write(f"\n整理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            print(f"✅ 创建目录: {category}")
    
    def match_pattern(self, filename: str, pattern: str) -> bool:
        """匹配文件名模式"""
        if '*' in pattern:
            if pattern.startswith('*') and pattern.endswith('*'):
                # *xxx*
                middle = pattern[1:-1]
                return middle in filename
            elif pattern.startswith('*'):
                # *xxx
                suffix = pattern[1:]
                return filename.endswith(suffix)
            elif pattern.endswith('*'):
                # xxx*
                prefix = pattern[:-1]
                return filename.startswith(prefix)
        else:
            # 精确匹配
            return filename == pattern
        
        return False
    
    def organize_files(self):
        """整理文件"""
        print("📋 开始整理文件...")
        
        moved_files = []
        
        for item in self.base_dir.iterdir():
            if item.is_file() and item.name not in ["文件整理脚本.py", "整理报告.txt"]:
                moved = False
                
                # 查找匹配的分类
                for category, info in self.file_categories.items():
                    for pattern in info['files']:
                        if self.match_pattern(item.name, pattern):
                            target_dir = self.base_dir / category
                            target_file = target_dir / item.name
                            
                            try:
                                shutil.move(str(item), str(target_file))
                                moved_files.append((item.name, category))
                                print(f"📁 {item.name} → {category}")
                                moved = True
                                break
                            except Exception as e:
                                print(f"❌ 移动文件失败 {item.name}: {e}")
                    
                    if moved:
                        break
                
                if not moved:
                    print(f"⚠️ 未分类文件: {item.name}")
        
        return moved_files
    
    def clean_empty_directories(self):
        """清理空目录"""
        print("🧹 清理空目录...")
        
        for item in self.base_dir.iterdir():
            if item.is_dir() and item.name not in ["整理前备份", "__pycache__"]:
                try:
                    # 检查是否为空目录（除了说明.txt）
                    contents = list(item.iterdir())
                    if len(contents) <= 1 and (len(contents) == 0 or contents[0].name == "说明.txt"):
                        print(f"🗑️ 删除空目录: {item.name}")
                        shutil.rmtree(item)
                except Exception as e:
                    print(f"⚠️ 清理目录失败 {item.name}: {e}")
    
    def generate_report(self, moved_files):
        """生成整理报告"""
        report_file = self.base_dir / "整理报告.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("文件整理报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"整理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"整理文件数量: {len(moved_files)}\n\n")
            
            # 按分类统计
            category_stats = {}
            for filename, category in moved_files:
                if category not in category_stats:
                    category_stats[category] = []
                category_stats[category].append(filename)
            
            f.write("分类统计:\n")
            f.write("-" * 30 + "\n")
            for category, files in category_stats.items():
                f.write(f"\n{category} ({len(files)} 个文件):\n")
                for filename in sorted(files):
                    f.write(f"  - {filename}\n")
            
            f.write("\n目录结构:\n")
            f.write("-" * 30 + "\n")
            for category, info in self.file_categories.items():
                category_dir = self.base_dir / category
                if category_dir.exists():
                    file_count = len([f for f in category_dir.iterdir() if f.is_file() and f.name != "说明.txt"])
                    f.write(f"{category}: {file_count} 个文件\n")
        
        print(f"📋 整理报告已生成: {report_file}")
    
    def organize(self):
        """执行完整的整理流程"""
        print("🚀 开始文件整理...")
        print("=" * 60)
        
        # 1. 创建备份
        self.create_backup()
        
        # 2. 创建分类目录
        self.create_directories()
        
        # 3. 整理文件
        moved_files = self.organize_files()
        
        # 4. 清理空目录
        self.clean_empty_directories()
        
        # 5. 生成报告
        self.generate_report(moved_files)
        
        print("\n" + "=" * 60)
        print("🎉 文件整理完成！")
        print(f"📦 备份位置: {self.backup_dir}")
        print(f"📋 整理了 {len(moved_files)} 个文件")
        print("📁 新的目录结构:")
        
        for category in self.file_categories.keys():
            category_dir = self.base_dir / category
            if category_dir.exists():
                file_count = len([f for f in category_dir.iterdir() if f.is_file() and f.name != "说明.txt"])
                print(f"  {category}: {file_count} 个文件")

def main():
    """主函数"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    organizer = FileOrganizer(base_dir)
    
    # 确认执行
    response = input("确认开始文件整理？这将移动和重新组织所有文件。(y/N): ")
    if response.lower() != 'y':
        print("❌ 整理已取消")
        return
    
    organizer.organize()

if __name__ == "__main__":
    main()
