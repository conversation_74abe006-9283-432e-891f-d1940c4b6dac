# -*- coding: utf-8 -*-
"""
表导入验证脚本
验证数据是否被正确导入到对应的表中
"""

import sys
import os
import pandas as pd
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection_pool import get_connection, reinitialize_connection_pool
from database.models import SMART_STATUS_PATTERNS, STATUS_TABLE_MAPPING
from scripts.data_import_optimized import DataImportProcessor


class TableImportValidator:
    """表导入验证器"""
    
    def __init__(self, db_path: str = None):
        """初始化验证器"""
        self.db_path = db_path or os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
        
        # 确保连接池使用正确的数据库路径
        reinitialize_connection_pool(self.db_path)
        
        # 创建数据导入处理器
        self.processor = DataImportProcessor(self.db_path)
        
        print(f"📊 表导入验证器已初始化")
        print(f"数据库路径: {self.db_path}")
    
    def create_test_data(self) -> pd.DataFrame:
        """创建测试数据"""
        test_data = [
            # 正常完成状态 - 应该导入到主表
            {"Order_No": "TEST001", "Order_status": "Finished", "Equipment_ID": "E001", "Order_price": "100.00", "Order_time": "2024-12-18 10:00:00"},
            {"Order_No": "TEST002", "Order_status": "Complete", "Equipment_ID": "E002", "Order_price": "200.00", "Order_time": "2024-12-18 10:01:00"},
            {"Order_No": "TEST003", "Order_status": "Payment Successful", "Equipment_ID": "E003", "Order_price": "300.00", "Order_time": "2024-12-18 10:02:00"},
            {"Order_No": "TEST004", "Order_status": "已完成", "Equipment_ID": "E004", "Order_price": "400.00", "Order_time": "2024-12-18 10:03:00"},
            {"Order_No": "TEST005", "Order_status": "支付成功", "Equipment_ID": "E005", "Order_price": "500.00", "Order_time": "2024-12-18 10:04:00"},
            
            # 退款状态 - 应该导入到Refunding表
            {"Order_No": "TEST006", "Order_status": "Refunded", "Equipment_ID": "E006", "Order_price": "600.00", "Order_time": "2024-12-18 10:05:00"},
            {"Order_No": "TEST007", "Order_status": "Refund Processing", "Equipment_ID": "E007", "Order_price": "700.00", "Order_time": "2024-12-18 10:06:00"},
            {"Order_No": "TEST008", "Order_status": "Cancelled by User", "Equipment_ID": "E008", "Order_price": "800.00", "Order_time": "2024-12-18 10:07:00"},
            {"Order_No": "TEST009", "Order_status": "退款中", "Equipment_ID": "E009", "Order_price": "900.00", "Order_time": "2024-12-18 10:08:00"},
            {"Order_No": "TEST010", "Order_status": "用户取消", "Equipment_ID": "E010", "Order_price": "1000.00", "Order_time": "2024-12-18 10:09:00"},
            
            # 关闭状态 - 应该导入到Close表
            {"Order_No": "TEST011", "Order_status": "Closed", "Equipment_ID": "E011", "Order_price": "1100.00", "Order_time": "2024-12-18 10:10:00"},
            {"Order_No": "TEST012", "Order_status": "Order Closed", "Equipment_ID": "E012", "Order_price": "1200.00", "Order_time": "2024-12-18 10:11:00"},
            {"Order_No": "TEST013", "Order_status": "System Ended", "Equipment_ID": "E013", "Order_price": "1300.00", "Order_time": "2024-12-18 10:12:00"},
            {"Order_No": "TEST014", "Order_status": "订单关闭", "Equipment_ID": "E014", "Order_price": "1400.00", "Order_time": "2024-12-18 10:13:00"},
            {"Order_No": "TEST015", "Order_status": "系统结束", "Equipment_ID": "E015", "Order_price": "1500.00", "Order_time": "2024-12-18 10:14:00"},
            
            # 未知状态 - 应该导入到主表
            {"Order_No": "TEST016", "Order_status": "Unknown Status", "Equipment_ID": "E016", "Order_price": "1600.00", "Order_time": "2024-12-18 10:15:00"},
            {"Order_No": "TEST017", "Order_status": "Processing", "Equipment_ID": "E017", "Order_price": "1700.00", "Order_time": "2024-12-18 10:16:00"},
            
            # 空状态 - 应该导入到主表
            {"Order_No": "TEST018", "Order_status": "", "Equipment_ID": "E018", "Order_price": "1800.00", "Order_time": "2024-12-18 10:17:00"},
        ]
        
        df = pd.DataFrame(test_data)
        df['Import_Date'] = datetime.now().strftime("%Y-%m-%d")
        
        return df
    
    def test_status_detection(self):
        """测试状态检测逻辑"""
        print("\n🔍 测试状态检测逻辑")
        print("=" * 60)
        
        test_cases = [
            # (状态, 期望的表后缀)
            ("Finished", ""),
            ("Complete", ""),
            ("Payment Successful", ""),
            ("已完成", ""),
            ("支付成功", ""),
            
            ("Refunded", "_Refunding"),
            ("Refund Processing", "_Refunding"),
            ("Cancelled by User", "_Refunding"),
            ("退款中", "_Refunding"),
            ("用户取消", "_Refunding"),
            
            ("Closed", "_Close"),
            ("Order Closed", "_Close"),
            ("System Ended", "_Close"),
            ("订单关闭", "_Close"),
            ("系统结束", "_Close"),
            
            ("Unknown Status", ""),
            ("Processing", ""),
            ("", ""),
        ]
        
        platform = "IOT"
        correct_detections = 0
        total_tests = len(test_cases)
        
        for status, expected_suffix in test_cases:
            detected_table = self.processor._determine_target_table(platform, status)
            expected_table = f"{platform}_Sales{expected_suffix}"
            
            if detected_table == expected_table:
                result = "✅"
                correct_detections += 1
            else:
                result = "❌"
            
            print(f"{result} 状态: '{status}' → 检测: {detected_table} | 期望: {expected_table}")
        
        accuracy = (correct_detections / total_tests) * 100
        print(f"\n📊 状态检测准确率: {accuracy:.1f}% ({correct_detections}/{total_tests})")
        
        return accuracy >= 95  # 期望准确率至少95%
    
    def clear_test_data(self, platform: str):
        """清理测试数据"""
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()
                
                tables = [
                    f"{platform}_Sales",
                    f"{platform}_Sales_Refunding", 
                    f"{platform}_Sales_Close"
                ]
                
                for table_name in tables:
                    try:
                        cursor.execute(f"DELETE FROM {table_name} WHERE Order_No LIKE 'TEST%'")
                        deleted_count = cursor.rowcount
                        if deleted_count > 0:
                            print(f"🧹 清理表 {table_name}: 删除 {deleted_count} 条测试数据")
                    except Exception as e:
                        print(f"⚠️ 清理表 {table_name} 失败: {e}")
                
                conn.connection.commit()
                print("✅ 测试数据清理完成")
                
        except Exception as e:
            print(f"❌ 清理测试数据失败: {e}")
    
    def verify_table_data(self, platform: str) -> bool:
        """验证表中的数据分布"""
        print(f"\n📋 验证 {platform} 平台表中的数据分布")
        print("=" * 60)
        
        try:
            with get_connection() as conn:
                tables_info = [
                    (f"{platform}_Sales", "主表", ["Finished", "Complete", "Payment Successful", "已完成", "支付成功", "Unknown Status", "Processing", ""]),
                    (f"{platform}_Sales_Refunding", "退款表", ["Refunded", "Refund Processing", "Cancelled by User", "退款中", "用户取消"]),
                    (f"{platform}_Sales_Close", "关闭表", ["Closed", "Order Closed", "System Ended", "订单关闭", "系统结束"])
                ]
                
                all_correct = True
                total_records = 0
                
                for table_name, table_desc, expected_statuses in tables_info:
                    try:
                        # 查询表中的测试数据
                        df = pd.read_sql(f"""
                            SELECT Order_No, Order_status, Equipment_ID, Order_price 
                            FROM {table_name} 
                            WHERE Order_No LIKE 'TEST%'
                            ORDER BY Order_No
                        """, conn.connection)
                        
                        record_count = len(df)
                        total_records += record_count
                        
                        print(f"\n📊 {table_desc} ({table_name}): {record_count} 条记录")
                        
                        if record_count > 0:
                            # 验证状态是否正确
                            correct_statuses = 0
                            for _, row in df.iterrows():
                                order_status = row['Order_status']
                                if order_status in expected_statuses:
                                    correct_statuses += 1
                                    status_icon = "✅"
                                else:
                                    status_icon = "❌"
                                    all_correct = False
                                
                                print(f"  {status_icon} {row['Order_No']}: '{order_status}' | 价格: {row['Order_price']}")
                            
                            status_accuracy = (correct_statuses / record_count) * 100 if record_count > 0 else 0
                            print(f"  📈 状态正确率: {status_accuracy:.1f}% ({correct_statuses}/{record_count})")
                        
                    except Exception as e:
                        print(f"❌ 查询表 {table_name} 失败: {e}")
                        all_correct = False
                
                print(f"\n📊 总计验证记录: {total_records} 条")
                return all_correct
                
        except Exception as e:
            print(f"❌ 验证表数据失败: {e}")
            return False
    
    def run_full_test(self, platform: str = "IOT"):
        """运行完整的导入验证测试"""
        print(f"🚀 开始完整的表导入验证测试")
        print(f"平台: {platform}")
        print("=" * 80)
        
        try:
            # 1. 清理现有测试数据
            print("🧹 第1步: 清理现有测试数据")
            self.clear_test_data(platform)
            
            # 2. 测试状态检测逻辑
            print("\n🔍 第2步: 测试状态检测逻辑")
            detection_passed = self.test_status_detection()
            
            # 3. 创建测试数据
            print("\n📝 第3步: 创建测试数据")
            test_df = self.create_test_data()
            print(f"创建了 {len(test_df)} 条测试数据")
            
            # 4. 执行智能导入
            print("\n💾 第4步: 执行智能导入")
            insert_results = self.processor.smart_insert_data(test_df, platform)
            
            print("插入结果:")
            total_inserted = 0
            for table_name, count in insert_results.items():
                print(f"  • {table_name}: {count} 条记录")
                total_inserted += count
            print(f"总计插入: {total_inserted} 条记录")
            
            # 5. 验证数据分布
            print("\n✅ 第5步: 验证数据分布")
            distribution_correct = self.verify_table_data(platform)
            
            # 6. 最终结果
            print("\n🎯 测试结果总结")
            print("=" * 60)
            print(f"状态检测准确性: {'✅ 通过' if detection_passed else '❌ 失败'}")
            print(f"数据分布正确性: {'✅ 通过' if distribution_correct else '❌ 失败'}")
            print(f"总体测试结果: {'✅ 全部通过' if detection_passed and distribution_correct else '❌ 存在问题'}")
            
            # 7. 清理测试数据
            print("\n🧹 第6步: 清理测试数据")
            self.clear_test_data(platform)
            
            return detection_passed and distribution_correct
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    print("🔍 表导入验证脚本")
    print("=" * 80)
    
    # 创建验证器
    validator = TableImportValidator()
    
    # 测试所有平台
    platforms = ["IOT", "ZERO", "APP"]
    all_passed = True
    
    for platform in platforms:
        print(f"\n🏷️ 测试平台: {platform}")
        print("-" * 40)
        
        platform_passed = validator.run_full_test(platform)
        if not platform_passed:
            all_passed = False
        
        print(f"平台 {platform} 测试结果: {'✅ 通过' if platform_passed else '❌ 失败'}")
    
    print(f"\n🎉 所有平台测试完成")
    print(f"最终结果: {'✅ 全部通过' if all_passed else '❌ 存在问题'}")
    
    if all_passed:
        print("\n✨ 恭喜！表导入逻辑完全正确！")
        print("数据会根据Order_status正确导入到对应的表中：")
        print("• 完成状态 → Platform_Sales")
        print("• 退款状态 → Platform_Sales_Refunding") 
        print("• 关闭状态 → Platform_Sales_Close")
    else:
        print("\n⚠️ 发现问题，请检查导入逻辑！")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
