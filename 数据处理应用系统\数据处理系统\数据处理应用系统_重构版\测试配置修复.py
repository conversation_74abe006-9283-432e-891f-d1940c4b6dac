# -*- coding: utf-8 -*-
"""
测试配置修复
验证配置文件和路径问题是否已解决
"""

import os
import sys
import configparser

def test_config_file():
    """测试配置文件"""
    
    print("🔍 测试配置文件修复...")
    print("=" * 60)
    
    config_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\config.ini"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    print(f"✅ 配置文件存在: {config_path}")
    
    try:
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        print("\n📋 检查配置内容:")
        
        # 检查Scripts段
        if 'Scripts' in config:
            scripts = config['Scripts']
            
            print("  [Scrip<PERSON>]")
            for key, value in scripts.items():
                print(f"    {key} = {value}")
                
                # 特别检查data_import_script_optimized
                if key == 'data_import_script_optimized':
                    script_path = os.path.join(
                        os.path.dirname(config_path),
                        value
                    )
                    if os.path.exists(script_path):
                        print(f"      ✅ 脚本文件存在: {script_path}")
                    else:
                        print(f"      ❌ 脚本文件不存在: {script_path}")
                        return False
        else:
            print("  ❌ Scripts段不存在")
            return False
        
        print("\n✅ 配置文件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def test_backup_path_handling():
    """测试备份路径处理"""
    
    print("\n🔍 测试备份路径处理...")
    print("=" * 60)
    
    # 模拟路径问题
    test_paths = [
        r"C:/Users/<USER>/Desktop/Day Report/database\backups\sqlite_backup_20250620_172737.db",
        r"C:\Users\<USER>\Desktop\Day Report\database\backups\sqlite_backup_20250620_172737.db",
        "sqlite_backup_20250620_172737.db"
    ]
    
    backup_dir = r"C:\Users\<USER>\Desktop\Day Report\database\backups"
    
    print("📋 测试路径规范化:")
    
    for test_path in test_paths:
        print(f"\n  原始路径: {test_path}")
        
        # 模拟修复后的逻辑
        if os.path.isabs(test_path):
            normalized_path = os.path.normpath(test_path)
        else:
            normalized_path = os.path.normpath(os.path.join(backup_dir, test_path))
        
        print(f"  规范化后: {normalized_path}")
        
        # 检查路径是否存在
        if os.path.exists(normalized_path):
            print(f"  ✅ 路径存在")
        else:
            print(f"  ⚠️ 路径不存在（这是正常的，因为是测试路径）")
    
    print("\n✅ 备份路径处理测试完成")
    return True

def test_main_app_config():
    """测试主程序配置"""
    
    print("\n🔍 测试主程序配置...")
    print("=" * 60)
    
    main_app_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据处理与导入应用_完整版.py"
    
    if not os.path.exists(main_app_path):
        print(f"❌ 主程序文件不存在: {main_app_path}")
        return False
    
    print(f"✅ 主程序文件存在")
    
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n📋 检查关键修复:")
        
        # 检查硬编码配置修复
        if "'data_import_script_optimized': '数据导入脚本.py'" in content:
            print("  ✅ 硬编码配置已修复")
        else:
            print("  ❌ 硬编码配置未修复")
            return False
        
        # 检查路径规范化
        if "os.path.normpath" in content:
            print("  ✅ 路径规范化已添加")
        else:
            print("  ❌ 路径规范化未添加")
            return False
        
        # 检查日志控件修复
        if 'self.gui_updater.safe_log(error_msg, "import")' in content:
            print("  ✅ 日志控件已修复")
        else:
            print("  ❌ 日志控件未修复")
            return False
        
        print("\n✅ 主程序配置检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 主程序配置检查失败: {e}")
        return False

def create_test_import_command():
    """创建测试导入命令"""
    
    print("\n🔧 创建测试导入命令...")
    print("=" * 60)
    
    # 构建测试命令
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据导入脚本.py"
    test_file = r"160625 CHINA ZERO.xlsx"  # 假设的测试文件
    db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    
    cmd = [
        sys.executable,
        script_path,
        '--file', test_file,
        '--platform', 'ZERO',
        '--db_path', db_path
    ]
    
    print("📋 测试命令:")
    print(f"  {' '.join(cmd)}")
    
    print("\n📋 命令参数说明:")
    print(f"  Python解释器: {sys.executable}")
    print(f"  导入脚本: {script_path}")
    print(f"  测试文件: {test_file}")
    print(f"  平台类型: ZERO")
    print(f"  数据库路径: {db_path}")
    
    # 检查脚本是否存在
    if os.path.exists(script_path):
        print(f"\n  ✅ 导入脚本存在")
    else:
        print(f"\n  ❌ 导入脚本不存在")
        return False
    
    print("\n✅ 测试导入命令创建完成")
    return True

def main():
    """主函数"""
    
    print("🚀 配置修复测试")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("配置文件", test_config_file),
        ("备份路径处理", test_backup_path_handling),
        ("主程序配置", test_main_app_config),
        ("测试导入命令", create_test_import_command)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试时出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📋 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 配置修复测试完全通过！")
        print("\n📋 修复内容:")
        print("  ✅ 配置文件中的脚本路径已修复")
        print("  ✅ 主程序中的硬编码配置已修复")
        print("  ✅ 备份路径规范化已添加")
        print("  ✅ 日志控件问题已修复")
        
        print("\n🔧 建议操作:")
        print("  1. 重新启动主程序以清除配置缓存")
        print("  2. 重新尝试导入操作")
        print("  3. 如果仍有问题，检查文件路径是否正确")
        
    else:
        print(f"\n⚠️ 配置修复测试部分失败！")
        print(f"  需要修复 {total - passed} 个问题")
    
    return passed == total

if __name__ == "__main__":
    result = main()
    input(f"\n按回车键退出... (测试{'成功' if result else '失败'})")
