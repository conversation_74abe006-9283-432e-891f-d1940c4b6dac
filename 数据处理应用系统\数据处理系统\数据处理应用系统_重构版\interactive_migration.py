# -*- coding: utf-8 -*-
"""
交互式PostgreSQL迁移脚本
引导用户输入正确的连接信息并执行迁移
"""

import os
import sys
import shutil
import sqlite3
import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InteractiveMigration:
    """交互式迁移器"""
    
    def __init__(self):
        self.pg_config = {}
        self.original_db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        self.backup_db_path = None
        self.migration_log = []
    
    def run_migration(self):
        """运行交互式迁移"""
        print("🚀 SQLite到PostgreSQL交互式迁移")
        print("="*60)
        
        # 步骤1: 检查SQLite数据库
        if not self._check_sqlite_database():
            return False
        
        # 步骤2: 获取PostgreSQL配置
        if not self._get_postgresql_config():
            return False
        
        # 步骤3: 测试PostgreSQL连接
        if not self._test_postgresql_connection():
            return False
        
        # 步骤4: 确认开始迁移
        if not self._confirm_migration():
            return False
        
        # 步骤5: 执行迁移
        return self._execute_migration()
    
    def _check_sqlite_database(self) -> bool:
        """检查SQLite数据库"""
        print("\n📋 步骤1: 检查SQLite数据库")
        print("-" * 40)
        
        if not os.path.exists(self.original_db_path):
            print(f"❌ SQLite数据库不存在: {self.original_db_path}")
            
            # 让用户输入正确的路径
            while True:
                new_path = input("请输入SQLite数据库的完整路径: ").strip()
                if os.path.exists(new_path):
                    self.original_db_path = new_path
                    break
                else:
                    print(f"❌ 文件不存在: {new_path}")
                    retry = input("是否重新输入? (y/N): ").strip().lower()
                    if retry not in ['y', 'yes', '是']:
                        return False
        
        # 检查数据库大小和表数量
        try:
            file_size = os.path.getsize(self.original_db_path)
            conn = sqlite3.connect(self.original_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            conn.close()
            
            print(f"✅ SQLite数据库检查通过")
            print(f"   路径: {self.original_db_path}")
            print(f"   大小: {file_size:,} 字节")
            print(f"   表数量: {table_count}")
            return True
            
        except Exception as e:
            print(f"❌ 检查SQLite数据库失败: {e}")
            return False
    
    def _get_postgresql_config(self) -> bool:
        """获取PostgreSQL配置"""
        print("\n🔧 步骤2: 配置PostgreSQL连接")
        print("-" * 40)
        
        print("请输入PostgreSQL数据库连接信息:")
        
        # 使用默认值
        default_config = {
            'host': 'localhost',
            'port': '5432',
            'database': 'sales_reports',
            'user': 'postgres',
            'password': ''
        }
        
        for key, default_value in default_config.items():
            if key == 'password':
                prompt = f"{key.capitalize()}: "
            else:
                prompt = f"{key.capitalize()} (默认: {default_value}): "
            
            value = input(prompt).strip()
            if not value and key != 'password':
                value = default_value
            
            self.pg_config[key] = value
        
        print("\n📋 配置信息:")
        for key, value in self.pg_config.items():
            if key == 'password':
                print(f"   {key}: {'*' * len(value)}")
            else:
                print(f"   {key}: {value}")
        
        return True
    
    def _test_postgresql_connection(self) -> bool:
        """测试PostgreSQL连接"""
        print("\n🔍 步骤3: 测试PostgreSQL连接")
        print("-" * 40)
        
        try:
            import psycopg2
            from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
            
            # 先尝试连接到postgres数据库
            print("测试服务器连接...")
            try:
                conn = psycopg2.connect(
                    host=self.pg_config['host'],
                    port=self.pg_config['port'],
                    database='postgres',
                    user=self.pg_config['user'],
                    password=self.pg_config['password']
                )
                conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
                print("✅ PostgreSQL服务器连接成功")
                
                # 检查目标数据库是否存在
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (self.pg_config['database'],))
                db_exists = cursor.fetchone() is not None
                
                if not db_exists:
                    print(f"数据库 '{self.pg_config['database']}' 不存在，正在创建...")
                    cursor.execute(f'CREATE DATABASE "{self.pg_config["database"]}"')
                    print(f"✅ 数据库 '{self.pg_config['database']}' 创建成功")
                
                conn.close()
                
                # 测试目标数据库连接
                print("测试目标数据库连接...")
                conn = psycopg2.connect(**self.pg_config)
                conn.close()
                print("✅ 目标数据库连接成功")
                
                return True
                
            except Exception as e:
                print(f"❌ PostgreSQL连接失败: {e}")
                
                retry = input("是否重新配置连接信息? (y/N): ").strip().lower()
                if retry in ['y', 'yes', '是']:
                    return self._get_postgresql_config() and self._test_postgresql_connection()
                else:
                    return False
                
        except ImportError:
            print("❌ psycopg2模块未安装")
            print("正在安装...")
            
            import subprocess
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "psycopg2-binary"], 
                             check=True, capture_output=True)
                print("✅ psycopg2-binary安装成功")
                return self._test_postgresql_connection()
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {e}")
                return False
    
    def _confirm_migration(self) -> bool:
        """确认开始迁移"""
        print("\n⚠️ 步骤4: 确认迁移")
        print("-" * 40)
        
        print("迁移信息确认:")
        print(f"源数据库: {self.original_db_path}")
        print(f"目标数据库: {self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}")
        print()
        print("⚠️ 重要提醒:")
        print("1. 迁移过程将修改应用程序配置文件")
        print("2. 原SQLite数据库将被复制备份")
        print("3. 迁移过程可能需要10-30分钟")
        print("4. 请确保PostgreSQL服务器稳定运行")
        
        confirm = input("\n是否开始迁移? (y/N): ").strip().lower()
        return confirm in ['y', 'yes', '是']
    
    def _execute_migration(self) -> bool:
        """执行迁移"""
        print("\n🚀 步骤5: 执行数据迁移")
        print("-" * 40)
        
        try:
            # 导入必要的模块
            import psycopg2
            
            # 1. 复制数据库
            if not self._copy_database():
                return False
            
            # 2. 分析数据库
            analysis = self._analyze_database()
            if not analysis:
                return False
            
            # 3. 连接PostgreSQL
            pg_conn = psycopg2.connect(**self.pg_config)
            pg_conn.autocommit = True
            
            # 4. 创建表结构
            if not self._create_tables(pg_conn, analysis['tables']):
                pg_conn.close()
                return False
            
            # 5. 迁移数据
            if not self._migrate_data(pg_conn, analysis['tables']):
                pg_conn.close()
                return False
            
            # 6. 创建视图和索引
            self._create_views_and_indexes(pg_conn, analysis)
            
            # 7. 验证迁移
            if not self._verify_migration(pg_conn, analysis):
                pg_conn.close()
                return False
            
            pg_conn.close()
            
            # 8. 更新配置
            self._update_config()
            
            # 9. 生成报告
            self._generate_report()
            
            print("\n🎉 迁移成功完成！")
            return True
            
        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            return False
    
    def _copy_database(self) -> bool:
        """复制数据库"""
        print("📋 复制SQLite数据库...")
        
        try:
            self.backup_db_path = f"sales_reports_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            shutil.copy2(self.original_db_path, self.backup_db_path)
            
            print(f"✅ 数据库已复制到: {self.backup_db_path}")
            return True
        except Exception as e:
            print(f"❌ 复制数据库失败: {e}")
            return False
    
    def _analyze_database(self) -> Dict[str, Any]:
        """分析数据库结构"""
        print("📊 分析数据库结构...")
        
        try:
            conn = sqlite3.connect(self.backup_db_path)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            table_names = [row[0] for row in cursor.fetchall()]
            
            tables = {}
            total_rows = 0
            
            for table_name in table_names:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                row_count = cursor.fetchone()[0]
                total_rows += row_count
                
                tables[table_name] = {
                    'columns': columns,
                    'row_count': row_count
                }
                
                print(f"  表 {table_name}: {row_count:,} 行")
            
            # 获取视图
            cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='view'")
            views = {name: sql for name, sql in cursor.fetchall()}
            
            # 获取索引
            cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND sql IS NOT NULL")
            indexes = {name: sql for name, sql in cursor.fetchall() if not name.startswith('sqlite_')}
            
            conn.close()
            
            print(f"✅ 分析完成: {len(tables)} 表, {total_rows:,} 总行数")
            
            return {
                'tables': tables,
                'views': views,
                'indexes': indexes
            }
            
        except Exception as e:
            print(f"❌ 分析数据库失败: {e}")
            return {}
    
    def _create_tables(self, pg_conn, tables: Dict[str, Any]) -> bool:
        """创建表结构"""
        print("📊 创建PostgreSQL表结构...")
        
        try:
            cursor = pg_conn.cursor()
            
            for table_name, table_info in tables.items():
                print(f"  创建表: {table_name}")
                
                # 生成PostgreSQL DDL
                columns_ddl = []
                for col in table_info['columns']:
                    col_name = col[1]
                    col_type = col[2].upper()
                    not_null = bool(col[3])
                    default_value = col[4]
                    is_primary_key = bool(col[5])
                    
                    # 数据类型映射
                    if col_type in ['INTEGER', 'INT']:
                        pg_type = 'INTEGER'
                    elif col_type in ['TEXT', 'VARCHAR']:
                        pg_type = 'TEXT'
                    elif col_type in ['REAL', 'FLOAT']:
                        pg_type = 'REAL'
                    else:
                        pg_type = 'TEXT'
                    
                    col_def = f'"{col_name}" {pg_type}'
                    
                    if is_primary_key:
                        col_def += ' PRIMARY KEY'
                    elif not_null:
                        col_def += ' NOT NULL'
                    
                    if default_value is not None:
                        if isinstance(default_value, str):
                            col_def += f" DEFAULT '{default_value}'"
                        else:
                            col_def += f' DEFAULT {default_value}'
                    
                    columns_ddl.append(col_def)
                
                # 删除表如果存在
                cursor.execute(f'DROP TABLE IF EXISTS "{table_name}" CASCADE')
                
                # 创建表
                ddl = f'CREATE TABLE "{table_name}" (\n    ' + ',\n    '.join(columns_ddl) + '\n)'
                cursor.execute(ddl)
            
            print("✅ 所有表创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建表失败: {e}")
            return False
    
    def _migrate_data(self, pg_conn, tables: Dict[str, Any]) -> bool:
        """迁移数据"""
        print("📦 迁移数据...")
        
        try:
            sqlite_conn = sqlite3.connect(self.backup_db_path)
            pg_cursor = pg_conn.cursor()
            
            total_migrated = 0
            
            for table_name, table_info in tables.items():
                row_count = table_info['row_count']
                if row_count == 0:
                    continue
                
                print(f"  迁移表 {table_name}: {row_count:,} 行")
                
                # 读取数据
                df = pd.read_sql_query(f'SELECT * FROM `{table_name}`', sqlite_conn)
                
                # 准备插入语句
                columns = [col[1] for col in table_info['columns']]
                quoted_columns = [f'"{col}"' for col in columns]
                placeholders = ', '.join(['%s'] * len(columns))
                insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'
                
                # 批量插入
                batch_size = 1000
                for i in range(0, len(df), batch_size):
                    batch_df = df.iloc[i:i+batch_size]
                    
                    batch_data = []
                    for _, row in batch_df.iterrows():
                        row_data = []
                        for col_name in columns:
                            value = row[col_name] if col_name in row else None
                            if pd.isna(value):
                                value = None
                            row_data.append(value)
                        batch_data.append(tuple(row_data))
                    
                    pg_cursor.executemany(insert_sql, batch_data)
                    
                    progress = min(i + batch_size, len(df))
                    print(f"    进度: {progress:,}/{len(df):,}")
                
                total_migrated += len(df)
            
            sqlite_conn.close()
            print(f"✅ 数据迁移完成，总计: {total_migrated:,} 行")
            return True
            
        except Exception as e:
            print(f"❌ 数据迁移失败: {e}")
            return False
    
    def _create_views_and_indexes(self, pg_conn, analysis):
        """创建视图和索引"""
        print("👁️ 创建视图和索引...")
        
        cursor = pg_conn.cursor()
        
        # 创建视图（可能失败，不影响主要功能）
        for view_name, view_sql in analysis['views'].items():
            try:
                cursor.execute(f'DROP VIEW IF EXISTS "{view_name}" CASCADE')
                cursor.execute(view_sql)
                print(f"  ✅ 视图 {view_name}")
            except Exception as e:
                print(f"  ⚠️ 视图 {view_name} 失败: {e}")
        
        # 创建索引（可能失败，不影响主要功能）
        for index_name, index_sql in analysis['indexes'].items():
            try:
                cursor.execute(f'DROP INDEX IF EXISTS "{index_name}"')
                cursor.execute(index_sql)
                print(f"  ✅ 索引 {index_name}")
            except Exception as e:
                print(f"  ⚠️ 索引 {index_name} 失败: {e}")
    
    def _verify_migration(self, pg_conn, analysis) -> bool:
        """验证迁移"""
        print("🔍 验证迁移结果...")
        
        try:
            cursor = pg_conn.cursor()
            all_match = True
            
            for table_name, table_info in analysis['tables'].items():
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                pg_count = cursor.fetchone()[0]
                sqlite_count = table_info['row_count']
                
                if pg_count == sqlite_count:
                    print(f"  ✅ {table_name}: {pg_count:,} 行")
                else:
                    print(f"  ❌ {table_name}: PG={pg_count:,}, SQLite={sqlite_count:,}")
                    all_match = False
            
            if all_match:
                print("✅ 验证通过")
            else:
                print("⚠️ 部分表行数不匹配")
            
            return True  # 即使不完全匹配也继续
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def _update_config(self):
        """更新配置文件"""
        print("🔧 更新应用配置...")
        
        try:
            # 备份原配置
            if os.path.exists("config.ini"):
                backup_config = f"config_sqlite_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ini"
                shutil.copy2("config.ini", backup_config)
                print(f"  原配置已备份: {backup_config}")
            
            # 写入新配置
            config_content = f"""[Database]
db_type = postgresql
db_host = {self.pg_config['host']}
db_port = {self.pg_config['port']}
db_name = {self.pg_config['database']}
db_user = {self.pg_config['user']}
db_password = {self.pg_config['password']}

[Scripts]
intelligent_processor = report 脚本 3.0.py
modular_processor = report 模块化设计 7.0.py
refund_script_optimized = scripts/refund_process_optimized.py
data_import_script_optimized = scripts/data_import_optimized.py

[UI]
window_width = 900
window_height = 700
theme = iphone_style
"""
            
            with open("config.ini", 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print("✅ 配置文件已更新")
            
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
    
    def _generate_report(self):
        """生成迁移报告"""
        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("SQLite到PostgreSQL迁移报告\n")
                f.write("="*50 + "\n")
                f.write(f"迁移时间: {datetime.now()}\n")
                f.write(f"源数据库: {self.original_db_path}\n")
                f.write(f"备份数据库: {self.backup_db_path}\n")
                f.write(f"目标数据库: {self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}\n")
                f.write("\n迁移状态: 成功\n")
            
            print(f"✅ 迁移报告: {report_file}")
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")

def main():
    """主函数"""
    migrator = InteractiveMigration()
    success = migrator.run_migration()
    
    if success:
        print("\n🎉 迁移成功！")
        print("\n📋 后续步骤:")
        print("1. 重启应用程序")
        print("2. 测试所有功能")
        print("3. 设置PostgreSQL备份")
        return 0
    else:
        print("\n❌ 迁移失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
