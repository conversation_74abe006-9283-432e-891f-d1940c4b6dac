# -*- coding: utf-8 -*-
"""
便携式PostgreSQL管理器
启动、停止、状态检查、备份等功能
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

# 可选依赖
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import psycopg2
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

class PostgreSQLManager:
    """便携式PostgreSQL管理器"""
    
    def __init__(self, base_dir: str = None):
        if base_dir is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        self.base_dir = Path(base_dir)
        self.postgresql_dir = self.base_dir / "PostgreSQL database"
        self.config_file = self.postgresql_dir / "config.json"
        self.config = self.load_config()
        
        if self.config:
            self.bin_path = Path(self.config['bin_path'])
            self.data_path = Path(self.config['data_path'])
            self.log_path = Path(self.config['log_path'])
            self.backup_path = Path(self.config['backup_path'])
        
    def load_config(self) -> Optional[Dict[str, Any]]:
        """加载配置文件"""
        if not self.config_file.exists():
            print(f"❌ 配置文件不存在: {self.config_file}")
            print("💡 请先运行 postgresql_setup.py 安装PostgreSQL")
            return None
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return None
    
    def is_installed(self) -> bool:
        """检查PostgreSQL是否已安装"""
        return self.config is not None and self.config.get('installed', False)
    
    def is_running(self) -> bool:
        """检查PostgreSQL是否正在运行"""
        if not self.is_installed():
            return False

        try:
            if PSUTIL_AVAILABLE:
                # 使用psutil检查进程
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] == 'postgres.exe':
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if str(self.data_path) in cmdline:
                            return True
                return False
            else:
                # 使用Windows tasklist命令检查进程
                result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq postgres.exe'],
                                      capture_output=True, text=True)
                return 'postgres.exe' in result.stdout
        except Exception:
            # 最后尝试连接测试
            return self.test_connection()
    
    def start_server(self) -> bool:
        """启动PostgreSQL服务器"""
        if not self.is_installed():
            print("❌ PostgreSQL未安装")
            return False
        
        if self.is_running():
            print("✅ PostgreSQL已在运行")
            return True
        
        try:
            print("🔄 启动PostgreSQL服务器...")
            
            pg_ctl_exe = self.bin_path / "pg_ctl.exe"
            if not pg_ctl_exe.exists():
                print(f"❌ 找不到pg_ctl.exe: {pg_ctl_exe}")
                return False
            
            # 启动命令
            cmd = [
                str(pg_ctl_exe),
                "start",
                "-D", str(self.data_path),
                "-l", str(self.log_path / "postgresql.log"),
                "-w"  # 等待启动完成
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(self.bin_path))
            
            if result.returncode == 0:
                print("✅ PostgreSQL服务器启动成功")
                
                # 等待服务器完全启动
                for i in range(10):
                    if self.test_connection():
                        print("✅ PostgreSQL连接测试成功")
                        return True
                    time.sleep(1)
                
                print("⚠️ PostgreSQL启动但连接测试失败")
                return True
            else:
                print(f"❌ PostgreSQL启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动PostgreSQL异常: {e}")
            return False
    
    def stop_server(self) -> bool:
        """停止PostgreSQL服务器"""
        if not self.is_installed():
            print("❌ PostgreSQL未安装")
            return False
        
        if not self.is_running():
            print("✅ PostgreSQL已停止")
            return True
        
        try:
            print("🔄 停止PostgreSQL服务器...")
            
            pg_ctl_exe = self.bin_path / "pg_ctl.exe"
            
            # 停止命令
            cmd = [
                str(pg_ctl_exe),
                "stop",
                "-D", str(self.data_path),
                "-m", "fast",  # 快速停止
                "-w"  # 等待停止完成
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(self.bin_path))
            
            if result.returncode == 0:
                print("✅ PostgreSQL服务器停止成功")
                return True
            else:
                print(f"❌ PostgreSQL停止失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 停止PostgreSQL异常: {e}")
            return False
    
    def restart_server(self) -> bool:
        """重启PostgreSQL服务器"""
        print("🔄 重启PostgreSQL服务器...")
        return self.stop_server() and self.start_server()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        if not self.is_installed():
            return False

        if not PSYCOPG2_AVAILABLE:
            print("⚠️ psycopg2未安装，无法测试PostgreSQL连接")
            return False

        try:
            conn = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password']
            )
            conn.close()
            return True
        except Exception:
            return False

    def get_connection(self):
        """获取数据库连接"""
        if not self.is_installed():
            raise Exception("PostgreSQL未安装")

        if not PSYCOPG2_AVAILABLE:
            raise Exception("psycopg2未安装，无法连接PostgreSQL")

        return psycopg2.connect(
            host=self.config['host'],
            port=self.config['port'],
            database=self.config['database'],
            user=self.config['user'],
            password=self.config['password']
        )
    
    def backup_database(self, backup_name: str = None) -> Optional[str]:
        """备份数据库"""
        if not self.is_installed():
            print("❌ PostgreSQL未安装")
            return None
        
        if not self.is_running():
            print("❌ PostgreSQL未运行")
            return None
        
        try:
            if backup_name is None:
                backup_name = f"postgresql_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
            backup_file = self.backup_path / backup_name
            self.backup_path.mkdir(parents=True, exist_ok=True)
            
            print(f"🔄 开始备份数据库到: {backup_file}")
            
            pg_dump_exe = self.bin_path / "pg_dump.exe"
            
            # 备份命令
            cmd = [
                str(pg_dump_exe),
                "-h", self.config['host'],
                "-p", str(self.config['port']),
                "-U", self.config['user'],
                "-d", self.config['database'],
                "-f", str(backup_file),
                "--verbose",
                "--no-password"
            ]
            
            # 设置环境变量避免密码提示
            env = os.environ.copy()
            env['PGPASSWORD'] = self.config['password']
            
            result = subprocess.run(cmd, capture_output=True, text=True, env=env, cwd=str(self.bin_path))
            
            if result.returncode == 0:
                print(f"✅ 数据库备份成功: {backup_file}")
                return str(backup_file)
            else:
                print(f"❌ 数据库备份失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 备份数据库异常: {e}")
            return None
    
    def restore_database(self, backup_file: str) -> bool:
        """恢复数据库"""
        if not self.is_installed():
            print("❌ PostgreSQL未安装")
            return False
        
        if not self.is_running():
            print("❌ PostgreSQL未运行")
            return False
        
        backup_path = Path(backup_file)
        if not backup_path.exists():
            print(f"❌ 备份文件不存在: {backup_file}")
            return False
        
        try:
            print(f"🔄 开始恢复数据库从: {backup_file}")
            
            psql_exe = self.bin_path / "psql.exe"
            
            # 恢复命令
            cmd = [
                str(psql_exe),
                "-h", self.config['host'],
                "-p", str(self.config['port']),
                "-U", self.config['user'],
                "-d", self.config['database'],
                "-f", str(backup_path),
                "--quiet"
            ]
            
            # 设置环境变量避免密码提示
            env = os.environ.copy()
            env['PGPASSWORD'] = self.config['password']
            
            result = subprocess.run(cmd, capture_output=True, text=True, env=env, cwd=str(self.bin_path))
            
            if result.returncode == 0:
                print(f"✅ 数据库恢复成功")
                return True
            else:
                print(f"❌ 数据库恢复失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 恢复数据库异常: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取PostgreSQL状态"""
        status = {
            'installed': self.is_installed(),
            'running': False,
            'connection': False,
            'config': self.config
        }
        
        if status['installed']:
            status['running'] = self.is_running()
            if status['running']:
                status['connection'] = self.test_connection()
        
        return status
    
    def print_status(self):
        """打印状态信息"""
        status = self.get_status()
        
        print("📊 PostgreSQL状态:")
        print(f"  安装状态: {'✅ 已安装' if status['installed'] else '❌ 未安装'}")
        
        if status['installed']:
            print(f"  运行状态: {'✅ 运行中' if status['running'] else '❌ 已停止'}")
            print(f"  连接状态: {'✅ 连接正常' if status['connection'] else '❌ 连接失败'}")
            print(f"  安装路径: {self.postgresql_dir}")
            print(f"  数据路径: {self.data_path}")
            print(f"  日志路径: {self.log_path}")
            print(f"  备份路径: {self.backup_path}")
            print(f"  连接信息: {self.config['host']}:{self.config['port']}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='PostgreSQL管理器')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status', 'backup', 'restore'],
                       help='要执行的操作')
    parser.add_argument('--backup-file', help='备份文件路径（用于restore操作）')
    parser.add_argument('--backup-name', help='备份文件名（用于backup操作）')
    
    args = parser.parse_args()
    
    manager = PostgreSQLManager()
    
    if args.action == 'start':
        success = manager.start_server()
    elif args.action == 'stop':
        success = manager.stop_server()
    elif args.action == 'restart':
        success = manager.restart_server()
    elif args.action == 'status':
        manager.print_status()
        success = True
    elif args.action == 'backup':
        backup_file = manager.backup_database(args.backup_name)
        success = backup_file is not None
    elif args.action == 'restore':
        if not args.backup_file:
            print("❌ 请指定备份文件: --backup-file <文件路径>")
            success = False
        else:
            success = manager.restore_database(args.backup_file)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
