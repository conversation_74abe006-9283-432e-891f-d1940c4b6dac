# -*- coding: utf-8 -*-
"""
测试编码修复效果
"""

import os
import sys
import locale

# 🔧 强化编码设置 - 解决乱码问题
try:
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    # 设置locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, '')
            except:
                pass
    
    # 重新配置标准输出
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    
    # 设置默认编码
    if hasattr(sys, 'setdefaultencoding'):
        sys.setdefaultencoding('utf-8')
        
except Exception:
    pass  # 静默处理编码设置失败

def test_chinese_output():
    """测试中文输出"""
    
    print("🔧 测试编码修复效果")
    print("=" * 60)
    
    # 测试用户反馈中的乱码字符串
    test_strings = [
        ("使用第一文件路径", "ä½¿ç¨ç¬¬ä¸æä»¶è·¯å¾"),
        ("第一文件读取的列名：", "ç¬¬ä¸æä»¶è¯»åçååï¼"),
        ("检测到独立的Time列", "æ£æµå°ç¬ç«çTimeå"),
        ("警告：第一文件列数为", "è­¦åï¼ç¬¬ä¸æä»¶åæ°ä¸º"),
        ("找到'Transaction ID'列，将用于匹配", "æ¾å°'Transaction ID'åï¼å°ç¨äºå¹é"),
        ("⏰ 处理日期时间数据...", "â° å¤çæ¥ææ¶é´æ°æ®..."),
        ("📅 日期一致性检查通过", "ð æ¥æä¸è´æ§æ£æ¥éè¿"),
        ("📊 开始智能检测Transaction Num匹配能力...", "ð å¼å§æºè½æ£æµTransaction Numå¹éè½å..."),
        ("✅ 检测到 Transaction Num 列", "â æ£æµå° Transaction Num å"),
        ("📊 第二文件总记录数", "ï¿½ ç¬¬äºæä»¶æ»è®°å½æ°"),
        ("📊 第二文件Transaction Num非空记录数", "ï¿½ ç¬¬äºæä»¶Transaction Numéç©ºè®°å½æ°"),
        ("📊 第二文件Transaction Num唯一值数量", "ï¿½ ç¬¬äºæä»¶Transaction Numå¯ä¸å¼æ°é"),
        ("📊 第二文件Transaction Num填充率", "ï¿½ ç¬¬äºæä»¶Transaction Numå¡«åç"),
        ("📊 检查Transaction Num与Transaction ID的匹配能力...", "ð æ£æ¥Transaction Numä¸Transaction IDçå¹éè½å..."),
        ("📊 第一文件有效Transaction ID数量", "ð ç¬¬ä¸æä»¶ææTransaction IDæ°é"),
        ("📊 第二文件有效Transaction Num数量", "ï¿½ ç¬¬äºæä»¶ææTransaction Numæ°é"),
        ("📊 第一文件Transaction ID样本", "ð ç¬¬ä¸æä»¶Transaction IDæ ·æ¬"),
        ("📊 第二文件Transaction Num样本", "ð ç¬¬äºæä»¶Transaction Numæ ·æ¬"),
        ("📊 可匹配的Transaction ID/Num数量", "ð å¯å¹éçTransaction ID/Numæ°é"),
        ("📊 匹配的Transaction ID样本", "ð å¹éçTransaction IDæ ·æ¬"),
        ("📊 匹配率", "ð å¹éç"),
        ("✅ Transaction Num具备匹配能力", "â Transaction Numå·å¤å¹éè½å"),
        ("📊 将使用Transaction ID匹配方式", "ð å°ä½¿ç¨Transaction IDå¹éæ¹å¼"),
        ("🎯 匹配模式: Transaction ID匹配", "ð¯ å¹éæ¨¡å¼: Transaction IDå¹é"),
        ("📊 将使用Transaction ID进行数据匹配和同步", "ð å°ä½¿ç¨Transaction IDè¿è¡æ°æ®å¹éååæ­¥"),
        ("📊 启动模块化数据处理器...", "ð å¯å¨æ¨¡ååæ°æ®å¤çå¨..."),
        ("🗂️ 开始按日期分组处理...", "ðï¸ å¼å§ææ¥æåç»å¤ç..."),
        ("🎯 匹配模式设置为: transaction_id", "ð¯ å¹éæ¨¡å¼è®¾ç½®ä¸º: transaction_id"),
        ("📊 发现 1 个日期分组", "ð åç° 1 ä¸ªæ¥æåç»"),
        ("📊 处理日期: 2025-06-17", "ð å¤çæ¥æ: 2025-06-17"),
        ("📊 修复前匹配数: 0, 修复后匹配数: 0", "ð ä¿®å¤åå¹éæ°: 0, ä¿®å¤åå¹éæ°: 0"),
        ("⚠️ Transaction ID仍无匹配，可能第二文件中不存在此ID", "â ï¸ Transaction IDä»æ å¹éï¼å¯è½ç¬¬äºæä»¶ä¸­ä¸å­å¨æ­¤ID"),
        ("📊 处理Transaction ID", "ð å¤çTransaction ID"),
        ("找到 1 条匹配记录", "æ¾å° 1 æ¡å¹éè®°å½"),
        ("✅ 记录 27 标记设置成功: Matched_Flag = True", "â è®°å½ 27 æ è®°è®¾ç½®æå: Matched_Flag = True"),
        ("📊 Transaction ID", "ð Transaction ID"),
        ("匹配统计: 成功 1, 失败 0", "å¹éç»è®¡: æå 1, å¤±è´¥ 0"),
        ("✅ 处理完成，耗时", "â å¤çå®æï¼èæ¶"),
        ("📊 transaction_id 模式统计:", "ð transaction_id æ¨¡å¼ç»è®¡:"),
        ("处理: 27 条", "å¤ç: 27 æ¡"),
        ("匹配: 26 条", "å¹é: 26 æ¡"),
        ("插入: 1 条", "æå¥: 1 æ¡"),
        ("✅ 模块化数据处理完成", "â æ¨¡ååæ°æ®å¤çå®æ"),
        ("📊 验证 - 实际匹配的记录数量", "ð éªè¯ - å®éå¹éçè®°å½æ°é"),
        ("📊 验证 - DataFrame中实际标记数量", "ð éªè¯ - DataFrameä¸­å®éæ è®°æ°é"),
        ("🚨 严重警告：matched_indices数量与DataFrame标记数量不符！", "ð¨ ä¸¥éè­¦åï¼matched_indicesæ°éä¸DataFrameæ è®°æ°éä¸ç¬¦ï¼"),
        ("差异:", "å·®å¼:"),
        ("总记录数:", "æ»è®°å½æ°:"),
        ("已标记记录数:", "å·²æ è®°è®°å½æ°:"),
        ("未标记记录数:", "æªæ è®°è®°å½æ°:"),
        ("📊 插入统计", "ð æå¥ç»è®¡"),
        ("条记录, 总金额:", "æ¡è®°å½, æ»éé¢:"),
        ("第一文件总金额，包含所有settled记录", "ç¬¬ä¸æä»¶æ»éé¢ï¼åå«ææsettledè®°å½"),
        ("第一文件没有Order types列，无法检查API订单", "ç¬¬ä¸æä»¶æ²¡æOrder typesåï¼æ æ³æ£æ¥APIè®¢å"),
        ("第二文件总金额，排除API订单", "ç¬¬äºæä»¶æ»éé¢ï¼æé¤APIè®¢åï¼"),
        ("金额差异，第一文件无法排除API订单", "éé¢å·®å¼ï¼ç¬¬ä¸æä»¶æ æ³æé¤APIè®¢åï¼"),
        ("Transaction ID匹配:", "Transaction IDå¹é:"),
        ("Transaction ID插入:", "Transaction IDæå¥:"),
        ("理论上应该完全匹配，差异应该为0", "çè®ºä¸åºè¯¥å®å¨å¹éï¼å·®å¼åºè¯¥ä¸º0"),
        ("执行数据恢复和补全...", "æ§è¡æ°æ®æ¢å¤åè¡¥å¨..."),
        ("📊 开始执行数据恢复...", "ð å¼å§æ§è¡æ°æ®æ¢å¤..."),
        ("✅ Transaction Num修复:", "â Transaction Numä¿®å¤:"),
        ("✅ Equipment信息恢复:", "â Equipmentä¿¡æ¯æ¢å¤:"),
        ("✅ Order No.信息恢复:", "â Order No.ä¿¡æ¯æ¢å¤:"),
        ("Transaction ID匹配模式：金额差异在可接受范围内，跳过自动修正", "Transaction IDå¹éæ¨¡å¼ï¼éé¢å·®å¼å¨å¯æ¥åèå´åï¼è·³è¿èªå¨ä¿®æ­£"),
        ("Transaction ID匹配模式：数据已通过Transaction ID同步，无需自动修正", "Transaction IDå¹éæ¨¡å¼ï¼æ°æ®å·²éè¿Transaction IDåæ­¥ï¼æ éèªå¨ä¿®æ­£"),
        ("处理完成，结果已写入", "å¤çå®æï¼ç»æå·²åå¥"),
        ("- 数据已写入 DATA sheet", "- æ°æ®å·²åå¥ DATA sheet"),
        ("- 日志已写入 LOG sheet", "- æ¥å¿å·²åå¥ LOG sheet"),
        ("第二文件最终总金额:", "ç¬¬äºæä»¶æç»æ»éé¢:"),
        ("第一文件总金额:", "ç¬¬ä¸æä»¶æ»éé¢:"),
        ("金额差异:", "éé¢å·®å¼:"),
        ("✅ 金额匹配成功！", "â éé¢å¹éæåï¼"),
        ("使用指定的文件路径:", "ä½¿ç¨æå®çæä»¶è·¯å¾:"),
        ("第一文件:", "ç¬¬ä¸æä»¶:"),
        ("第二文件:", "ç¬¬äºæä»¶:"),
        ("Sheet名称:", "Sheetåç§°:"),
        ("脚本执行完成", "èæ¬æ§è¡å®æ")
    ]
    
    print("📋 测试中文字符输出:")
    print("-" * 60)
    
    success_count = 0
    total_count = len(test_strings)
    
    for i, (correct, garbled) in enumerate(test_strings, 1):
        try:
            print(f"{i:2d}. {correct}")
            success_count += 1
        except Exception as e:
            print(f"{i:2d}. 输出失败: {e}")
    
    print("-" * 60)
    print(f"📊 测试结果: {success_count}/{total_count} 成功")
    print(f"📊 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 编码修复成功！所有中文字符正常显示")
    else:
        print("⚠️ 编码修复部分成功，仍有问题需要解决")
    
    # 显示当前编码信息
    print("\n📋 当前编码信息:")
    print(f"   sys.stdout.encoding: {getattr(sys.stdout, 'encoding', 'Unknown')}")
    print(f"   sys.stderr.encoding: {getattr(sys.stderr, 'encoding', 'Unknown')}")
    print(f"   sys.getdefaultencoding(): {sys.getdefaultencoding()}")
    print(f"   locale.getpreferredencoding(): {locale.getpreferredencoding()}")
    
    return success_count == total_count

def main():
    """主函数"""
    
    print("🚀 编码修复测试")
    print("=" * 80)
    
    # 执行测试
    result = test_chinese_output()
    
    print("\n📊 测试总结:")
    print("=" * 80)
    
    if result:
        print("🎉 编码修复验证成功！")
        print("\n📋 修复效果:")
        print("  ✅ 中文字符正常显示")
        print("  ✅ 编码设置正确")
        print("  ✅ 乱码问题已解决")
        
        print("\n🎯 用户重新运行后预期看到:")
        print("  • 使用第一文件路径: [路径]")
        print("  • 第一文件读取的列名：[列名列表]")
        print("  • 检测到独立的Time列")
        print("  • 所有中文字符正常显示，无乱码")
        
    else:
        print("⚠️ 编码修复验证失败！")
        print("  需要进一步检查编码设置")
    
    return result

if __name__ == "__main__":
    result = main()
    input(f"\n按回车键退出... (测试{'成功' if result else '失败'})")
