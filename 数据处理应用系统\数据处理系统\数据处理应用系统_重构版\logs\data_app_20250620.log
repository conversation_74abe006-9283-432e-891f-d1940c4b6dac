2025-06-20 08:42:40,683 - WARNING - 日志控件未注册: general, 已注册的控件: []
2025-06-20 08:42:40,683 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 08:42:40,683 - INFO - [processing] 请使用浏览按钮选择文件
2025-06-20 08:42:41,007 - INFO - [general] 数据库状态: 可用数据库: SQLite
2025-06-20 08:42:41,010 - INFO - [general] 请使用浏览按钮选择文件
2025-06-20 08:42:41,010 - INFO - [import] 请使用浏览按钮选择文件
2025-06-20 08:42:41,017 - INFO - [general] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 08:42:41,017 - INFO - [refund] 请选择包含REFUND_LIST工作表的Excel文件进行退款处理
2025-06-20 08:42:41,039 - INFO - [general] 数据库设置已加载
2025-06-20 08:42:41,039 - INFO - [general] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:42:41,039 - INFO - [settings] 数据库设置已加载
2025-06-20 08:42:41,039 - INFO - [settings] 当前数据库路径: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:42:41,206 - INFO - [database_config] 数据库状态已刷新
2025-06-20 08:42:41,211 - INFO - 数据处理与导入应用已启动
2025-06-20 08:42:41,305 - INFO - [settings] 已刷新备份列表，共10个备份文件
2025-06-20 08:42:48,457 - INFO - [import] 已选择1个导入文件: 130625 CHINA IOT.xlsx
2025-06-20 08:42:48,458 - INFO - [import] 自动识别为IOT平台
2025-06-20 08:42:55,262 - INFO - [import] 选择的目标数据库: SQLite
2025-06-20 08:42:55,263 - INFO - [import] 开始导入数据，平台类型: IOT, 订单类型: all
2025-06-20 08:42:55,263 - INFO - [import] 目标数据库: SQLite
2025-06-20 08:42:55,264 - INFO - [import] 正在备份数据库...
2025-06-20 08:42:55,344 - INFO - SQLite数据库备份成功: sqlite_backup_20250620_084255.db
2025-06-20 08:42:55,344 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250620_084255.db
2025-06-20 08:42:55,344 - INFO - [import] 处理文件: 130625 CHINA IOT.xlsx
2025-06-20 08:42:55,345 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform IOT
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: File: 130625 CHINA IOT.xlsx
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: Platform: IOT
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: Total rows: 4197
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: New data: 4197
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-20 08:42:58,679 - INFO - [import] 📤 导入脚本: Actually inserted: 4197
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - IOT_Sales: 3530 records
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: 156 records
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: 511 records
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - IOT_Sales: inserted 3441 records
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: inserted 156 records
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: inserted 511 records
2025-06-20 08:42:58,680 - INFO - [import] 📤 导入脚本:   - APP_Sales: inserted 89 records
2025-06-20 08:42:58,681 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-20 08:42:58,681 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-20 08:42:58,681 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-20 08:42:58,681 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-20 08:42:58,681 - INFO - [import] 数据导入成功，返回码: 0
2025-06-20 08:42:58,681 - INFO - [import] 文件 130625 CHINA IOT.xlsx 导入成功
2025-06-20 08:42:58,681 - INFO - [import] 所有文件导入成功
2025-06-20 08:43:41,728 - INFO - [import] 已清空文件选择
2025-06-20 08:43:45,863 - INFO - [import] 已选择2个导入文件: 140625 CHINA IOT.xlsx, 150625 CHINA IOT.xlsx
2025-06-20 08:43:45,863 - INFO - [import] 自动识别为IOT平台
2025-06-20 08:43:47,206 - INFO - [import] 选择的目标数据库: SQLite
2025-06-20 08:43:47,207 - INFO - [import] 开始导入数据，平台类型: IOT, 订单类型: all
2025-06-20 08:43:47,207 - INFO - [import] 目标数据库: SQLite
2025-06-20 08:43:47,207 - INFO - [import] 正在备份数据库...
2025-06-20 08:43:47,280 - INFO - SQLite数据库备份成功: sqlite_backup_20250620_084347.db
2025-06-20 08:43:47,280 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250620_084347.db
2025-06-20 08:43:47,280 - INFO - [import] 处理文件: 140625 CHINA IOT.xlsx
2025-06-20 08:43:47,280 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform IOT
2025-06-20 08:43:51,218 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-20 08:43:51,218 - INFO - [import] 📤 导入脚本: File: 140625 CHINA IOT.xlsx
2025-06-20 08:43:51,218 - INFO - [import] 📤 导入脚本: Platform: IOT
2025-06-20 08:43:51,218 - INFO - [import] 📤 导入脚本: Total rows: 6544
2025-06-20 08:43:51,218 - INFO - [import] 📤 导入脚本: New data: 6544
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本: Actually inserted: 6544
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: 349 records
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本:   - IOT_Sales: 5540 records
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: 655 records
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-20 08:43:51,219 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: inserted 349 records
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本:   - IOT_Sales: inserted 5424 records
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: inserted 655 records
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本:   - APP_Sales: inserted 116 records
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-20 08:43:51,220 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-20 08:43:51,220 - INFO - [import] 数据导入成功，返回码: 0
2025-06-20 08:43:51,220 - INFO - [import] 文件 140625 CHINA IOT.xlsx 导入成功
2025-06-20 08:43:51,220 - INFO - [import] 处理文件: 150625 CHINA IOT.xlsx
2025-06-20 08:43:51,221 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform IOT
2025-06-20 08:43:55,386 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-20 08:43:55,386 - INFO - [import] 📤 导入脚本: File: 150625 CHINA IOT.xlsx
2025-06-20 08:43:55,386 - INFO - [import] 📤 导入脚本: Platform: IOT
2025-06-20 08:43:55,386 - INFO - [import] 📤 导入脚本: Total rows: 7457
2025-06-20 08:43:55,386 - INFO - [import] 📤 导入脚本: New data: 7457
2025-06-20 08:43:55,386 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本: Actually inserted: 7457
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - IOT_Sales: 6392 records
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: 276 records
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: 789 records
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - IOT_Sales: inserted 6242 records
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Refunding: inserted 276 records
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - IOT_Sales_Close: inserted 789 records
2025-06-20 08:43:55,387 - INFO - [import] 📤 导入脚本:   - APP_Sales: inserted 150 records
2025-06-20 08:43:55,388 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-20 08:43:55,388 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-20 08:43:55,388 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-20 08:43:55,388 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-20 08:43:55,388 - INFO - [import] 数据导入成功，返回码: 0
2025-06-20 08:43:55,388 - INFO - [import] 文件 150625 CHINA IOT.xlsx 导入成功
2025-06-20 08:43:55,388 - INFO - [import] 所有文件导入成功
2025-06-20 08:44:13,013 - INFO - [import] 已清空文件选择
2025-06-20 08:44:19,972 - INFO - [import] 已选择3个导入文件: 130625 CHINA ZERO.xlsx, 140625 CHINA ZERO.xlsx, 150625 CHINA ZERO.xlsx
2025-06-20 08:44:19,973 - INFO - [import] 自动识别为ZERO平台
2025-06-20 08:44:21,727 - INFO - [import] 选择的目标数据库: SQLite
2025-06-20 08:44:21,728 - INFO - [import] 开始导入数据，平台类型: ZERO, 订单类型: all
2025-06-20 08:44:21,728 - INFO - [import] 目标数据库: SQLite
2025-06-20 08:44:21,728 - INFO - [import] 正在备份数据库...
2025-06-20 08:44:21,800 - INFO - SQLite数据库备份成功: sqlite_backup_20250620_084421.db
2025-06-20 08:44:21,800 - INFO - [import] SQLite数据库备份成功: sqlite_backup_20250620_084421.db
2025-06-20 08:44:21,801 - INFO - [import] 处理文件: 130625 CHINA ZERO.xlsx
2025-06-20 08:44:21,801 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform ZERO
2025-06-20 08:44:24,759 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-20 08:44:24,759 - INFO - [import] 📤 导入脚本: File: 130625 CHINA ZERO.xlsx
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: Platform: ZERO
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: Total rows: 101
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: New data: 101
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: Actually inserted: 101
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本:   - ZERO_Sales: 99 records
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本:   - ZERO_Sales_Refunding: 2 records
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-20 08:44:24,760 - INFO - [import] 📤 导入脚本:   - ZERO_Sales: inserted 99 records
2025-06-20 08:44:24,761 - INFO - [import] 📤 导入脚本:   - ZERO_Sales_Refunding: inserted 2 records
2025-06-20 08:44:24,761 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-20 08:44:24,761 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-20 08:44:24,761 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-20 08:44:24,761 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-20 08:44:24,761 - INFO - [import] 数据导入成功，返回码: 0
2025-06-20 08:44:24,761 - INFO - [import] 文件 130625 CHINA ZERO.xlsx 导入成功
2025-06-20 08:44:24,761 - INFO - [import] 处理文件: 140625 CHINA ZERO.xlsx
2025-06-20 08:44:24,761 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform ZERO
2025-06-20 08:44:26,062 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-20 08:44:26,062 - INFO - [import] 📤 导入脚本: File: 140625 CHINA ZERO.xlsx
2025-06-20 08:44:26,062 - INFO - [import] 📤 导入脚本: Platform: ZERO
2025-06-20 08:44:26,062 - INFO - [import] 📤 导入脚本: Total rows: 173
2025-06-20 08:44:26,062 - INFO - [import] 📤 导入脚本: New data: 173
2025-06-20 08:44:26,062 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本: Actually inserted: 173
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - ZERO_Sales: 167 records
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - ZERO_Sales_Refunding: 6 records
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - ZERO_Sales: inserted 167 records
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - ZERO_Sales_Refunding: inserted 6 records
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-20 08:44:26,063 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-20 08:44:26,063 - INFO - [import] 数据导入成功，返回码: 0
2025-06-20 08:44:26,064 - INFO - [import] 文件 140625 CHINA ZERO.xlsx 导入成功
2025-06-20 08:44:26,064 - INFO - [import] 处理文件: 150625 CHINA ZERO.xlsx
2025-06-20 08:44:26,064 - INFO - [import] 执行导入命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe c:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\scripts/data_import_optimized.py --file C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx --db_path C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db --platform ZERO
2025-06-20 08:44:27,334 - INFO - [import] 📤 导入脚本: SUCCESS: Smart import completed
2025-06-20 08:44:27,335 - INFO - [import] 📤 导入脚本: File: 150625 CHINA ZERO.xlsx
2025-06-20 08:44:27,335 - INFO - [import] 📤 导入脚本: Platform: ZERO
2025-06-20 08:44:27,335 - INFO - [import] 📤 导入脚本: Total rows: 107
2025-06-20 08:44:27,335 - INFO - [import] 📤 导入脚本: New data: 107
2025-06-20 08:44:27,335 - INFO - [import] 📤 导入脚本: Duplicate data: 0
2025-06-20 08:44:27,335 - INFO - [import] 📤 导入脚本: Actually inserted: 107
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本: Data distribution:
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - ZERO_Sales: 101 records
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - ZERO_Sales_Refunding: 6 records
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本: Insert details:
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - ZERO_Sales: inserted 101 records
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - ZERO_Sales_Refunding: inserted 6 records
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本: Smart import features:
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - Auto assign to tables based on Order_status
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - Auto backup and error recovery
2025-06-20 08:44:27,336 - INFO - [import] 📤 导入脚本:   - Transaction protection for data consistency
2025-06-20 08:44:27,336 - INFO - [import] 数据导入成功，返回码: 0
2025-06-20 08:44:27,336 - INFO - [import] 文件 150625 CHINA ZERO.xlsx 导入成功
2025-06-20 08:44:27,337 - INFO - [import] 所有文件导入成功
