# -*- coding: utf-8 -*-
"""
简单的PostgreSQL连接测试
"""

import configparser
import os

def test_config_reading():
    """测试配置文件读取"""
    print("🧪 测试配置文件读取...")
    
    config_file = "config.ini"
    
    try:
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        print("📋 配置文件内容:")
        for section in config.sections():
            print(f"  [{section}]")
            for key, value in config[section].items():
                print(f"    {key} = {value}")
        
        # 测试PostgreSQL配置
        if config.has_section('PostgreSQL'):
            pg_config = {
                'host': config.get('PostgreSQL', 'host', fallback='localhost'),
                'port': config.get('PostgreSQL', 'port', fallback='5432'),
                'database': config.get('PostgreSQL', 'database', fallback='postgres'),
                'user': config.get('PostgreSQL', 'user', fallback='postgres'),
                'password': config.get('PostgreSQL', 'password', fallback=''),
                'enabled': config.getboolean('PostgreSQL', 'enabled', fallback=False)
            }
            
            print("\n📊 PostgreSQL配置:")
            for key, value in pg_config.items():
                if key == 'password':
                    print(f"  {key}: {'*' * len(str(value))}")
                else:
                    print(f"  {key}: {value}")
            
            return pg_config
        else:
            print("❌ 没有找到PostgreSQL配置")
            return None
        
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        return None

def test_direct_postgresql_connection(pg_config):
    """直接测试PostgreSQL连接"""
    print("\n🧪 直接测试PostgreSQL连接...")
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host=pg_config['host'],
            port=pg_config['port'],
            database=pg_config['database'],
            user=pg_config['user'],
            password=pg_config['password']
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL连接成功")
        print(f"📊 版本信息: {version}")
        
        # 获取表列表
        cursor.execute("""
            SELECT table_name, 
                   (SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_name = t.table_name AND table_schema = 'public') as column_count
            FROM information_schema.tables t
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        print(f"📊 找到 {len(tables)} 个表:")
        for table_name, column_count in tables[:10]:  # 显示前10个表
            cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
            row_count = cursor.fetchone()[0]
            print(f"  {table_name}: {row_count:,} 行, {column_count} 列")
        
        if len(tables) > 10:
            print(f"  ... 还有 {len(tables) - 10} 个表")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False

def test_dual_database_manager_simple():
    """简单测试双数据库管理器"""
    print("\n🧪 简单测试双数据库管理器...")
    
    try:
        # 直接导入并创建实例
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'database'))
        
        from dual_database_manager import DualDatabaseManager
        
        # 直接创建实例，不使用全局单例
        manager = DualDatabaseManager()
        
        print("📊 SQLite配置:")
        for key, value in manager.sqlite_config.items():
            print(f"  {key}: {value}")
        
        print("📊 PostgreSQL配置:")
        for key, value in manager.postgresql_config.items():
            if key == 'password':
                print(f"  {key}: {'*' * len(str(value))}")
            else:
                print(f"  {key}: {value}")
        
        # 测试连接
        sqlite_ok = manager.test_sqlite_connection()
        postgresql_ok = manager.test_postgresql_connection()
        
        print(f"\n📊 连接测试结果:")
        print(f"  SQLite: {'✅ 成功' if sqlite_ok else '❌ 失败'}")
        print(f"  PostgreSQL: {'✅ 成功' if postgresql_ok else '❌ 失败'}")
        
        if postgresql_ok:
            # 获取PostgreSQL表信息
            pg_tables = manager.get_table_info('PostgreSQL')
            print(f"  PostgreSQL表数量: {len(pg_tables)}")
            total_rows = sum(table['rows'] for table in pg_tables)
            print(f"  PostgreSQL总行数: {total_rows:,}")
        
        return sqlite_ok and postgresql_ok
        
    except Exception as e:
        print(f"❌ 双数据库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 简单PostgreSQL连接测试")
    print("=" * 50)
    
    # 测试配置读取
    pg_config = test_config_reading()
    if not pg_config:
        return False
    
    # 测试直接连接
    if not test_direct_postgresql_connection(pg_config):
        return False
    
    # 测试双数据库管理器
    if not test_dual_database_manager_simple():
        return False
    
    print("\n🎉 所有测试通过！PostgreSQL集成正常。")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
