# -*- coding: utf-8 -*-
"""
SQLite到PostgreSQL数据库迁移分析器
分析现有SQLite数据库结构，为迁移做准备
"""

import sqlite3
import pandas as pd
import os
from typing import Dict, List, Tuple, Any
import json

class DatabaseAnalyzer:
    """数据库结构分析器"""
    
    def __init__(self, sqlite_db_path: str):
        self.sqlite_db_path = sqlite_db_path
        self.analysis_result = {}
    
    def analyze_database(self) -> Dict[str, Any]:
        """分析数据库结构"""
        print("🔍 开始分析SQLite数据库结构...")
        
        if not os.path.exists(self.sqlite_db_path):
            print(f"❌ 数据库文件不存在: {self.sqlite_db_path}")
            return {}
        
        try:
            with sqlite3.connect(self.sqlite_db_path) as conn:
                # 分析表结构
                tables = self._analyze_tables(conn)
                
                # 分析视图
                views = self._analyze_views(conn)
                
                # 分析索引
                indexes = self._analyze_indexes(conn)
                
                # 分析数据统计
                data_stats = self._analyze_data_statistics(conn, tables)
                
                self.analysis_result = {
                    'database_path': self.sqlite_db_path,
                    'tables': tables,
                    'views': views,
                    'indexes': indexes,
                    'data_statistics': data_stats,
                    'migration_notes': self._generate_migration_notes()
                }
                
                print("✅ 数据库分析完成")
                return self.analysis_result
                
        except Exception as e:
            print(f"❌ 分析数据库时出错: {e}")
            return {}
    
    def _analyze_tables(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """分析表结构"""
        print("📊 分析表结构...")
        
        # 获取所有表名
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        table_names = [row[0] for row in cursor.fetchall()]
        
        tables = {}
        for table_name in table_names:
            print(f"  分析表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # 获取表的CREATE语句
            cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            result = cursor.fetchone()
            create_sql = result[0] if result else ""
            
            # 获取数据行数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            tables[table_name] = {
                'columns': [
                    {
                        'name': col[1],
                        'type': col[2],
                        'not_null': bool(col[3]),
                        'default_value': col[4],
                        'primary_key': bool(col[5])
                    }
                    for col in columns
                ],
                'create_sql': create_sql,
                'row_count': row_count,
                'postgresql_equivalent': self._convert_table_to_postgresql(table_name, columns)
            }
        
        return tables
    
    def _analyze_views(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """分析视图"""
        print("👁️ 分析视图...")
        
        cursor = conn.cursor()
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='view'")
        view_data = cursor.fetchall()
        
        views = {}
        for view_name, view_sql in view_data:
            print(f"  分析视图: {view_name}")
            views[view_name] = {
                'original_sql': view_sql,
                'postgresql_equivalent': self._convert_view_to_postgresql(view_sql)
            }
        
        return views
    
    def _analyze_indexes(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """分析索引"""
        print("🔍 分析索引...")
        
        cursor = conn.cursor()
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND sql IS NOT NULL")
        index_data = cursor.fetchall()
        
        indexes = {}
        for index_name, index_sql in index_data:
            if index_name and not index_name.startswith('sqlite_'):
                print(f"  分析索引: {index_name}")
                indexes[index_name] = {
                    'original_sql': index_sql,
                    'postgresql_equivalent': self._convert_index_to_postgresql(index_sql)
                }
        
        return indexes
    
    def _analyze_data_statistics(self, conn: sqlite3.Connection, tables: Dict[str, Any]) -> Dict[str, Any]:
        """分析数据统计信息"""
        print("📈 分析数据统计...")
        
        stats = {}
        cursor = conn.cursor()
        
        for table_name, table_info in tables.items():
            print(f"  统计表: {table_name}")
            
            table_stats = {
                'row_count': table_info['row_count'],
                'column_stats': {}
            }
            
            # 分析每列的统计信息
            for column in table_info['columns']:
                col_name = column['name']
                col_type = column['type'].upper()
                
                try:
                    if 'TEXT' in col_type or 'VARCHAR' in col_type:
                        # 文本列统计
                        cursor.execute(f"SELECT COUNT(DISTINCT {col_name}), COUNT({col_name}) FROM {table_name}")
                        distinct_count, non_null_count = cursor.fetchone()
                        
                        cursor.execute(f"SELECT MAX(LENGTH({col_name})) FROM {table_name}")
                        max_length = cursor.fetchone()[0] or 0
                        
                        table_stats['column_stats'][col_name] = {
                            'distinct_values': distinct_count,
                            'non_null_count': non_null_count,
                            'max_length': max_length
                        }
                    
                    elif 'INT' in col_type or 'REAL' in col_type or 'NUMERIC' in col_type:
                        # 数值列统计
                        cursor.execute(f"SELECT MIN({col_name}), MAX({col_name}), COUNT({col_name}) FROM {table_name}")
                        min_val, max_val, non_null_count = cursor.fetchone()
                        
                        table_stats['column_stats'][col_name] = {
                            'min_value': min_val,
                            'max_value': max_val,
                            'non_null_count': non_null_count
                        }
                
                except Exception as e:
                    print(f"    警告: 无法统计列 {col_name}: {e}")
                    table_stats['column_stats'][col_name] = {'error': str(e)}
            
            stats[table_name] = table_stats
        
        return stats
    
    def _convert_table_to_postgresql(self, table_name: str, columns: List[Tuple]) -> str:
        """将SQLite表结构转换为PostgreSQL"""
        pg_columns = []
        
        for col in columns:
            col_name = col[1]
            col_type = col[2].upper()
            not_null = bool(col[3])
            default_value = col[4]
            is_primary_key = bool(col[5])
            
            # 数据类型映射
            pg_type = self._map_sqlite_type_to_postgresql(col_type)
            
            # 构建列定义
            col_def = f"    {col_name} {pg_type}"
            
            if is_primary_key:
                col_def += " PRIMARY KEY"
            elif not_null:
                col_def += " NOT NULL"
            
            if default_value is not None:
                if isinstance(default_value, str):
                    col_def += f" DEFAULT '{default_value}'"
                else:
                    col_def += f" DEFAULT {default_value}"
            
            pg_columns.append(col_def)
        
        return f"CREATE TABLE {table_name} (\n" + ",\n".join(pg_columns) + "\n);"
    
    def _map_sqlite_type_to_postgresql(self, sqlite_type: str) -> str:
        """SQLite类型到PostgreSQL类型的映射"""
        sqlite_type = sqlite_type.upper()
        
        type_mapping = {
            'INTEGER': 'INTEGER',
            'INT': 'INTEGER',
            'BIGINT': 'BIGINT',
            'TEXT': 'TEXT',
            'VARCHAR': 'VARCHAR',
            'CHAR': 'CHAR',
            'REAL': 'REAL',
            'FLOAT': 'REAL',
            'DOUBLE': 'DOUBLE PRECISION',
            'NUMERIC': 'NUMERIC',
            'DECIMAL': 'DECIMAL',
            'BOOLEAN': 'BOOLEAN',
            'BLOB': 'BYTEA',
            'TIMESTAMP': 'TIMESTAMP',
            'DATETIME': 'TIMESTAMP',
            'DATE': 'DATE',
            'TIME': 'TIME'
        }
        
        # 处理带参数的类型
        for sqlite_key, pg_type in type_mapping.items():
            if sqlite_type.startswith(sqlite_key):
                if '(' in sqlite_type:
                    # 保留参数，如 VARCHAR(50)
                    return sqlite_type.replace(sqlite_key, pg_type)
                else:
                    return pg_type
        
        # 默认映射为TEXT
        return 'TEXT'
    
    def _convert_view_to_postgresql(self, view_sql: str) -> str:
        """将SQLite视图转换为PostgreSQL"""
        # 基本的SQL语法转换
        pg_sql = view_sql
        
        # SQLite特定函数到PostgreSQL的映射
        function_mapping = {
            'SUBSTR(': 'SUBSTRING(',
            'LENGTH(': 'CHAR_LENGTH(',
            'DATETIME(': 'TO_TIMESTAMP(',
            'DATE(': 'DATE(',
            'STRFTIME(': 'TO_CHAR('
        }
        
        for sqlite_func, pg_func in function_mapping.items():
            pg_sql = pg_sql.replace(sqlite_func, pg_func)
        
        return pg_sql
    
    def _convert_index_to_postgresql(self, index_sql: str) -> str:
        """将SQLite索引转换为PostgreSQL"""
        # PostgreSQL索引语法基本兼容SQLite
        return index_sql
    
    def _generate_migration_notes(self) -> List[str]:
        """生成迁移注意事项"""
        notes = [
            "数据库迁移注意事项:",
            "1. PostgreSQL区分大小写，建议使用小写表名和列名",
            "2. PostgreSQL的自增列使用SERIAL类型",
            "3. 检查日期时间格式是否兼容",
            "4. 验证所有视图和索引是否正确创建",
            "5. 测试应用程序连接和查询功能",
            "6. 备份原始SQLite数据库",
            "7. 在生产环境部署前进行充分测试"
        ]
        return notes
    
    def save_analysis_report(self, output_file: str = "migration_analysis.json"):
        """保存分析报告"""
        if not self.analysis_result:
            print("❌ 没有分析结果可保存")
            return
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_result, f, indent=2, ensure_ascii=False)
            print(f"✅ 分析报告已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存分析报告失败: {e}")
    
    def print_summary(self):
        """打印分析摘要"""
        if not self.analysis_result:
            print("❌ 没有分析结果")
            return
        
        print("\n" + "="*60)
        print("📋 数据库迁移分析摘要")
        print("="*60)
        
        print(f"数据库路径: {self.analysis_result['database_path']}")
        print(f"表数量: {len(self.analysis_result['tables'])}")
        print(f"视图数量: {len(self.analysis_result['views'])}")
        print(f"索引数量: {len(self.analysis_result['indexes'])}")
        
        print("\n📊 表统计:")
        for table_name, table_info in self.analysis_result['tables'].items():
            print(f"  {table_name}: {table_info['row_count']} 行, {len(table_info['columns'])} 列")
        
        if self.analysis_result['views']:
            print("\n👁️ 视图:")
            for view_name in self.analysis_result['views'].keys():
                print(f"  {view_name}")
        
        if self.analysis_result['indexes']:
            print("\n🔍 索引:")
            for index_name in self.analysis_result['indexes'].keys():
                print(f"  {index_name}")
        
        print("\n⚠️ 迁移注意事项:")
        for note in self.analysis_result['migration_notes']:
            print(f"  {note}")

def main():
    """主函数"""
    # 数据库路径
    db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    
    print("🚀 SQLite到PostgreSQL迁移分析器")
    print("="*50)
    
    # 创建分析器
    analyzer = DatabaseAnalyzer(db_path)
    
    # 执行分析
    result = analyzer.analyze_database()
    
    if result:
        # 打印摘要
        analyzer.print_summary()
        
        # 保存分析报告
        analyzer.save_analysis_report()
        
        print("\n✅ 分析完成！请查看 migration_analysis.json 文件获取详细信息。")
    else:
        print("❌ 分析失败")

if __name__ == "__main__":
    main()
