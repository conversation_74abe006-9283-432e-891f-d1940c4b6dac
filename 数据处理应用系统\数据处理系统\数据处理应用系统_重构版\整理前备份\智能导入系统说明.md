# 🚀 智能数据导入系统

## 📋 系统概述

智能数据导入系统是重构版应用的核心功能之一，提供了基于订单状态的自动表分配、完善的备份恢复机制和事务保护功能。

## 🎯 核心特性

### 1. 智能表分配 🧠
- **自动识别订单状态**：根据 `Order_status` 字段自动判断数据应该导入到哪个表
- **多状态支持**：支持 Finished、Refunding、Close 等多种状态
- **默认表保护**：未知状态自动导入到原表，确保数据不丢失

### 2. 数据库表结构 🗄️

#### 原表（保持不变）
- `IOT_Sales` - IOT平台销售数据
- `ZERO_Sales` - ZERO平台销售数据  
- `APP_Sales` - APP平台销售数据

#### 新增表（智能分配）
- `IOT_Sales_Refunding` - IOT平台退款中数据
- `IOT_Sales_Close` - IOT平台关闭订单数据
- `ZERO_Sales_Refunding` - ZERO平台退款中数据
- `ZERO_Sales_Close` - ZERO平台关闭订单数据
- `APP_Sales_Refunding` - APP平台退款中数据
- `APP_Sales_Close` - APP平台关闭订单数据

### 3. 状态映射规则 📊

| 订单状态 | 目标表 | 说明 |
|---------|--------|------|
| Finish / Finished | `{Platform}_Sales` | 完成的订单 |
| Refunded / Refunding | `{Platform}_Sales_Refunding` | 退款中的订单 |
| Close / Closed | `{Platform}_Sales_Close` | 关闭的订单 |
| 其他状态 | `{Platform}_Sales` | 默认表（原表） |

### 4. 智能备份和恢复 🔄

#### 自动备份
- **操作前备份**：每次导入前自动创建数据库备份
- **备份命名**：`backup_导入_{平台}_{文件名}_{时间戳}.db`
- **备份验证**：自动验证备份文件完整性
- **自动清理**：保留最近20个备份文件

#### 错误恢复
- **自动检测**：操作失败时自动检测
- **用户选择**：提供图形界面让用户选择是否恢复
- **一键恢复**：点击确认即可恢复到操作前状态
- **恢复验证**：恢复后自动验证数据库完整性

### 5. 事务保护 🛡️

#### 原子性保证
- **全事务处理**：整个导入过程在一个事务中完成
- **批量插入**：支持大文件分批处理，但保持事务一致性
- **失败回滚**：任何步骤失败都会回滚所有操作

#### 数据一致性
- **重复检测**：智能检测重复数据
- **完整性验证**：导入前后数据完整性检查
- **状态验证**：确保数据导入到正确的表

## 🔧 使用方法

### 命令行使用
```bash
# 导入IOT平台数据
python scripts/data_import_optimized.py --file "数据文件.xlsx" --platform IOT

# 导入ZERO平台数据
python scripts/data_import_optimized.py --file "数据文件.xlsx" --platform ZERO

# 导入APP平台数据
python scripts/data_import_optimized.py --file "数据文件.xlsx" --platform APP

# 指定数据库路径
python scripts/data_import_optimized.py --file "数据文件.xlsx" --platform IOT --db_path "custom_path.db"
```

### 主应用程序使用
1. 打开 `数据处理与导入应用_完整版.py`
2. 切换到"数据导入"选项卡
3. 选择文件和平台
4. 点击"开始导入"
5. 系统自动进行智能导入

## 📊 导入结果示例

```
✅ 智能导入成功
📁 文件: sales_data.xlsx
🏷️ 平台: IOT
📊 总行数: 1000
🆕 新数据: 950
🔄 重复数据: 50
💾 实际插入: 950

📋 数据分布:
  • IOT_Sales: 800 条
  • IOT_Sales_Refunding: 100 条
  • IOT_Sales_Close: 50 条

💾 插入详情:
  • IOT_Sales: 插入 760 条记录
  • IOT_Sales_Refunding: 插入 95 条记录
  • IOT_Sales_Close: 插入 45 条记录

🎯 智能导入特性:
  • 自动根据Order_status分配到对应表
  • 自动备份和错误恢复机制
  • 事务保护确保数据一致性
```

## ⚠️ 重要说明

### 数据安全保证
1. **原表保护**：原有的 `IOT_Sales`、`ZERO_Sales`、`APP_Sales` 表数据完全不受影响
2. **备份机制**：每次操作前自动备份，失败时可一键恢复
3. **事务保护**：确保数据操作的原子性，要么全部成功要么全部回滚

### 兼容性保证
1. **向后兼容**：完全兼容原有的数据导入功能
2. **数据格式**：支持现有的Excel文件格式
3. **配置兼容**：使用现有的配置文件和设置

### 性能优化
1. **批量处理**：大文件自动分批处理，避免内存溢出
2. **连接池**：使用数据库连接池，提升处理速度
3. **智能分析**：预先分析数据分布，优化插入策略

## 🔍 故障排除

### 常见问题

**Q: 导入失败怎么办？**
A: 系统会自动显示错误恢复对话框，选择"是"即可恢复到操作前状态。

**Q: 数据导入到错误的表怎么办？**
A: 检查数据文件中的 `Order_status` 字段是否正确，系统严格按照状态映射规则分配。

**Q: 原表数据会被影响吗？**
A: 不会。新系统只会在原表基础上新增 Refunding 和 Close 表，原表数据完全不受影响。

**Q: 备份文件在哪里？**
A: 备份文件存储在 `database/backups/` 目录下，文件名包含操作时间戳。

### 日志查看
- 详细日志存储在 `logs/` 目录下
- 日志文件按日期和操作类型分类
- 可通过日志追踪完整的操作过程

## 🚀 未来扩展

### 计划功能
1. **更多状态支持**：支持更多订单状态的自动分配
2. **自定义规则**：允许用户自定义状态映射规则
3. **批量导入**：支持多文件批量导入
4. **数据同步**：支持不同表之间的数据同步

### 性能优化
1. **并行处理**：支持多线程并行导入
2. **增量导入**：支持增量数据导入
3. **压缩存储**：支持数据压缩存储

---

**智能数据导入系统 - 让数据管理更智能、更安全、更高效！** 🎉
