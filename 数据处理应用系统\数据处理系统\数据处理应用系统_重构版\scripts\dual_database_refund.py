# -*- coding: utf-8 -*-
"""
双数据库退款处理脚本
支持SQLite和PostgreSQL的退款操作，包含备份功能
"""

import os
import sys
import argparse
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

# 设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.dual_database_manager import get_dual_database_manager
from scripts.refund_process_optimized import RefundProcessor
from utils.logger import get_logger

class DualDatabaseRefundProcessor:
    """双数据库退款处理器"""
    
    def __init__(self):
        self.logger = get_logger('dual_database_refund')
        self.db_manager = get_dual_database_manager()
        self.refund_processor = RefundProcessor()
    
    def get_available_databases(self) -> List[str]:
        """获取可用的数据库列表"""
        return self.db_manager.get_available_databases()
    
    def backup_databases(self, databases: List[str]) -> Dict[str, str]:
        """
        备份指定的数据库
        
        Args:
            databases: 要备份的数据库列表
            
        Returns:
            数据库类型到备份文件路径的映射
        """
        backup_files = {}
        
        for db_type in databases:
            try:
                backup_path = self.db_manager.backup_database(db_type)
                backup_files[db_type] = backup_path
                self.logger.info(f"{db_type}数据库已备份到: {backup_path}")
            except Exception as e:
                self.logger.error(f"备份{db_type}数据库失败: {e}")
                raise Exception(f"备份{db_type}数据库失败: {e}")
        
        return backup_files
    
    def process_refunds(self, refund_file: str, target_databases: List[str] = None,
                       auto_backup: bool = True) -> Dict[str, Any]:
        """
        处理退款文件
        
        Args:
            refund_file: 退款文件路径
            target_databases: 目标数据库列表
            auto_backup: 是否自动备份
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始处理退款文件: {refund_file}")
            
            # 如果没有指定目标数据库，使用所有可用数据库
            if target_databases is None:
                target_databases = self.get_available_databases()
            
            if not target_databases:
                raise Exception("没有可用的数据库")
            
            self.logger.info(f"目标数据库: {target_databases}")
            
            # 自动备份
            backup_files = {}
            if auto_backup:
                self.logger.info("开始备份数据库...")
                backup_files = self.backup_databases(target_databases)
                self.logger.info("数据库备份完成")
            
            # 处理每个数据库
            results = {}
            
            for db_type in target_databases:
                try:
                    self.logger.info(f"处理{db_type}数据库的退款...")
                    
                    # 根据数据库类型设置连接
                    if db_type == 'SQLite':
                        # 使用SQLite连接处理退款
                        result = self._process_refund_sqlite(refund_file)
                    elif db_type == 'PostgreSQL':
                        # 使用PostgreSQL连接处理退款
                        result = self._process_refund_postgresql(refund_file)
                    else:
                        raise Exception(f"不支持的数据库类型: {db_type}")
                    
                    results[db_type] = {
                        'success': True,
                        'result': result,
                        'backup_file': backup_files.get(db_type, '')
                    }
                    
                    self.logger.info(f"{db_type}数据库退款处理成功")
                    
                except Exception as e:
                    self.logger.error(f"{db_type}数据库退款处理失败: {e}")
                    results[db_type] = {
                        'success': False,
                        'error': str(e),
                        'backup_file': backup_files.get(db_type, '')
                    }
            
            # 生成总结果
            success_count = sum(1 for result in results.values() if result['success'])
            overall_success = success_count == len(target_databases)
            
            return {
                'success': overall_success,
                'message': f'退款处理完成: {success_count}/{len(target_databases)} 个数据库成功',
                'results': results,
                'backup_files': backup_files,
                'target_databases': target_databases
            }
            
        except Exception as e:
            self.logger.error(f"退款处理失败: {e}")
            return {
                'success': False,
                'message': f'退款处理失败: {str(e)}',
                'error': str(e)
            }
    
    def _process_refund_sqlite(self, refund_file: str) -> Dict[str, Any]:
        """处理SQLite数据库的退款"""
        # 临时设置数据库路径为SQLite
        original_db_path = self.refund_processor.db_path
        self.refund_processor.db_path = self.db_manager.sqlite_config['db_path']
        
        try:
            # 使用原有的退款处理逻辑
            result = self.refund_processor.process_refund_file(refund_file)
            return result
        finally:
            # 恢复原始数据库路径
            self.refund_processor.db_path = original_db_path
    
    def _process_refund_postgresql(self, refund_file: str) -> Dict[str, Any]:
        """处理PostgreSQL数据库的退款"""
        try:
            # 读取退款文件
            df_refund = pd.read_excel(refund_file)
            
            # 连接PostgreSQL
            conn = self.db_manager.get_postgresql_connection()
            cursor = conn.cursor()
            
            # 处理退款逻辑
            processed_count = 0
            error_count = 0
            
            for _, row in df_refund.iterrows():
                try:
                    order_no = str(row.get('Order_No', ''))
                    if not order_no:
                        continue
                    
                    # 查找订单
                    cursor.execute('''
                        SELECT * FROM "IOT_Sales" WHERE "Order_No" = %s
                        UNION ALL
                        SELECT * FROM "ZERO_Sales" WHERE "Order_No" = %s
                        UNION ALL
                        SELECT * FROM "APP_Sales" WHERE "Order_No" = %s
                    ''', (order_no, order_no, order_no))
                    
                    orders = cursor.fetchall()
                    
                    if orders:
                        # 移动到退款表
                        for order in orders:
                            # 确定源表
                            cursor.execute('SELECT * FROM "IOT_Sales" WHERE "Order_No" = %s', (order_no,))
                            if cursor.fetchone():
                                source_table = 'IOT_Sales'
                                target_table = 'IOT_Sales_Refunding'
                            else:
                                cursor.execute('SELECT * FROM "ZERO_Sales" WHERE "Order_No" = %s', (order_no,))
                                if cursor.fetchone():
                                    source_table = 'ZERO_Sales'
                                    target_table = 'ZERO_Sales_Refunding'
                                else:
                                    source_table = 'APP_Sales'
                                    target_table = 'APP_Sales_Refunding'
                            
                            # 移动数据
                            cursor.execute(f'''
                                INSERT INTO "{target_table}" 
                                SELECT * FROM "{source_table}" WHERE "Order_No" = %s
                            ''', (order_no,))
                            
                            cursor.execute(f'''
                                DELETE FROM "{source_table}" WHERE "Order_No" = %s
                            ''', (order_no,))
                        
                        processed_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    self.logger.error(f"处理订单{order_no}时出错: {e}")
                    error_count += 1
            
            conn.commit()
            conn.close()
            
            return {
                'processed_count': processed_count,
                'error_count': error_count,
                'total_count': len(df_refund)
            }
            
        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            raise e
    
    def get_refund_status(self, databases: List[str] = None) -> Dict[str, Any]:
        """
        获取退款状态统计
        
        Args:
            databases: 要查询的数据库列表
            
        Returns:
            退款状态统计
        """
        if databases is None:
            databases = self.get_available_databases()
        
        status = {}
        
        for db_type in databases:
            try:
                if db_type == 'SQLite':
                    conn = self.db_manager.get_sqlite_connection()
                    status[db_type] = self._get_sqlite_refund_status(conn)
                    conn.close()
                elif db_type == 'PostgreSQL':
                    conn = self.db_manager.get_postgresql_connection()
                    status[db_type] = self._get_postgresql_refund_status(conn)
                    conn.close()
                    
            except Exception as e:
                self.logger.error(f"获取{db_type}退款状态失败: {e}")
                status[db_type] = {'error': str(e)}
        
        return status
    
    def _get_sqlite_refund_status(self, conn) -> Dict[str, int]:
        """获取SQLite退款状态"""
        cursor = conn.cursor()
        status = {}
        
        tables = ['IOT_Sales_Refunding', 'ZERO_Sales_Refunding', 'APP_Sales_Refunding',
                 'IOT_Sales_Close', 'ZERO_Sales_Close', 'APP_Sales_Close']
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                status[table] = count
            except Exception:
                status[table] = 0
        
        return status
    
    def _get_postgresql_refund_status(self, conn) -> Dict[str, int]:
        """获取PostgreSQL退款状态"""
        cursor = conn.cursor()
        status = {}
        
        tables = ['IOT_Sales_Refunding', 'ZERO_Sales_Refunding', 'APP_Sales_Refunding',
                 'IOT_Sales_Close', 'ZERO_Sales_Close', 'APP_Sales_Close']
        
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                count = cursor.fetchone()[0]
                status[table] = count
            except Exception:
                status[table] = 0
        
        return status

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='双数据库退款处理工具')
    parser.add_argument('--refund-file', help='退款文件路径')
    parser.add_argument('--databases', nargs='+', choices=['SQLite', 'PostgreSQL'],
                       help='目标数据库列表，如果不指定则处理所有可用数据库')
    parser.add_argument('--no-backup', action='store_true',
                       help='跳过自动备份')
    parser.add_argument('--status', action='store_true',
                       help='显示退款状态统计')
    
    args = parser.parse_args()
    
    # 创建退款处理器
    processor = DualDatabaseRefundProcessor()
    
    # 如果请求状态信息
    if args.status:
        status = processor.get_refund_status(args.databases)
        print("退款状态统计:")
        for db_type, info in status.items():
            print(f"\n{db_type}:")
            if 'error' in info:
                print(f"  错误: {info['error']}")
            else:
                for table, count in info.items():
                    print(f"  {table}: {count:,} 条记录")
        return 0
    
    # 执行退款处理
    if not args.refund_file:
        print("ERROR: 请指定退款文件路径 (--refund-file)")
        return 1
    
    try:
        result = processor.process_refunds(
            refund_file=args.refund_file,
            target_databases=args.databases,
            auto_backup=not args.no_backup
        )
        
        if result['success']:
            print("SUCCESS: 退款处理成功")
            print(f"文件: {args.refund_file}")
            print(f"目标数据库: {result['target_databases']}")
            
            if result.get('backup_files'):
                print("\n备份文件:")
                for db_type, backup_file in result['backup_files'].items():
                    print(f"  {db_type}: {backup_file}")
            
            print(f"\n处理结果:")
            for db_type, db_result in result['results'].items():
                if db_result['success']:
                    print(f"  {db_type}: 成功")
                    if 'result' in db_result:
                        res = db_result['result']
                        if isinstance(res, dict) and 'processed_count' in res:
                            print(f"    处理数量: {res['processed_count']}")
                            print(f"    错误数量: {res['error_count']}")
                else:
                    print(f"  {db_type}: 失败 - {db_result['error']}")
        else:
            print("ERROR: 退款处理失败")
            print(f"错误: {result['message']}")
            if 'error' in result:
                print(f"详细错误: {result['error']}")
            return 1
    
    except Exception as e:
        print(f"ERROR: 退款处理过程中出现异常: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
