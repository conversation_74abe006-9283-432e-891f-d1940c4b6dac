# -*- coding: utf-8 -*-
"""
快速Unicode编码测试脚本
验证修复后的系统是否解决了Unicode编码问题
"""

import sys
import os

# 设置环境变量避免编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否成功"""
    print("Testing imports...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        print("SUCCESS: DataImportProcessor import successful")
        
        from database.connection_pool import get_connection, reinitialize_connection_pool
        print("SUCCESS: Connection pool import successful")
        
        from database.smart_backup_manager import get_smart_backup_manager
        print("SUCCESS: Smart backup manager import successful")
        
        from utils.logger import get_logger
        print("SUCCESS: Logger import successful")
        
        return True
    except Exception as e:
        print(f"ERROR: Import failed - {e}")
        return False

def test_processor_creation():
    """测试处理器创建"""
    print("\nTesting processor creation...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        # 使用默认数据库路径
        processor = DataImportProcessor()
        print("SUCCESS: DataImportProcessor created successfully")
        print(f"Database path: {processor.db_path}")
        
        return True
    except Exception as e:
        print(f"ERROR: Processor creation failed - {e}")
        return False

def test_status_detection():
    """测试状态检测功能"""
    print("\nTesting status detection...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种状态
        test_cases = [
            ("Finished", "IOT_Sales"),
            ("Payment Successful", "IOT_Sales"),
            ("Refunded", "IOT_Sales_Refunding"),
            ("Order Closed", "IOT_Sales_Close"),
            ("Unknown", "IOT_Sales")
        ]
        
        all_correct = True
        for status, expected in test_cases:
            result = processor._determine_target_table("IOT", status)
            if result == expected:
                print(f"SUCCESS: '{status}' -> {result}")
            else:
                print(f"ERROR: '{status}' -> {result} (expected: {expected})")
                all_correct = False
        
        return all_correct
    except Exception as e:
        print(f"ERROR: Status detection test failed - {e}")
        return False

def test_connection_pool():
    """测试连接池"""
    print("\nTesting connection pool...")
    
    try:
        from database.connection_pool import get_connection, reinitialize_connection_pool
        
        # 重新初始化连接池
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
        reinitialize_connection_pool(db_path)
        print("SUCCESS: Connection pool reinitialized")
        
        # 测试获取连接
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"SUCCESS: Connection test result: {result}")
        
        return True
    except Exception as e:
        print(f"ERROR: Connection pool test failed - {e}")
        return False

def main():
    """主测试函数"""
    print("=== Quick Unicode Fix Test ===")
    print("Testing if Unicode encoding issues are resolved...")
    
    tests = [
        ("Imports", test_imports),
        ("Processor Creation", test_processor_creation),
        ("Status Detection", test_status_detection),
        ("Connection Pool", test_connection_pool)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"Running: {test_name}")
        print(f"{'='*40}")
        
        try:
            if test_func():
                print(f"PASSED: {test_name}")
                passed += 1
            else:
                print(f"FAILED: {test_name}")
        except Exception as e:
            print(f"ERROR in {test_name}: {e}")
    
    print(f"\n{'='*40}")
    print(f"TEST SUMMARY")
    print(f"{'='*40}")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("SUCCESS: All Unicode fixes are working!")
        print("The system should now work without encoding errors.")
        return 0
    else:
        print("WARNING: Some tests failed. Check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
