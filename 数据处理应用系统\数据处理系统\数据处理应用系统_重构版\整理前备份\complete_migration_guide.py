# -*- coding: utf-8 -*-
"""
完整的SQLite到PostgreSQL迁移指南和自动化脚本
包含分析、迁移、测试、验证的完整流程
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any

class MigrationGuide:
    """迁移指南和自动化执行器"""
    
    def __init__(self):
        self.steps = [
            ("准备工作", self.step_preparation),
            ("分析现有数据库", self.step_analyze_database),
            ("安装PostgreSQL依赖", self.step_install_dependencies),
            ("执行数据迁移", self.step_migrate_data),
            ("更新应用配置", self.step_update_config),
            ("测试迁移结果", self.step_test_migration),
            ("验证应用功能", self.step_verify_application),
            ("清理和备份", self.step_cleanup_backup)
        ]
        self.current_step = 0
        self.migration_status = {}
    
    def run_complete_migration(self):
        """运行完整的迁移流程"""
        print("🚀 SQLite到PostgreSQL完整迁移指南")
        print("="*60)
        print("本脚本将引导您完成从SQLite到PostgreSQL的完整迁移过程")
        print("包括数据分析、迁移、配置更新、测试验证等所有步骤")
        print("="*60)
        
        # 确认开始
        if not self._confirm_start():
            print("❌ 迁移已取消")
            return False
        
        # 执行所有步骤
        for i, (step_name, step_func) in enumerate(self.steps):
            self.current_step = i + 1
            print(f"\n📋 步骤 {self.current_step}/{len(self.steps)}: {step_name}")
            print("-" * 50)
            
            try:
                success = step_func()
                self.migration_status[step_name] = {
                    'success': success,
                    'timestamp': datetime.now().isoformat()
                }
                
                if success:
                    print(f"✅ 步骤 {self.current_step} 完成: {step_name}")
                else:
                    print(f"❌ 步骤 {self.current_step} 失败: {step_name}")
                    if not self._ask_continue_on_error():
                        break
            except Exception as e:
                print(f"❌ 步骤 {self.current_step} 出错: {e}")
                self.migration_status[step_name] = {
                    'success': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                if not self._ask_continue_on_error():
                    break
        
        # 生成迁移报告
        self._generate_migration_report()
        
        # 检查整体结果
        successful_steps = sum(1 for status in self.migration_status.values() if status['success'])
        total_steps = len(self.steps)
        
        print(f"\n📊 迁移完成统计:")
        print(f"成功步骤: {successful_steps}/{total_steps}")
        
        if successful_steps == total_steps:
            print("🎉 恭喜！数据库迁移已成功完成！")
            print("\n📋 迁移后检查清单:")
            print("✅ 数据已从SQLite迁移到PostgreSQL")
            print("✅ 应用配置已更新")
            print("✅ 功能测试已通过")
            print("\n🔧 建议的后续操作:")
            print("1. 设置PostgreSQL数据库的定期备份")
            print("2. 监控应用性能和稳定性")
            print("3. 考虑优化PostgreSQL配置")
            print("4. 在确认稳定后删除原SQLite文件")
            return True
        else:
            print("⚠️ 迁移过程中遇到问题，请查看详细报告")
            return False
    
    def step_preparation(self) -> bool:
        """步骤1: 准备工作"""
        print("🔍 检查迁移前提条件...")
        
        # 检查SQLite数据库
        sqlite_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        if not os.path.exists(sqlite_path):
            print(f"❌ SQLite数据库不存在: {sqlite_path}")
            return False
        print(f"✅ SQLite数据库存在: {sqlite_path}")
        
        # 检查Python环境
        try:
            import pandas
            import sqlite3
            print("✅ Python依赖检查通过")
        except ImportError as e:
            print(f"❌ Python依赖缺失: {e}")
            return False
        
        # 创建备份目录
        backup_dir = "migration_backup"
        os.makedirs(backup_dir, exist_ok=True)
        print(f"✅ 备份目录已创建: {backup_dir}")
        
        # 备份原始配置
        if os.path.exists("config.ini"):
            backup_config = f"{backup_dir}/config_original_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ini"
            import shutil
            shutil.copy2("config.ini", backup_config)
            print(f"✅ 原始配置已备份: {backup_config}")
        
        return True
    
    def step_analyze_database(self) -> bool:
        """步骤2: 分析现有数据库"""
        print("📊 分析SQLite数据库结构...")
        
        try:
            # 运行数据库分析器
            result = subprocess.run([
                sys.executable, "database_migration_analyzer.py"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ 数据库分析完成")
                if os.path.exists("migration_analysis.json"):
                    print("✅ 分析报告已生成: migration_analysis.json")
                    return True
                else:
                    print("❌ 分析报告文件未生成")
                    return False
            else:
                print(f"❌ 数据库分析失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 数据库分析超时")
            return False
        except Exception as e:
            print(f"❌ 运行数据库分析器失败: {e}")
            return False
    
    def step_install_dependencies(self) -> bool:
        """步骤3: 安装PostgreSQL依赖"""
        print("📦 安装PostgreSQL Python驱动...")
        
        try:
            # 检查是否已安装
            import psycopg2
            print("✅ psycopg2已安装")
            return True
        except ImportError:
            print("📦 正在安装psycopg2-binary...")
            
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", "psycopg2-binary"
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print("✅ psycopg2-binary安装成功")
                    return True
                else:
                    print(f"❌ 安装失败: {result.stderr}")
                    return False
                    
            except subprocess.TimeoutExpired:
                print("❌ 安装超时")
                return False
            except Exception as e:
                print(f"❌ 安装过程出错: {e}")
                return False
    
    def step_migrate_data(self) -> bool:
        """步骤4: 执行数据迁移"""
        print("🔄 执行数据迁移...")
        
        try:
            # 运行迁移脚本
            result = subprocess.run([
                sys.executable, "postgresql_migrator.py"
            ], capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                print("✅ 数据迁移完成")
                return True
            else:
                print(f"❌ 数据迁移失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 数据迁移超时")
            return False
        except Exception as e:
            print(f"❌ 运行迁移脚本失败: {e}")
            return False
    
    def step_update_config(self) -> bool:
        """步骤5: 更新应用配置"""
        print("🔧 更新应用程序配置...")
        
        # 检查配置文件是否已更新
        if os.path.exists("config.ini"):
            try:
                with open("config.ini", 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "postgresql" in content.lower() or "db_host" in content:
                    print("✅ 配置文件已更新为PostgreSQL")
                    return True
                else:
                    print("❌ 配置文件未更新")
                    return False
                    
            except Exception as e:
                print(f"❌ 读取配置文件失败: {e}")
                return False
        else:
            print("❌ 配置文件不存在")
            return False
    
    def step_test_migration(self) -> bool:
        """步骤6: 测试迁移结果"""
        print("🧪 测试迁移结果...")
        
        try:
            # 测试数据库连接
            from database.postgresql_adapter import test_database_connection, get_database_info
            
            if test_database_connection():
                print("✅ PostgreSQL数据库连接测试通过")
                
                # 显示数据库信息
                db_info = get_database_info()
                print(f"✅ 数据库类型: {db_info['type']}")
                
                return True
            else:
                print("❌ PostgreSQL数据库连接测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 测试数据库连接失败: {e}")
            return False
    
    def step_verify_application(self) -> bool:
        """步骤7: 验证应用功能"""
        print("✅ 验证应用程序功能...")
        
        try:
            # 简单的功能测试
            from database.postgresql_adapter import get_connection
            
            with get_connection() as conn:
                # 测试查询表
                tables = conn.get_table_names()
                print(f"✅ 发现 {len(tables)} 个表")
                
                # 测试数据查询
                for table in tables[:3]:  # 只测试前3个表
                    try:
                        count = conn.get_row_count(table)
                        print(f"✅ 表 {table}: {count} 行")
                    except Exception as e:
                        print(f"⚠️ 表 {table} 查询警告: {e}")
                
                print("✅ 基本功能验证通过")
                return True
                
        except Exception as e:
            print(f"❌ 应用功能验证失败: {e}")
            return False
    
    def step_cleanup_backup(self) -> bool:
        """步骤8: 清理和备份"""
        print("🧹 清理和备份...")
        
        try:
            # 创建迁移完成标记
            completion_file = f"migration_completed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(completion_file, 'w', encoding='utf-8') as f:
                f.write(f"SQLite到PostgreSQL迁移完成\n")
                f.write(f"完成时间: {datetime.now()}\n")
                f.write(f"迁移状态: {json.dumps(self.migration_status, indent=2)}\n")
            
            print(f"✅ 迁移完成标记已创建: {completion_file}")
            
            # 建议备份操作
            print("📋 建议的清理操作:")
            print("1. 验证应用程序运行正常后，可以删除原SQLite数据库文件")
            print("2. 设置PostgreSQL数据库的定期备份")
            print("3. 清理临时迁移文件")
            
            return True
            
        except Exception as e:
            print(f"❌ 清理操作失败: {e}")
            return False
    
    def _confirm_start(self) -> bool:
        """确认开始迁移"""
        print("\n⚠️ 重要提醒:")
        print("1. 迁移过程将修改应用程序配置")
        print("2. 请确保PostgreSQL服务器已安装并运行")
        print("3. 请确保有PostgreSQL数据库的管理权限")
        print("4. 建议在测试环境先进行迁移验证")
        
        response = input("\n是否继续迁移? (y/N): ").strip().lower()
        return response in ['y', 'yes', '是']
    
    def _ask_continue_on_error(self) -> bool:
        """询问是否在错误后继续"""
        response = input("是否继续下一步? (y/N): ").strip().lower()
        return response in ['y', 'yes', '是']
    
    def _generate_migration_report(self):
        """生成迁移报告"""
        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'migration_date': datetime.now().isoformat(),
            'total_steps': len(self.steps),
            'completed_steps': self.current_step,
            'step_status': self.migration_status,
            'success_rate': sum(1 for s in self.migration_status.values() if s['success']) / len(self.migration_status) if self.migration_status else 0
        }
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"✅ 迁移报告已生成: {report_file}")
        except Exception as e:
            print(f"❌ 生成迁移报告失败: {e}")

def main():
    """主函数"""
    guide = MigrationGuide()
    success = guide.run_complete_migration()
    
    if success:
        print("\n🎉 迁移成功完成！")
        return 0
    else:
        print("\n❌ 迁移未完全成功，请查看报告了解详情")
        return 1

if __name__ == "__main__":
    sys.exit(main())
