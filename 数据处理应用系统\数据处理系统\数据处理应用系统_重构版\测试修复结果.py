# -*- coding: utf-8 -*-
"""
测试修复结果脚本
验证Unicode编码问题和列缺失问题的修复效果
"""

import sys
import os
import pandas as pd
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from scripts.data_import_optimized import DataImportProcessor
    from database.connection_pool import get_connection, reinitialize_connection_pool
    print("SUCCESS: All imports successful")
except Exception as e:
    print(f"ERROR: Import failed - {e}")
    sys.exit(1)


def test_unicode_fix():
    """测试Unicode编码修复"""
    print("\n=== Testing Unicode Fix ===")
    
    try:
        # 创建处理器
        processor = DataImportProcessor()
        print("SUCCESS: DataImportProcessor created without Unicode errors")
        
        # 测试连接池初始化
        reinitialize_connection_pool(processor.db_path)
        print("SUCCESS: Connection pool reinitialized without Unicode errors")
        
        return True
    except Exception as e:
        print(f"ERROR: Unicode test failed - {e}")
        return False


def test_missing_columns_fix():
    """测试缺失列处理修复"""
    print("\n=== Testing Missing Columns Fix ===")
    
    try:
        # 创建测试数据（只包含基本列）
        test_data = {
            'Order_price': [100.0, 200.0, 300.0],
            'Order_time': ['2024-12-18 10:00:00', '2024-12-18 10:01:00', '2024-12-18 10:02:00']
            # 故意缺少其他列
        }
        
        df = pd.DataFrame(test_data)
        print(f"Created test data with columns: {list(df.columns)}")
        
        # 创建处理器
        processor = DataImportProcessor()
        
        # 测试缺失列处理
        processed_df = processor._handle_missing_columns(df, "IOT")
        
        print(f"After processing, columns: {list(processed_df.columns)}")
        
        # 验证必要列是否已添加
        expected_columns = ['Order_No', 'Order_status', 'Equipment_ID', 'Equipment_name', 
                          'Branch_name', 'Order_types', 'Transaction_Num', 'Transaction_ID']
        
        missing_columns = [col for col in expected_columns if col not in processed_df.columns]
        
        if missing_columns:
            print(f"ERROR: Still missing columns: {missing_columns}")
            return False
        else:
            print("SUCCESS: All expected columns are present")
            
        # 验证默认值
        print(f"Order_status default values: {processed_df['Order_status'].unique()}")
        print(f"Order_types default values: {processed_df['Order_types'].unique()}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Missing columns test failed - {e}")
        return False


def test_status_detection():
    """测试状态检测功能"""
    print("\n=== Testing Status Detection ===")
    
    try:
        processor = DataImportProcessor()
        
        # 测试各种状态
        test_cases = [
            ("Finished", "IOT_Sales"),
            ("Payment Successful", "IOT_Sales"),
            ("Complete", "IOT_Sales"),
            ("Refunded", "IOT_Sales_Refunding"),
            ("Refund Processing", "IOT_Sales_Refunding"),
            ("Cancel Request", "IOT_Sales_Refunding"),
            ("Closed", "IOT_Sales_Close"),
            ("Order Closed", "IOT_Sales_Close"),
            ("System End", "IOT_Sales_Close"),
            ("Unknown Status", "IOT_Sales"),
            ("", "IOT_Sales")
        ]
        
        platform = "IOT"
        all_correct = True
        
        for status, expected_table in test_cases:
            detected_table = processor._determine_target_table(platform, status)
            if detected_table == expected_table:
                result = "SUCCESS"
            else:
                result = "ERROR"
                all_correct = False
            
            print(f"{result}: '{status}' -> {detected_table} (expected: {expected_table})")
        
        return all_correct
        
    except Exception as e:
        print(f"ERROR: Status detection test failed - {e}")
        return False


def test_data_processing():
    """测试完整的数据处理流程"""
    print("\n=== Testing Complete Data Processing ===")
    
    try:
        # 创建测试Excel文件
        test_file = "test_data.xlsx"
        test_data = {
            'Order_price': [100.0, 200.0, 300.0, 400.0, 500.0],
            'Order_time': ['2024-12-18 10:00:00', '2024-12-18 10:01:00', '2024-12-18 10:02:00', 
                          '2024-12-18 10:03:00', '2024-12-18 10:04:00'],
            'Order_status': ['Finished', 'Payment Success', 'Refunded', 'Order Closed', 'Unknown']
        }
        
        df = pd.DataFrame(test_data)
        df.to_excel(test_file, index=False)
        print(f"Created test file: {test_file}")
        
        # 创建处理器
        processor = DataImportProcessor()
        
        # 测试文件处理
        result = processor.process_file(test_file, "IOT")
        
        if result['success']:
            print("SUCCESS: File processing completed")
            print(f"Total rows: {result['total_rows']}")
            print(f"New rows: {result['new_rows']}")
            print(f"Inserted rows: {result['inserted_rows']}")
            
            if 'table_distribution' in result:
                print("Table distribution:")
                for table, count in result['table_distribution'].items():
                    print(f"  - {table}: {count} records")
            
            if 'table_insert_details' in result:
                print("Insert details:")
                for table, count in result['table_insert_details'].items():
                    print(f"  - {table}: inserted {count} records")
        else:
            print("ERROR: File processing failed")
            for error in result['errors']:
                print(f"  - {error}")
            return False
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"Cleaned up test file: {test_file}")
        
        return result['success']
        
    except Exception as e:
        print(f"ERROR: Data processing test failed - {e}")
        return False


def main():
    """主测试函数"""
    print("=== Testing System Fixes ===")
    print("Testing Unicode encoding fixes and missing columns handling...")
    
    tests = [
        ("Unicode Fix", test_unicode_fix),
        ("Missing Columns Fix", test_missing_columns_fix),
        ("Status Detection", test_status_detection),
        ("Complete Data Processing", test_data_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System fixes are working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
