# -*- coding: utf-8 -*-
"""
测试Unicode修复脚本
验证Excel文件读取和Unicode字符处理
"""

import sys
import os
import pandas as pd

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_unicode_cleaning():
    """测试Unicode字符清理功能"""
    print("Testing Unicode character cleaning...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种特殊字符
        test_strings = [
            "Normal text",
            "Text with – em dash",
            'Text with " quotes "',
            "Text with … ellipsis",
            "Text with non-breaking space",
            "中文字符测试",
            "Mixed 中英文 text",
            "",
            None,
            "nan"
        ]
        
        print("Testing string cleaning:")
        for test_str in test_strings:
            try:
                cleaned = processor._clean_string(test_str)
                print(f"  '{test_str}' -> '{cleaned}'")
            except Exception as e:
                print(f"  ERROR cleaning '{test_str}': {e}")
        
        return True
    except Exception as e:
        print(f"ERROR: Unicode cleaning test failed - {e}")
        return False

def test_dataframe_cleaning():
    """测试DataFrame清理功能"""
    print("\nTesting DataFrame cleaning...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含特殊字符的测试DataFrame
        test_data = {
            'Order_No': ['ORD001', 'ORD002', 'ORD003'],
            'Order_status': ['Finished', 'Payment – Success', 'Order Complete'],
            'Equipment_ID': ['E001', 'E002', 'E003'],
            'Order_price': [100.0, 200.0, 300.0],
            'Notes': ['Normal note', 'Note with … ellipsis', '中文备注']
        }
        
        df = pd.DataFrame(test_data)
        print("Original DataFrame:")
        print(df)
        
        # 清理Unicode字符
        cleaned_df = processor._clean_unicode_characters(df)
        print("\nCleaned DataFrame:")
        print(cleaned_df)
        
        return True
    except Exception as e:
        print(f"ERROR: DataFrame cleaning test failed - {e}")
        return False

def test_excel_reading():
    """测试Excel文件读取（如果有测试文件）"""
    print("\nTesting Excel file reading...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建一个简单的测试Excel文件
        test_file = "test_unicode.xlsx"
        test_data = {
            'Order_price': [100.0, 200.0, 300.0],
            'Order_time': ['2024-12-19 10:00:00', '2024-12-19 10:01:00', '2024-12-19 10:02:00'],
            'Order_status': ['Finished', 'Payment – Success', 'Order Complete'],
            'Equipment_ID': ['E001', 'E002', 'E003'],
            'Notes': ['Normal', 'Special – chars', '中文测试']
        }
        
        df = pd.DataFrame(test_data)
        df.to_excel(test_file, index=False)
        print(f"Created test file: {test_file}")
        
        # 测试读取
        try:
            loaded_df = processor.load_and_validate_data(test_file, "IOT")
            print("Successfully loaded and processed Excel file")
            print(f"Loaded {len(loaded_df)} rows")
            print("Sample data:")
            print(loaded_df.head())
            
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f"Cleaned up test file: {test_file}")
            
            return True
        except Exception as e:
            print(f"ERROR reading Excel file: {e}")
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
            return False
            
    except Exception as e:
        print(f"ERROR: Excel reading test failed - {e}")
        return False

def test_import_process():
    """测试完整的导入处理器创建"""
    print("\nTesting import processor creation...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        # 创建处理器
        processor = DataImportProcessor()
        print("SUCCESS: DataImportProcessor created")
        
        # 测试状态检测
        test_statuses = [
            "Finished",
            "Payment – Success", 
            "Order Complete",
            "Refund…",
            "中文状态"
        ]
        
        print("Testing status detection with special characters:")
        for status in test_statuses:
            try:
                table = processor._determine_target_table("IOT", status)
                print(f"  '{status}' -> {table}")
            except Exception as e:
                print(f"  ERROR with '{status}': {e}")
        
        return True
    except Exception as e:
        print(f"ERROR: Import process test failed - {e}")
        return False

def main():
    """主测试函数"""
    print("=== Unicode Fix Verification Test ===")
    print("Testing Unicode character handling and Excel file processing...")
    
    tests = [
        ("Unicode String Cleaning", test_unicode_cleaning),
        ("DataFrame Cleaning", test_dataframe_cleaning),
        ("Excel File Reading", test_excel_reading),
        ("Import Process", test_import_process)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                print(f"PASSED: {test_name}")
                passed += 1
            else:
                print(f"FAILED: {test_name}")
        except Exception as e:
            print(f"ERROR in {test_name}: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("SUCCESS: All Unicode fixes are working!")
        print("The system should now handle Excel files with special characters.")
        return 0
    else:
        print("WARNING: Some tests failed. Check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
