# -*- coding: utf-8 -*-
"""
修复全局代码脚本
将所有全局执行的代码移到main函数中
"""

import re
import os

def fix_global_code():
    """修复全局执行的代码"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有需要移除的全局执行代码
    global_code_patterns = [
        # 移除全局变量赋值
        r'^# 加载第一文件\ndf1, has_time_column = load_and_validate_file1.*?\n',
        r'^# 检查Transaction ID列是否存在\nif "Transaction ID" not in df1\.columns:.*?log_manager\.log_summary\("Transaction ID列检测完成"\)\n.*?\n',
        r'^# 智能处理日期时间列.*?\ndf1 = process_datetime_columns\(df1, has_time_column\)\n',
        r'^# 验证文件日期一致性\nvalidate_file_dates\(df1, file_config\[\'file2_path\'\]\)\n',
        r'^# 加载第二文件\ndf2 = load_and_process_file2.*?\n',
        r'^# 添加API order类型排除条件.*?\n.*?\.copy\(\)\n',
        r'^# 计算第一文件总金额.*?\ntotal_bill_amt = .*?\n',
        r'^# 智能检测Transaction Num匹配能力.*?\nmode = .*?\n',
        r'^# 创建统一数据处理器.*?\nprocessor = .*?\n',
        r'^# 按日期分组处理.*?\ndate_groups = .*?\n',
        r'^# 处理每个日期组.*?\n.*?processor\.process_.*?\n',
        r'^# 执行数据恢复和补全.*?\nprocessor\.recover_data\(\)\n',
        r'^# 显示处理摘要.*?\nprint_processing_summary.*?\n',
        r'^# 保存结果.*?\nsave_results.*?\n',
    ]
    
    # 移除所有全局执行代码
    modified_content = content
    removal_count = 0
    
    # 简单的方法：找到所有以#开头的注释后跟代码的模式
    lines = modified_content.split('\n')
    new_lines = []
    skip_until_function = False
    
    for i, line in enumerate(lines):
        # 如果遇到函数定义，停止跳过
        if line.strip().startswith('def ') or line.strip().startswith('class '):
            skip_until_function = False
        
        # 如果遇到main_processing_flow函数定义，停止跳过
        if 'def main_processing_flow' in line:
            skip_until_function = False
        
        # 如果遇到if __name__ == "__main__"，停止跳过
        if 'if __name__ == "__main__"' in line:
            skip_until_function = False
        
        # 检查是否是需要移除的全局执行代码
        if not skip_until_function:
            # 检查是否是全局执行的代码行
            if (line.strip().startswith('# 加载第一文件') or
                line.strip().startswith('# 检查Transaction ID列') or
                line.strip().startswith('# 智能处理日期时间列') or
                line.strip().startswith('# 验证文件日期一致性') or
                line.strip().startswith('# 加载第二文件') or
                line.strip().startswith('# 添加API order类型排除条件') or
                line.strip().startswith('# 计算第一文件总金额') or
                line.strip().startswith('# 智能检测Transaction Num匹配能力') or
                line.strip().startswith('# 创建统一数据处理器') or
                line.strip().startswith('# 按日期分组处理') or
                line.strip().startswith('# 处理每个日期组') or
                line.strip().startswith('# 执行数据恢复和补全') or
                line.strip().startswith('# 显示处理摘要') or
                line.strip().startswith('# 保存结果')):
                skip_until_function = True
                removal_count += 1
                continue
        
        # 如果正在跳过，并且不是函数/类定义，则跳过这行
        if skip_until_function:
            if (not line.strip().startswith('def ') and 
                not line.strip().startswith('class ') and
                not line.strip().startswith('if __name__') and
                'def main_processing_flow' not in line):
                continue
            else:
                skip_until_function = False
        
        new_lines.append(line)
    
    modified_content = '\n'.join(new_lines)
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"✅ 全局代码修复完成！")
    print(f"移除了 {removal_count} 个全局执行代码块")

def test_syntax():
    """测试语法是否正确"""
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        import ast
        ast.parse(content)
        print("✅ 语法检查通过！")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: 行{e.lineno}: {e.msg}")
        if e.text:
            print(f"代码: {e.text.strip()}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("开始修复全局代码...")
    
    # 1. 修复全局执行代码
    fix_global_code()
    
    # 2. 测试语法
    if test_syntax():
        print("\n🎉 修复完成！")
        print("现在脚本应该不会在导入时执行，只有在main函数中才会执行。")
    else:
        print("\n⚠️ 仍有语法错误需要手动修复。")
