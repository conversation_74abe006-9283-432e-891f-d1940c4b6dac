# -*- coding: utf-8 -*-
"""
智能迁移用户界面
提供图形化的迁移操作界面
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_migration import IntelligentMigrator
from postgresql_manager import PostgreSQLManager

class MigrationUI:
    """迁移用户界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("智能数据迁移工具")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.sqlite_db_path = tk.StringVar()
        self.selected_tables = []
        self.migration_running = False
        
        # 创建界面
        self.create_widgets()
        
        # 初始化PostgreSQL管理器
        self.pg_manager = PostgreSQLManager()
        
        # 刷新状态
        self.refresh_status()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="智能数据迁移工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # PostgreSQL状态区域
        status_frame = ttk.LabelFrame(main_frame, text="PostgreSQL状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="检查中...")
        self.status_label.grid(row=0, column=0, columnspan=2, sticky=tk.W)
        
        ttk.Button(status_frame, text="刷新状态", command=self.refresh_status).grid(row=0, column=2, padx=(10, 0))
        ttk.Button(status_frame, text="启动PostgreSQL", command=self.start_postgresql).grid(row=1, column=0, pady=(5, 0))
        ttk.Button(status_frame, text="停止PostgreSQL", command=self.stop_postgresql).grid(row=1, column=1, pady=(5, 0))
        ttk.Button(status_frame, text="安装PostgreSQL", command=self.install_postgresql).grid(row=1, column=2, pady=(5, 0))
        
        # SQLite数据库选择
        db_frame = ttk.LabelFrame(main_frame, text="SQLite数据库", padding="10")
        db_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        db_frame.columnconfigure(1, weight=1)
        
        ttk.Label(db_frame, text="数据库路径:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(db_frame, textvariable=self.sqlite_db_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5))
        ttk.Button(db_frame, text="浏览", command=self.browse_sqlite_db).grid(row=0, column=2)
        
        # 表选择区域
        table_frame = ttk.LabelFrame(main_frame, text="选择要迁移的表", padding="10")
        table_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(1, weight=1)
        
        # 表选择按钮
        button_frame = ttk.Frame(table_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(button_frame, text="全选", command=self.select_all_tables).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="全不选", command=self.deselect_all_tables).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新表列表", command=self.refresh_table_list).pack(side=tk.LEFT, padx=(0, 5))
        
        # 表列表
        self.table_listbox = tk.Listbox(table_frame, selectmode=tk.MULTIPLE, height=8)
        self.table_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.table_listbox.yview)
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.table_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 迁移选项
        options_frame = ttk.LabelFrame(main_frame, text="迁移选项", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.backup_var = tk.BooleanVar(value=True)
        self.verify_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="迁移前备份PostgreSQL", variable=self.backup_var).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="迁移后验证数据", variable=self.verify_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 操作按钮
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0))
        
        self.migrate_button = ttk.Button(action_frame, text="开始迁移", command=self.start_migration)
        self.migrate_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(action_frame, text="查看报告", command=self.view_reports).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="配置设置", command=self.open_config).pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="迁移日志", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 配置网格权重
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(7, weight=1)
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def refresh_status(self):
        """刷新PostgreSQL状态"""
        try:
            status = self.pg_manager.get_status()
            
            if not status['installed']:
                self.status_label.config(text="❌ PostgreSQL未安装", foreground="red")
            elif not status['running']:
                self.status_label.config(text="⚠️ PostgreSQL已安装但未运行", foreground="orange")
            elif not status['connection']:
                self.status_label.config(text="⚠️ PostgreSQL运行中但连接失败", foreground="orange")
            else:
                self.status_label.config(text="✅ PostgreSQL运行正常", foreground="green")
                
        except Exception as e:
            self.status_label.config(text=f"❌ 状态检查失败: {e}", foreground="red")
    
    def start_postgresql(self):
        """启动PostgreSQL"""
        def start_thread():
            self.log_message("正在启动PostgreSQL...")
            if self.pg_manager.start_server():
                self.log_message("✅ PostgreSQL启动成功")
            else:
                self.log_message("❌ PostgreSQL启动失败")
            self.refresh_status()
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_postgresql(self):
        """停止PostgreSQL"""
        def stop_thread():
            self.log_message("正在停止PostgreSQL...")
            if self.pg_manager.stop_server():
                self.log_message("✅ PostgreSQL停止成功")
            else:
                self.log_message("❌ PostgreSQL停止失败")
            self.refresh_status()
        
        threading.Thread(target=stop_thread, daemon=True).start()
    
    def install_postgresql(self):
        """安装PostgreSQL"""
        response = messagebox.askyesno("安装PostgreSQL", 
                                     "这将下载并安装便携式PostgreSQL，可能需要几分钟时间。\n\n确认继续？")
        if response:
            def install_thread():
                self.log_message("开始安装便携式PostgreSQL...")
                try:
                    from postgresql_setup import PostgreSQLPortableInstaller
                    installer = PostgreSQLPortableInstaller()
                    if installer.install():
                        self.log_message("✅ PostgreSQL安装成功")
                        self.refresh_status()
                    else:
                        self.log_message("❌ PostgreSQL安装失败")
                except Exception as e:
                    self.log_message(f"❌ 安装异常: {e}")
            
            threading.Thread(target=install_thread, daemon=True).start()
    
    def browse_sqlite_db(self):
        """浏览SQLite数据库文件"""
        filename = filedialog.askopenfilename(
            title="选择SQLite数据库文件",
            filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
        )
        if filename:
            self.sqlite_db_path.set(filename)
            self.refresh_table_list()
    
    def refresh_table_list(self):
        """刷新表列表"""
        self.table_listbox.delete(0, tk.END)
        
        db_path = self.sqlite_db_path.get()
        if not db_path or not os.path.exists(db_path):
            return
        
        try:
            migrator = IntelligentMigrator(db_path)
            tables = migrator.get_table_list()
            
            for table in tables:
                self.table_listbox.insert(tk.END, table)
                
            self.log_message(f"发现 {len(tables)} 个支持的表")
            
        except Exception as e:
            self.log_message(f"刷新表列表失败: {e}")
    
    def select_all_tables(self):
        """全选表"""
        self.table_listbox.select_set(0, tk.END)
    
    def deselect_all_tables(self):
        """全不选表"""
        self.table_listbox.selection_clear(0, tk.END)
    
    def get_selected_tables(self) -> List[str]:
        """获取选中的表"""
        selected_indices = self.table_listbox.curselection()
        return [self.table_listbox.get(i) for i in selected_indices]
    
    def start_migration(self):
        """开始迁移"""
        if self.migration_running:
            messagebox.showwarning("警告", "迁移正在进行中，请等待完成")
            return
        
        # 验证输入
        db_path = self.sqlite_db_path.get()
        if not db_path or not os.path.exists(db_path):
            messagebox.showerror("错误", "请选择有效的SQLite数据库文件")
            return
        
        selected_tables = self.get_selected_tables()
        if not selected_tables:
            messagebox.showerror("错误", "请至少选择一个表进行迁移")
            return
        
        # 确认迁移
        response = messagebox.askyesno("确认迁移", 
                                     f"确认迁移以下表到PostgreSQL？\n\n{', '.join(selected_tables)}\n\n"
                                     f"备份: {'是' if self.backup_var.get() else '否'}\n"
                                     f"验证: {'是' if self.verify_var.get() else '否'}")
        if not response:
            return
        
        # 开始迁移线程
        def migration_thread():
            self.migration_running = True
            self.migrate_button.config(state='disabled')
            self.progress_var.set(0)
            
            try:
                self.log_message("开始智能数据迁移...")
                
                # 创建迁移器
                migrator = IntelligentMigrator(db_path)
                
                # 更新配置
                migrator.migration_config['backup_before_migration'] = self.backup_var.get()
                migrator.migration_config['verify_after_migration'] = self.verify_var.get()
                
                # 执行迁移
                success = migrator.migrate_all_tables(selected_tables)
                
                self.progress_var.set(100)
                
                if success:
                    self.log_message("✅ 迁移完成！")
                    messagebox.showinfo("成功", "数据迁移完成！")
                else:
                    self.log_message("❌ 迁移失败！")
                    messagebox.showerror("失败", "数据迁移失败，请查看日志")
                
            except Exception as e:
                self.log_message(f"❌ 迁移异常: {e}")
                messagebox.showerror("错误", f"迁移过程中发生异常：{e}")
            
            finally:
                self.migration_running = False
                self.migrate_button.config(state='normal')
        
        threading.Thread(target=migration_thread, daemon=True).start()
    
    def view_reports(self):
        """查看迁移报告"""
        reports_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "migration_logs"
        if reports_dir.exists():
            os.startfile(str(reports_dir))
        else:
            messagebox.showinfo("信息", "还没有生成迁移报告")
    
    def open_config(self):
        """打开配置文件"""
        config_file = Path(os.path.dirname(os.path.abspath(__file__))) / "migration_config.json"
        if config_file.exists():
            os.startfile(str(config_file))
        else:
            messagebox.showinfo("信息", "配置文件不存在")

def main():
    """主函数"""
    root = tk.Tk()
    app = MigrationUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
