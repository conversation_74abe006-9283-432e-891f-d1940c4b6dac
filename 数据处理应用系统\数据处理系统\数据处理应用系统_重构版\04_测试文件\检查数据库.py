# -*- coding: utf-8 -*-
"""
检查数据库状态和导入结果
"""

import sqlite3
import pandas as pd
import os

def check_database():
    """检查数据库状态"""
    db_path = 'database/sales_reports.db'
    
    if not os.path.exists(db_path):
        print("Database does not exist")
        return
    
    print(f"Database exists: {db_path}")
    print(f"Database size: {os.path.getsize(db_path)} bytes")
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 检查表是否存在
        tables = pd.read_sql("SELECT name FROM sqlite_master WHERE type='table'", conn)
        print(f"\nAvailable tables ({len(tables)}):")
        for table in tables['name']:
            print(f"  - {table}")
        
        # 检查IOT_Sales表的结构
        if 'IOT_Sales' in tables['name'].values:
            print("\nIOT_Sales table schema:")
            schema = pd.read_sql("PRAGMA table_info(IOT_Sales)", conn)
            for _, row in schema.iterrows():
                print(f"  {row['name']}: {row['type']}")
            
            # 检查数据
            count = pd.read_sql('SELECT COUNT(*) as count FROM IOT_Sales', conn).iloc[0]['count']
            print(f"\nIOT_Sales data count: {count}")
            
            if count > 0:
                data = pd.read_sql('SELECT * FROM IOT_Sales LIMIT 5', conn)
                print("\nSample data:")
                print(data)
        
        # 检查其他表
        for table_name in ['IOT_Sales_Refunding', 'IOT_Sales_Close']:
            if table_name in tables['name'].values:
                count = pd.read_sql(f'SELECT COUNT(*) as count FROM {table_name}', conn).iloc[0]['count']
                print(f"\n{table_name} data count: {count}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error checking database: {e}")

def main():
    """主函数"""
    print("=== Database Status Check ===")
    check_database()

if __name__ == "__main__":
    main()
