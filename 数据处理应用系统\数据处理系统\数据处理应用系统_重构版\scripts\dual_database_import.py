# -*- coding: utf-8 -*-
"""
双数据库数据导入脚本
支持同时导入到SQLite和PostgreSQL数据库
"""

import os
import sys
import argparse
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional

# 设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.dual_database_manager import get_dual_database_manager, import_to_databases
try:
    from scripts.data_import_optimized import DataImportProcessor
except ImportError:
    # 如果没有DataImportProcessor，创建一个简化版本
    class DataImportProcessor:
        def validate_file(self, file_path):
            return os.path.exists(file_path)

        def load_and_validate_data(self, file_path, platform):
            import pandas as pd
            return pd.read_excel(file_path)

        def check_duplicate_data(self, df, platform):
            # 简化版本：假设所有数据都是新数据
            return pd.DataFrame(), pd.DataFrame(), df

try:
    from utils.logger import get_logger
except ImportError:
    # 如果没有logger，使用print
    def get_logger(name):
        class SimpleLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
        return SimpleLogger()

class DualDatabaseImporter:
    """双数据库导入器"""
    
    def __init__(self):
        self.logger = get_logger('dual_database_import')
        self.db_manager = get_dual_database_manager()
        self.processor = DataImportProcessor()
    
    def get_available_databases(self) -> List[str]:
        """获取可用的数据库列表"""
        return self.db_manager.get_available_databases()
    
    def import_file(self, file_path: str, platform: str, 
                   target_databases: List[str] = None, 
                   order_type: str = 'all') -> Dict[str, Any]:
        """
        导入文件到指定数据库
        
        Args:
            file_path: 文件路径
            platform: 平台类型 (IOT, ZERO, APP)
            target_databases: 目标数据库列表，如果为None则导入到所有可用数据库
            order_type: 订单类型过滤
            
        Returns:
            导入结果字典
        """
        try:
            self.logger.info(f"开始导入文件: {file_path}")
            self.logger.info(f"平台: {platform}, 订单类型: {order_type}")
            
            # 如果没有指定目标数据库，使用所有可用数据库
            if target_databases is None:
                target_databases = self.get_available_databases()
            
            if not target_databases:
                raise Exception("没有可用的数据库")
            
            self.logger.info(f"目标数据库: {target_databases}")
            
            # 验证文件
            self.processor.validate_file(file_path)
            
            # 加载和处理数据
            df = self.processor.load_and_validate_data(file_path, platform)
            
            if df.empty:
                return {
                    'success': False,
                    'message': '文件中没有有效数据',
                    'results': {}
                }
            
            # 过滤订单类型
            if order_type != 'all':
                original_count = len(df)
                df = df[df['Order_types'].str.contains(order_type, case=False, na=False)]
                filtered_count = len(df)
                self.logger.info(f"订单类型过滤: {original_count} -> {filtered_count} 行")
            
            # 检查重复数据（基于第一个数据库）
            if target_databases:
                # 使用原有的重复检查逻辑
                duplicates, partial_duplicates, new_data = self.processor.check_duplicate_data(df, platform)
                
                self.logger.info(f"数据分析: 总行数={len(df)}, 新数据={len(new_data)}, "
                               f"完全重复={len(duplicates)}, 部分重复={len(partial_duplicates)}")
                
                if new_data.empty:
                    return {
                        'success': True,
                        'message': '没有新数据需要导入',
                        'results': {db: True for db in target_databases},
                        'stats': {
                            'total_rows': len(df),
                            'new_rows': 0,
                            'duplicate_rows': len(duplicates),
                            'partial_duplicate_rows': len(partial_duplicates)
                        }
                    }
                
                df_to_import = new_data
            else:
                df_to_import = df
            
            # 智能表分配
            table_assignments = self._assign_tables_by_status(df_to_import, platform)
            
            # 导入到各个数据库
            import_results = {}
            total_imported = 0
            
            for table_name, table_df in table_assignments.items():
                if table_df.empty:
                    continue
                
                self.logger.info(f"导入到表 {table_name}: {len(table_df)} 行")
                
                # 导入到多个数据库
                results = import_to_databases(table_df, table_name, target_databases)
                
                for db_type, success in results.items():
                    if db_type not in import_results:
                        import_results[db_type] = {'tables': {}, 'success': True}
                    
                    import_results[db_type]['tables'][table_name] = {
                        'success': success,
                        'rows': len(table_df)
                    }
                    
                    if not success:
                        import_results[db_type]['success'] = False
                
                total_imported += len(table_df)
            
            # 生成结果报告
            success_count = sum(1 for result in import_results.values() if result['success'])
            overall_success = success_count == len(target_databases)
            
            result = {
                'success': overall_success,
                'message': f'导入完成: {success_count}/{len(target_databases)} 个数据库成功',
                'results': import_results,
                'stats': {
                    'total_rows': len(df),
                    'new_rows': len(df_to_import),
                    'actually_imported': total_imported,
                    'target_databases': target_databases
                },
                'table_distribution': {
                    table_name: len(table_df) 
                    for table_name, table_df in table_assignments.items() 
                    if not table_df.empty
                }
            }
            
            self.logger.info(f"导入结果: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"导入失败: {e}")
            return {
                'success': False,
                'message': f'导入失败: {str(e)}',
                'results': {},
                'error': str(e)
            }
    
    def _assign_tables_by_status(self, df: pd.DataFrame, platform: str) -> Dict[str, pd.DataFrame]:
        """
        根据订单状态智能分配到不同表
        
        Args:
            df: 数据框
            platform: 平台类型
            
        Returns:
            表名到数据框的映射
        """
        assignments = {}
        
        # 主表 - 完成状态的订单
        main_table = f"{platform}_Sales"
        main_df = df[df['Order_status'].isin(['Finished', 'Completed', 'Success', ''])].copy()
        assignments[main_table] = main_df
        
        # 退款表 - 退款状态的订单
        refunding_table = f"{platform}_Sales_Refunding"
        refunding_df = df[df['Order_status'].isin(['Refunded', 'Refunding', 'Cancelled'])].copy()
        assignments[refunding_table] = refunding_df
        
        # 关闭表 - 关闭状态的订单
        close_table = f"{platform}_Sales_Close"
        close_df = df[df['Order_status'].isin(['Closed', 'Failed', 'Expired'])].copy()
        assignments[close_table] = close_df
        
        # 记录分配统计
        self.logger.info(f"表分配统计:")
        self.logger.info(f"  {main_table}: {len(main_df)} 行")
        self.logger.info(f"  {refunding_table}: {len(refunding_df)} 行")
        self.logger.info(f"  {close_table}: {len(close_df)} 行")
        
        return assignments
    
    def get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        return self.db_manager.get_database_status()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='双数据库数据导入工具')
    parser.add_argument('--file', help='要导入的Excel文件路径')
    parser.add_argument('--platform', choices=['IOT', 'ZERO', 'APP'],
                       help='平台类型')
    parser.add_argument('--databases', nargs='+', choices=['SQLite', 'PostgreSQL'],
                       help='目标数据库列表，如果不指定则导入到所有可用数据库')
    parser.add_argument('--order-type', default='all',
                       help='订单类型过滤 (默认: all)')
    parser.add_argument('--status', action='store_true',
                       help='显示数据库状态')

    args = parser.parse_args()

    # 创建导入器
    importer = DualDatabaseImporter()

    # 如果请求状态信息
    if args.status:
        status = importer.get_database_status()
        print("数据库状态:")
        for db_type, info in status.items():
            print(f"\n{db_type}:")
            print(f"  启用: {info['enabled']}")
            print(f"  连接: {info['connected']}")
            if info['connected'] and info['tables']:
                print(f"  表数量: {len(info['tables'])}")
                total_rows = sum(table['rows'] for table in info['tables'])
                print(f"  总行数: {total_rows:,}")
        return 0

    # 检查必需参数
    if not args.file or not args.platform:
        parser.error("--file 和 --platform 参数是必需的（除非使用 --status）")
    
    # 执行导入
    try:
        result = importer.import_file(
            file_path=args.file,
            platform=args.platform,
            target_databases=args.databases,
            order_type=args.order_type
        )
        
        if result['success']:
            print("SUCCESS: 数据导入成功")
            print(f"文件: {args.file}")
            print(f"平台: {args.platform}")
            print(f"总行数: {result['stats']['total_rows']}")
            print(f"新数据: {result['stats']['new_rows']}")
            print(f"实际导入: {result['stats']['actually_imported']}")
            
            if 'table_distribution' in result:
                print("\n数据分布:")
                for table_name, count in result['table_distribution'].items():
                    print(f"  {table_name}: {count} 条记录")
            
            print(f"\n数据库结果:")
            for db_type, db_result in result['results'].items():
                status = "成功" if db_result['success'] else "失败"
                print(f"  {db_type}: {status}")
        else:
            print("ERROR: 数据导入失败")
            print(f"错误: {result['message']}")
            if 'error' in result:
                print(f"详细错误: {result['error']}")
            return 1
    
    except Exception as e:
        print(f"ERROR: 导入过程中出现异常: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
