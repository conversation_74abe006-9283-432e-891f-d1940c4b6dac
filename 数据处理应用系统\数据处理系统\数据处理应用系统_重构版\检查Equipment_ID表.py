# -*- coding: utf-8 -*-
"""
检查哪些表包含Equipment_ID字段
"""

import sqlite3
import psycopg2

def check_equipment_id_tables():
    """检查包含Equipment_ID的表"""
    
    # 数据库配置
    sqlite_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'
    pg_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    print("🔍 检查包含Equipment_ID的表...")
    print("=" * 80)
    
    try:
        # 连接SQLite
        sqlite_conn = sqlite3.connect(sqlite_path)
        sqlite_cursor = sqlite_conn.cursor()
        
        # 连接PostgreSQL
        pg_conn = psycopg2.connect(**pg_config)
        pg_cursor = pg_conn.cursor()
        
        # 获取SQLite所有表
        sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        sqlite_tables = [row[0] for row in sqlite_cursor.fetchall()]
        
        # 获取PostgreSQL所有表
        pg_cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        pg_tables = [row[0] for row in pg_cursor.fetchall()]
        
        print(f"📊 SQLite总表数: {len(sqlite_tables)}")
        print(f"📊 PostgreSQL总表数: {len(pg_tables)}")
        print()
        
        # 检查SQLite中包含Equipment_ID的表
        print("🔍 SQLite中包含Equipment_ID的表:")
        print("-" * 60)
        
        sqlite_equipment_tables = []
        
        for table_name in sorted(sqlite_tables):
            try:
                # 检查表结构
                sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in sqlite_cursor.fetchall()]
                
                if 'Equipment_ID' in columns:
                    # 检查数据
                    sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    total_count = sqlite_cursor.fetchone()[0]
                    
                    sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE Equipment_ID IS NOT NULL AND Equipment_ID != ''")
                    equipment_count = sqlite_cursor.fetchone()[0]
                    
                    sqlite_equipment_tables.append(table_name)
                    
                    if equipment_count > 0:
                        print(f"✅ {table_name:<30} {equipment_count:>8,}/{total_count:>8,} 条Equipment_ID记录")
                    else:
                        print(f"⚪ {table_name:<30} {equipment_count:>8,}/{total_count:>8,} 条Equipment_ID记录 (空)")
                        
            except Exception as e:
                print(f"❌ {table_name:<30} 检查失败: {e}")
        
        print()
        print("🔍 PostgreSQL中包含Equipment_ID的表:")
        print("-" * 60)
        
        pg_equipment_tables = []
        
        for table_name in sorted(pg_tables):
            try:
                # 检查表结构
                pg_cursor.execute("""
                    SELECT column_name FROM information_schema.columns 
                    WHERE table_name = %s AND column_name = 'Equipment_ID'
                """, (table_name,))
                
                if pg_cursor.fetchone():
                    # 检查数据
                    pg_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                    total_count = pg_cursor.fetchone()[0]
                    
                    pg_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}" WHERE "Equipment_ID" IS NOT NULL AND "Equipment_ID" != \'\'')
                    equipment_count = pg_cursor.fetchone()[0]
                    
                    pg_equipment_tables.append(table_name)
                    
                    if equipment_count > 0:
                        print(f"✅ {table_name:<30} {equipment_count:>8,}/{total_count:>8,} 条Equipment_ID记录")
                    else:
                        print(f"⚪ {table_name:<30} {equipment_count:>8,}/{total_count:>8,} 条Equipment_ID记录 (空)")
                        
            except Exception as e:
                print(f"❌ {table_name:<30} 检查失败: {e}")
        
        print()
        print("📊 汇总统计:")
        print("-" * 60)
        print(f"SQLite包含Equipment_ID的表: {len(sqlite_equipment_tables)} 个")
        print(f"PostgreSQL包含Equipment_ID的表: {len(pg_equipment_tables)} 个")
        
        # 比较两个数据库的Equipment_ID表
        sqlite_set = set(sqlite_equipment_tables)
        pg_set = set(pg_equipment_tables)
        
        common_tables = sqlite_set & pg_set
        sqlite_only = sqlite_set - pg_set
        pg_only = pg_set - sqlite_set
        
        print(f"共同的Equipment_ID表: {len(common_tables)} 个")
        if common_tables:
            print(f"  {', '.join(sorted(common_tables))}")
        
        if sqlite_only:
            print(f"仅SQLite有的Equipment_ID表: {len(sqlite_only)} 个")
            print(f"  {', '.join(sorted(sqlite_only))}")
        
        if pg_only:
            print(f"仅PostgreSQL有的Equipment_ID表: {len(pg_only)} 个")
            print(f"  {', '.join(sorted(pg_only))}")
        
        print()
        print("🛡️ 受保护的表 (不包含Equipment_ID的sales表):")
        print("-" * 60)
        
        protected_tables = []
        for table_name in sorted(sqlite_tables):
            if table_name not in sqlite_equipment_tables:
                if 'sales' in table_name.lower() or 'Sales' in table_name:
                    try:
                        sqlite_cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                        count = sqlite_cursor.fetchone()[0]
                        protected_tables.append((table_name, count))
                        print(f"🛡️ {table_name:<30} {count:>8,} 行 (受保护)")
                    except Exception as e:
                        print(f"❌ {table_name:<30} 检查失败: {e}")
        
        print(f"\n✅ 总计受保护的sales表: {len(protected_tables)} 个")
        
        sqlite_conn.close()
        pg_conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_equipment_id_tables()
    input("\n按回车键退出...")
