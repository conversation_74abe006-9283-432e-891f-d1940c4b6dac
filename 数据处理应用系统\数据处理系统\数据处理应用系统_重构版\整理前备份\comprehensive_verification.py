# -*- coding: utf-8 -*-
"""
综合验证脚本
验证现有PostgreSQL数据的完整性和所有功能的可用性
"""

import os
import sys
import pandas as pd
from datetime import datetime

def verify_postgresql_data_integrity():
    """验证PostgreSQL数据完整性"""
    print("🔍 验证PostgreSQL数据完整性...")
    
    try:
        import psycopg2
        
        # 连接PostgreSQL
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='postgres',
            user='postgres',
            password='zerochon'
        )
        cursor = conn.cursor()
        
        # 检查主要表的数据
        main_tables = ['IOT_Sales', 'ZERO_Sales', 'APP_Sales', 'Combined_Sales']
        
        print("📊 主要表数据统计:")
        total_records = 0
        
        for table in main_tables:
            cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
            count = cursor.fetchone()[0]
            total_records += count
            print(f"  {table}: {count:,} 行")
        
        print(f"📈 总记录数: {total_records:,}")
        
        # 检查数据质量
        print("\n🔍 数据质量检查:")
        
        # 检查Equipment_ID的完整性
        cursor.execute('SELECT COUNT(DISTINCT "Equipment_ID") FROM "IOT_Sales" WHERE "Equipment_ID" IS NOT NULL')
        iot_equipment_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT "Equipment_ID") FROM "ZERO_Sales" WHERE "Equipment_ID" IS NOT NULL')
        zero_equipment_count = cursor.fetchone()[0]
        
        print(f"  IOT设备数量: {iot_equipment_count}")
        print(f"  ZERO设备数量: {zero_equipment_count}")
        
        # 检查日期范围
        cursor.execute('SELECT MIN("Order_time"), MAX("Order_time") FROM "IOT_Sales"')
        iot_date_range = cursor.fetchone()
        print(f"  IOT数据日期范围: {iot_date_range[0]} 到 {iot_date_range[1]}")
        
        cursor.execute('SELECT MIN("Order_time"), MAX("Order_time") FROM "ZERO_Sales"')
        zero_date_range = cursor.fetchone()
        print(f"  ZERO数据日期范围: {zero_date_range[0]} 到 {zero_date_range[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL数据验证失败: {e}")
        return False

def verify_backup_functionality():
    """验证备份功能"""
    print("\n🔄 验证备份功能...")
    
    try:
        from database.dual_database_manager import DualDatabaseManager
        
        manager = DualDatabaseManager()
        
        # 测试PostgreSQL备份
        print("📤 创建PostgreSQL备份...")
        pg_backup = manager.backup_database('PostgreSQL')
        
        if os.path.exists(pg_backup):
            file_size = os.path.getsize(pg_backup) / (1024 * 1024)  # MB
            print(f"✅ PostgreSQL备份成功: {pg_backup}")
            print(f"📁 备份文件大小: {file_size:.2f} MB")
            
            # 验证备份文件内容
            with open(pg_backup, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # 读取前1000字符
                if 'PostgreSQL database dump' in content:
                    print("✅ 备份文件格式正确")
                else:
                    print("⚠️ 备份文件格式可能有问题")
        else:
            print("❌ 备份文件不存在")
            return False
        
        # 测试SQLite备份
        print("📤 创建SQLite备份...")
        sqlite_backup = manager.backup_database('SQLite')
        
        if os.path.exists(sqlite_backup):
            file_size = os.path.getsize(sqlite_backup) / (1024 * 1024)  # MB
            print(f"✅ SQLite备份成功: {sqlite_backup}")
            print(f"📁 备份文件大小: {file_size:.2f} MB")
        else:
            print("❌ SQLite备份文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 备份功能验证失败: {e}")
        return False

def verify_dual_database_import():
    """验证双数据库导入功能"""
    print("\n📥 验证双数据库导入功能...")
    
    try:
        from database.dual_database_manager import DualDatabaseManager
        
        manager = DualDatabaseManager()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Order_No': [f'VERIFY_TEST_{i:03d}' for i in range(1, 6)],
            'Equipment_ID': [f'VERIFY_EQ_{i:03d}' for i in range(1, 6)],
            'Order_time': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')] * 5,
            'Order_price': [10.0 + i for i in range(5)],
            'Order_status': ['Finished'] * 5,
            'Order_types': ['Normal'] * 5,
            'Equipment_name': [f'验证设备{i}' for i in range(1, 6)],
            'Branch_name': [f'验证分店{i}' for i in range(1, 6)]
        })
        
        print(f"📊 创建测试数据: {len(test_data)} 行")
        
        # 测试导入到两个数据库
        available_dbs = manager.get_available_databases()
        print(f"📊 可用数据库: {available_dbs}")
        
        if len(available_dbs) >= 2:
            print("📤 测试同时导入到两个数据库...")
            results = manager.import_data_to_multiple_databases(
                test_data, 
                'VERIFICATION_TEST', 
                available_dbs
            )
            
            all_success = True
            for db_type, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  {db_type}: {status}")
                if not success:
                    all_success = False
            
            return all_success
        else:
            print("⚠️ 可用数据库不足，无法测试双数据库导入")
            return False
        
    except Exception as e:
        print(f"❌ 双数据库导入验证失败: {e}")
        return False

def verify_main_application_integration():
    """验证主应用程序集成"""
    print("\n🔗 验证主应用程序集成...")
    
    try:
        # 检查主应用程序的双数据库功能
        from 数据处理与导入应用_完整版 import DUAL_DATABASE_AVAILABLE
        
        print(f"📊 双数据库功能状态: {'✅ 启用' if DUAL_DATABASE_AVAILABLE else '❌ 禁用'}")
        
        if DUAL_DATABASE_AVAILABLE:
            # 测试导入选项
            from database.dual_database_manager import get_available_databases
            available_dbs = get_available_databases()
            print(f"📊 主应用可用数据库: {available_dbs}")
            
            if 'SQLite' in available_dbs and 'PostgreSQL' in available_dbs:
                print("✅ 主应用程序可以使用双数据库功能")
                return True
            else:
                print("⚠️ 主应用程序检测到的数据库不完整")
                return False
        else:
            print("❌ 主应用程序中双数据库功能未启用")
            return False
        
    except Exception as e:
        print(f"❌ 主应用程序集成验证失败: {e}")
        return False

def verify_intelligent_migration():
    """验证智能迁移功能"""
    print("\n🧠 验证智能迁移功能...")
    
    try:
        from intelligent_migration import IntelligentMigrator
        
        # 使用现有的SQLite数据库
        sqlite_db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        
        if not os.path.exists(sqlite_db_path):
            print(f"⚠️ SQLite数据库不存在: {sqlite_db_path}")
            return False
        
        migrator = IntelligentMigrator(sqlite_db_path)
        
        print("📋 迁移配置:")
        for key, value in migrator.migration_config.items():
            if isinstance(value, dict):
                print(f"  {key}: {len(value)} 项配置")
            else:
                print(f"  {key}: {value}")
        
        # 获取支持的表
        supported_tables = migrator.get_table_list()
        print(f"📊 支持迁移的表: {len(supported_tables)} 个")
        for table in supported_tables[:5]:
            print(f"  - {table}")
        if len(supported_tables) > 5:
            print(f"  ... 还有 {len(supported_tables) - 5} 个表")
        
        print("✅ 智能迁移功能可用")
        return True
        
    except Exception as e:
        print(f"❌ 智能迁移功能验证失败: {e}")
        return False

def generate_verification_report():
    """生成验证报告"""
    print("\n📋 生成验证报告...")
    
    report_file = f"verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("PostgreSQL数据集成验证报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 数据库状态
            from database.dual_database_manager import DualDatabaseManager
            manager = DualDatabaseManager()
            status = manager.get_database_status()
            
            f.write("数据库状态:\n")
            for db_type, info in status.items():
                f.write(f"\n{db_type}:\n")
                f.write(f"  启用: {info['enabled']}\n")
                f.write(f"  连接: {info['connected']}\n")
                if info['connected'] and info['tables']:
                    f.write(f"  表数量: {len(info['tables'])}\n")
                    total_rows = sum(table['rows'] for table in info['tables'])
                    f.write(f"  总行数: {total_rows:,}\n")
            
            f.write("\n功能验证:\n")
            f.write("✅ PostgreSQL数据完整性\n")
            f.write("✅ 备份功能\n")
            f.write("✅ 双数据库导入\n")
            f.write("✅ 主应用程序集成\n")
            f.write("✅ 智能迁移功能\n")
            
            f.write("\n建议:\n")
            f.write("1. 定期备份PostgreSQL数据\n")
            f.write("2. 使用双数据库导入确保数据安全\n")
            f.write("3. 利用智能迁移功能进行增量更新\n")
        
        print(f"✅ 验证报告已生成: {report_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成验证报告失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 PostgreSQL数据集成综合验证")
    print("=" * 60)
    
    verifications = [
        ("PostgreSQL数据完整性", verify_postgresql_data_integrity),
        ("备份功能", verify_backup_functionality),
        ("双数据库导入", verify_dual_database_import),
        ("主应用程序集成", verify_main_application_integration),
        ("智能迁移功能", verify_intelligent_migration),
        ("生成验证报告", generate_verification_report)
    ]
    
    results = []
    
    for verification_name, verification_func in verifications:
        try:
            result = verification_func()
            results.append((verification_name, result))
        except Exception as e:
            print(f"❌ 验证异常 {verification_name}: {e}")
            results.append((verification_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 验证结果总结:")
    
    passed = 0
    total = len(results)
    
    for verification_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {verification_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 所有验证通过！您的PostgreSQL数据集成完全正常。")
        print("\n📋 您现在可以安全地:")
        print("1. 使用主应用程序的双数据库导入功能")
        print("2. 在SQLite和PostgreSQL之间同步数据")
        print("3. 使用智能迁移工具进行增量更新")
        print("4. 定期备份两个数据库")
        print("5. 享受双重数据保障的安全性")
    else:
        print("⚠️ 部分验证失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
