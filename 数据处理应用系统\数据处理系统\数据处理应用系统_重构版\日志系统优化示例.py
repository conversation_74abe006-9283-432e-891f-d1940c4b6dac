# -*- coding: utf-8 -*-
"""
日志系统优化示例
实现用户需求：纯中文界面显示 + 详细日志保存到文件
"""

import os
from datetime import datetime

class LogManager:
    """日志管理器 - 分离界面显示和详细日志"""
    
    def __init__(self):
        self.detailed_logs = []  # 详细日志
        self.summary_logs = []   # 摘要日志
        self.log_file_path = None
        
    def set_log_file(self, file_path: str):
        """设置日志文件路径"""
        log_dir = os.path.dirname(file_path)
        self.log_file_path = os.path.join(log_dir, "processing_log.txt")
        
    def log_detailed(self, message: str, level: str = "INFO"):
        """记录详细日志（保存到文件）"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.detailed_logs.append(log_entry)
        
        # 实时写入文件
        if self.log_file_path:
            try:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    f.write(log_entry + "\n")
            except:
                pass
    
    def log_summary(self, message: str):
        """记录摘要日志（显示在界面）"""
        self.summary_logs.append(message)
        print(message)
        
    def clear_log_file(self):
        """清空日志文件"""
        if self.log_file_path:
            try:
                with open(self.log_file_path, 'w', encoding='utf-8') as f:
                    f.write(f"=== 数据处理日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n\n")
            except:
                pass

# 使用示例
def main():
    """演示日志系统的使用"""
    
    # 初始化日志管理器
    log_manager = LogManager()
    log_manager.set_log_file("C:/Users/<USER>/Desktop/test_file.xlsx")
    log_manager.clear_log_file()
    
    # 模拟数据处理过程
    print("=" * 50)
    print("数据处理系统 - 日志优化演示")
    print("=" * 50)
    
    # 1. 开始处理
    log_manager.log_detailed("系统初始化完成")
    log_manager.log_detailed("第一文件路径: C:/Users/<USER>/Desktop/June/ZERO/TRANSACTION_17 TO_19062025_ZERO.xlsx")
    log_manager.log_detailed("第二文件路径: C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx")
    log_manager.log_summary("开始数据处理...")
    
    # 2. 文件加载
    log_manager.log_detailed("第一文件列名: ['Date', 'Time', 'Transaction ID', 'Order ID', 'Bill Amt', ...]")
    log_manager.log_detailed("第一文件列数: 13")
    log_manager.log_detailed("检测到独立的Time列")
    log_manager.log_detailed("找到'Transaction ID'列，将用于匹配")
    log_manager.log_summary("文件加载完成: 第一文件 27 条记录，第二文件 28 条记录")
    
    # 3. Transaction ID分析
    log_manager.log_detailed("开始智能检测Transaction Num匹配能力...")
    log_manager.log_detailed("检测到 Transaction Num 列")
    log_manager.log_detailed("第二文件总记录数: 28")
    log_manager.log_detailed("第二文件Transaction Num非空记录数: 28")
    log_manager.log_detailed("第二文件Transaction Num唯一值数量: 28")
    log_manager.log_detailed("第二文件Transaction Num填充率: 100.0%")
    log_manager.log_detailed("第一文件有效Transaction ID数量: 27")
    log_manager.log_detailed("第二文件有效Transaction Num数量: 28")
    log_manager.log_detailed("可匹配的Transaction ID/Num数量: 26")
    log_manager.log_detailed("匹配率: 96.3%")
    log_manager.log_summary("Transaction ID分析完成: 96.3% 匹配能力")
    
    # 4. 数据处理
    log_manager.log_detailed("设置匹配模式为: Transaction ID匹配")
    log_manager.log_detailed("开始按日期分组处理...")
    log_manager.log_detailed("发现 1 个日期分组")
    log_manager.log_detailed("处理日期: 2025-06-17 (27 条记录)")
    
    # 模拟处理每个Transaction ID
    for i, trans_id in enumerate(['2936662760', '2936728235', '2936768998'], 1):
        log_manager.log_detailed(f"处理Transaction ID {trans_id}，找到 1 条匹配记录")
        log_manager.log_detailed(f"记录 {28-i} 标记设置成功: Matched_Flag = True")
        log_manager.log_detailed(f"Transaction ID {trans_id} 匹配统计: 成功 1, 失败 0")
    
    log_manager.log_summary("数据匹配进行中: 已处理 26 条匹配，1 条插入")
    
    # 5. 处理结果
    log_manager.log_detailed("处理完成，耗时: 0.05秒")
    log_manager.log_detailed("transaction_id 模式统计:")
    log_manager.log_detailed("  处理: 27 条")
    log_manager.log_detailed("  匹配: 26 条")
    log_manager.log_detailed("  插入: 1 条")
    log_manager.log_detailed("验证 - 实际匹配的记录数量: 26")
    log_manager.log_detailed("验证 - DataFrame中实际标记数量: 27")
    log_manager.log_detailed("第一文件总金额（包含所有settled记录）: RM200.00")
    log_manager.log_detailed("第二文件总金额（排除API订单）: RM200.00")
    log_manager.log_detailed("金额差异（第一文件无法排除API订单）: RM0.00")
    log_manager.log_summary("处理结果: 匹配 26 条，插入 1 条，总计 27 条")
    log_manager.log_summary("金额验证: RM200.00 完全匹配")
    
    # 6. 数据恢复
    log_manager.log_detailed("开始执行数据恢复...")
    log_manager.log_detailed("Transaction Num修复: 0 条")
    log_manager.log_detailed("Equipment信息恢复: 1 条")
    log_manager.log_detailed("Order No.信息恢复: 0 条")
    log_manager.log_summary("数据恢复完成: 1 条设备信息已恢复")
    
    # 7. 保存结果
    log_manager.log_detailed("处理完成，结果已保存 C:/Users/<USER>/Desktop/June/ZERO/170625 CHINA ZERO - Copy.xlsx")
    log_manager.log_detailed("- 数据已保存 DATA sheet")
    log_manager.log_detailed("- 日志已保存 LOG sheet")
    log_manager.log_detailed("第二文件最终总金额: RM200.00")
    log_manager.log_detailed("第一文件总金额: RM200.00")
    log_manager.log_detailed("金额差异: RM0.00")
    log_manager.log_detailed("脚本执行完成")
    log_manager.log_summary("结果已保存到文件")
    log_manager.log_summary("详细日志已保存到: processing_log.txt")
    
    print("=" * 50)
    log_manager.log_summary("处理完成！")
    print("=" * 50)
    
    print(f"\n详细日志文件位置: {log_manager.log_file_path}")
    print("界面显示的是简化信息，完整处理过程请查看日志文件。")

if __name__ == "__main__":
    main()
