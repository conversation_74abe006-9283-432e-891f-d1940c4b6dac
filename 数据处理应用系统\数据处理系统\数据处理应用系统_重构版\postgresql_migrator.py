# -*- coding: utf-8 -*-
"""
SQLite到PostgreSQL数据迁移器
执行完整的数据库迁移过程
"""

import sqlite3
import psycopg2
import pandas as pd
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PostgreSQLMigrator:
    """PostgreSQL迁移器"""
    
    def __init__(self, sqlite_db_path: str, pg_config: Dict[str, str]):
        self.sqlite_db_path = sqlite_db_path
        self.pg_config = pg_config
        self.migration_log = []
        
    def migrate_database(self, analysis_file: str = "migration_analysis.json") -> bool:
        """执行完整的数据库迁移"""
        logger.info("🚀 开始数据库迁移...")
        
        try:
            # 1. 加载分析结果
            if not os.path.exists(analysis_file):
                logger.error(f"❌ 分析文件不存在: {analysis_file}")
                return False
            
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis = json.load(f)
            
            # 2. 连接PostgreSQL
            pg_conn = self._connect_postgresql()
            if not pg_conn:
                return False
            
            # 3. 创建表结构
            if not self._create_tables(pg_conn, analysis['tables']):
                pg_conn.close()
                return False
            
            # 4. 迁移数据
            if not self._migrate_data(pg_conn, analysis['tables']):
                pg_conn.close()
                return False
            
            # 5. 创建视图
            if not self._create_views(pg_conn, analysis['views']):
                pg_conn.close()
                return False
            
            # 6. 创建索引
            if not self._create_indexes(pg_conn, analysis['indexes']):
                pg_conn.close()
                return False
            
            # 7. 验证迁移结果
            if not self._verify_migration(pg_conn, analysis):
                pg_conn.close()
                return False
            
            pg_conn.close()
            logger.info("✅ 数据库迁移完成！")
            self._save_migration_log()
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移过程中出错: {e}")
            return False
    
    def _connect_postgresql(self) -> Optional[psycopg2.extensions.connection]:
        """连接PostgreSQL数据库"""
        try:
            logger.info("🔌 连接PostgreSQL数据库...")
            conn = psycopg2.connect(
                host=self.pg_config['host'],
                port=self.pg_config['port'],
                database=self.pg_config['database'],
                user=self.pg_config['user'],
                password=self.pg_config['password']
            )
            conn.autocommit = True
            logger.info("✅ PostgreSQL连接成功")
            return conn
        except Exception as e:
            logger.error(f"❌ PostgreSQL连接失败: {e}")
            return None
    
    def _create_tables(self, pg_conn: psycopg2.extensions.connection, 
                      tables: Dict[str, Any]) -> bool:
        """创建表结构"""
        logger.info("📊 创建表结构...")
        
        try:
            cursor = pg_conn.cursor()
            
            for table_name, table_info in tables.items():
                logger.info(f"  创建表: {table_name}")
                
                # 使用PostgreSQL等效的CREATE语句
                create_sql = table_info['postgresql_equivalent']
                
                # 删除表如果存在
                cursor.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE")
                
                # 创建表
                cursor.execute(create_sql)
                
                self.migration_log.append(f"表 {table_name} 创建成功")
                logger.info(f"    ✅ 表 {table_name} 创建成功")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建表失败: {e}")
            return False
    
    def _migrate_data(self, pg_conn: psycopg2.extensions.connection, 
                     tables: Dict[str, Any]) -> bool:
        """迁移数据"""
        logger.info("📦 迁移数据...")
        
        try:
            # 连接SQLite
            sqlite_conn = sqlite3.connect(self.sqlite_db_path)
            pg_cursor = pg_conn.cursor()
            
            for table_name, table_info in tables.items():
                logger.info(f"  迁移表数据: {table_name}")
                
                # 从SQLite读取数据
                df = pd.read_sql_query(f"SELECT * FROM {table_name}", sqlite_conn)
                
                if df.empty:
                    logger.info(f"    表 {table_name} 无数据")
                    continue
                
                # 准备插入语句
                columns = [col['name'] for col in table_info['columns']]
                placeholders = ', '.join(['%s'] * len(columns))
                insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                
                # 批量插入数据
                batch_size = 1000
                total_rows = len(df)
                
                for i in range(0, total_rows, batch_size):
                    batch_df = df.iloc[i:i+batch_size]
                    
                    # 转换数据类型
                    batch_data = []
                    for _, row in batch_df.iterrows():
                        row_data = []
                        for col_name in columns:
                            value = row[col_name] if col_name in row else None
                            # 处理None值和数据类型转换
                            if pd.isna(value):
                                value = None
                            elif isinstance(value, (pd.Timestamp, datetime)):
                                value = value.strftime('%Y-%m-%d %H:%M:%S')
                            row_data.append(value)
                        batch_data.append(tuple(row_data))
                    
                    # 执行批量插入
                    pg_cursor.executemany(insert_sql, batch_data)
                    
                    logger.info(f"    已迁移 {min(i+batch_size, total_rows)}/{total_rows} 行")
                
                self.migration_log.append(f"表 {table_name} 数据迁移完成: {total_rows} 行")
                logger.info(f"    ✅ 表 {table_name} 数据迁移完成: {total_rows} 行")
            
            sqlite_conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据迁移失败: {e}")
            return False
    
    def _create_views(self, pg_conn: psycopg2.extensions.connection, 
                     views: Dict[str, Any]) -> bool:
        """创建视图"""
        if not views:
            logger.info("👁️ 无视图需要创建")
            return True
        
        logger.info("👁️ 创建视图...")
        
        try:
            cursor = pg_conn.cursor()
            
            for view_name, view_info in views.items():
                logger.info(f"  创建视图: {view_name}")
                
                # 删除视图如果存在
                cursor.execute(f"DROP VIEW IF EXISTS {view_name} CASCADE")
                
                # 创建视图
                create_sql = view_info['postgresql_equivalent']
                cursor.execute(create_sql)
                
                self.migration_log.append(f"视图 {view_name} 创建成功")
                logger.info(f"    ✅ 视图 {view_name} 创建成功")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建视图失败: {e}")
            return False
    
    def _create_indexes(self, pg_conn: psycopg2.extensions.connection, 
                       indexes: Dict[str, Any]) -> bool:
        """创建索引"""
        if not indexes:
            logger.info("🔍 无索引需要创建")
            return True
        
        logger.info("🔍 创建索引...")
        
        try:
            cursor = pg_conn.cursor()
            
            for index_name, index_info in indexes.items():
                logger.info(f"  创建索引: {index_name}")
                
                # 删除索引如果存在
                cursor.execute(f"DROP INDEX IF EXISTS {index_name}")
                
                # 创建索引
                create_sql = index_info['postgresql_equivalent']
                cursor.execute(create_sql)
                
                self.migration_log.append(f"索引 {index_name} 创建成功")
                logger.info(f"    ✅ 索引 {index_name} 创建成功")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建索引失败: {e}")
            return False
    
    def _verify_migration(self, pg_conn: psycopg2.extensions.connection, 
                         analysis: Dict[str, Any]) -> bool:
        """验证迁移结果"""
        logger.info("🔍 验证迁移结果...")
        
        try:
            cursor = pg_conn.cursor()
            sqlite_conn = sqlite3.connect(self.sqlite_db_path)
            
            verification_passed = True
            
            for table_name, table_info in analysis['tables'].items():
                # 检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, (table_name,))
                
                if not cursor.fetchone()[0]:
                    logger.error(f"❌ 表 {table_name} 不存在")
                    verification_passed = False
                    continue
                
                # 检查行数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                pg_count = cursor.fetchone()[0]
                
                sqlite_cursor = sqlite_conn.cursor()
                sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                sqlite_count = sqlite_cursor.fetchone()[0]
                
                if pg_count != sqlite_count:
                    logger.error(f"❌ 表 {table_name} 行数不匹配: PostgreSQL={pg_count}, SQLite={sqlite_count}")
                    verification_passed = False
                else:
                    logger.info(f"    ✅ 表 {table_name} 验证通过: {pg_count} 行")
            
            sqlite_conn.close()
            
            if verification_passed:
                logger.info("✅ 迁移验证通过")
                self.migration_log.append("迁移验证通过")
            else:
                logger.error("❌ 迁移验证失败")
                self.migration_log.append("迁移验证失败")
            
            return verification_passed
            
        except Exception as e:
            logger.error(f"❌ 验证迁移失败: {e}")
            return False
    
    def _save_migration_log(self):
        """保存迁移日志"""
        log_file = f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("SQLite到PostgreSQL迁移日志\n")
                f.write("="*50 + "\n")
                f.write(f"迁移时间: {datetime.now()}\n")
                f.write(f"源数据库: {self.sqlite_db_path}\n")
                f.write(f"目标数据库: {self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}\n")
                f.write("\n迁移步骤:\n")
                for log_entry in self.migration_log:
                    f.write(f"- {log_entry}\n")
            
            logger.info(f"✅ 迁移日志已保存到: {log_file}")
        except Exception as e:
            logger.error(f"❌ 保存迁移日志失败: {e}")

def create_postgresql_config() -> Dict[str, str]:
    """创建PostgreSQL配置"""
    print("🔧 配置PostgreSQL连接信息")
    print("请输入PostgreSQL数据库连接信息:")
    
    config = {}
    config['host'] = input("主机地址 (默认: localhost): ").strip() or 'localhost'
    config['port'] = input("端口 (默认: 5432): ").strip() or '5432'
    config['database'] = input("数据库名称: ").strip()
    config['user'] = input("用户名: ").strip()
    config['password'] = input("密码: ").strip()
    
    return config

def update_application_config(pg_config: Dict[str, str]):
    """更新应用程序配置以使用PostgreSQL"""
    print("\n🔧 更新应用程序配置...")

    # 更新config.ini
    config_file = "config.ini"
    if os.path.exists(config_file):
        try:
            # 读取现有配置
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 备份原配置
            backup_file = f"config_sqlite_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ini"
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 原配置已备份到: {backup_file}")

            # 更新数据库配置
            new_content = content.replace(
                "[Database]",
                f"""[Database]
# PostgreSQL配置
db_type = postgresql
db_host = {pg_config['host']}
db_port = {pg_config['port']}
db_name = {pg_config['database']}
db_user = {pg_config['user']}
db_password = {pg_config['password']}
# 原SQLite配置（已备份）"""
            )

            # 保存新配置
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(new_content)

            print(f"  ✅ 配置文件已更新: {config_file}")

        except Exception as e:
            print(f"  ❌ 更新配置文件失败: {e}")
    else:
        print(f"  ⚠️ 配置文件不存在: {config_file}")

def main():
    """主函数"""
    print("🚀 SQLite到PostgreSQL数据库迁移器")
    print("="*50)

    # SQLite数据库路径
    sqlite_db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

    # 检查SQLite数据库是否存在
    if not os.path.exists(sqlite_db_path):
        print(f"❌ SQLite数据库不存在: {sqlite_db_path}")
        return

    # 获取PostgreSQL配置
    pg_config = create_postgresql_config()

    if not all(pg_config.values()):
        print("❌ PostgreSQL配置不完整")
        return

    # 创建迁移器
    migrator = PostgreSQLMigrator(sqlite_db_path, pg_config)

    # 执行迁移
    success = migrator.migrate_database()

    if success:
        # 更新应用程序配置
        update_application_config(pg_config)

        print("\n🎉 数据库迁移成功完成！")
        print("\n📋 后续步骤:")
        print("1. 安装PostgreSQL驱动: pip install psycopg2-binary")
        print("2. 重启应用程序以使用新的数据库配置")
        print("3. 测试所有功能是否正常工作")
        print("4. 设置PostgreSQL数据库的定期备份")
        print("5. 考虑删除原SQLite数据库文件（建议先保留一段时间）")
    else:
        print("\n❌ 数据库迁移失败，请查看日志了解详情")

if __name__ == "__main__":
    main()
