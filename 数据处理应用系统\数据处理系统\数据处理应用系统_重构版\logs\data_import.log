2025-06-19 08:49:40 - data_import - INFO - All database table structures created/verified
2025-06-19 08:49:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:49:40 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 08:49:40 - data_import - INFO - All database table structures created/verified
2025-06-19 08:49:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:49:40 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 08:49:40 - data_import - INFO - All database table structures created/verified
2025-06-19 08:49:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:49:40 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 08:49:40 - data_import - INFO - Successfully read file: test_unicode.xlsx, rows: 3
2025-06-19 08:49:40 - data_import - INFO - Added missing columns: ['Order_No', 'Equipment_name', 'Branch_name', 'Order_types', 'Transaction_Num', 'Transaction_ID', 'Payment_date']
2025-06-19 08:49:40 - data_import - INFO - Data validation and cleaning completed, valid rows: 3
2025-06-19 08:49:40 - data_import - INFO - All database table structures created/verified
2025-06-19 08:49:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:49:40 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 08:49:40 - data_import - INFO - Smart match status 'Payment – Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:49:40 - data_import - INFO - Smart match status 'Order Complete' -> IOT_Sales (category: FINISHED)
2025-06-19 08:49:40 - data_import - INFO - Smart match status 'Refund…' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:49:40 - data_import - WARNING - Unrecognized order status '中文状态', using default table: IOT_Sales
2025-06-19 08:54:43 - data_import - INFO - All database table structures created/verified
2025-06-19 08:54:43 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:54:43 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 08:54:43 - data_import - INFO - Starting smart file processing: test_unicode_excel.xlsx, platform: IOT
2025-06-19 08:54:43 - data_import - INFO - Successfully read file: test_unicode_excel.xlsx, rows: 5
2025-06-19 08:54:43 - data_import - INFO - Added missing columns: ['Order_No', 'Transaction_ID', 'Payment_date']
2025-06-19 08:54:43 - data_import - INFO - Data validation and cleaning completed, valid rows: 5
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:54:43 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 08:54:43 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:54:43 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 08:54:43 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 08:54:43 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:54:43 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 08:54:43 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Notes
2025-06-19 08:54:45 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Notes
2025-06-19 08:55:42 - data_import - INFO - All database table structures created/verified
2025-06-19 08:55:42 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:55:42 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 08:55:42 - data_import - INFO - Starting smart file processing: test_unicode_excel.xlsx, platform: IOT
2025-06-19 08:55:43 - data_import - INFO - Successfully read file: test_unicode_excel.xlsx, rows: 5
2025-06-19 08:55:43 - data_import - INFO - Data validation and cleaning completed, valid rows: 5
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:55:43 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 08:55:43 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:55:43 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 08:55:43 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 08:55:43 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 08:55:43 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 08:55:43 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 08:55:45 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:07:47 - data_import - INFO - All database table structures created/verified
2025-06-19 09:07:47 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:07:47 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:07:47 - data_import - INFO - Starting smart file processing: test_unicode_excel.xlsx, platform: IOT
2025-06-19 09:07:52 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: utils.exceptions.AppException.__init__() got multiple values for keyword argument 'details'
2025-06-19 09:08:46 - data_import - INFO - All database table structures created/verified
2025-06-19 09:08:46 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:08:46 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:08:46 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 09:08:46 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:08:46 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:08:46 - data_import - INFO - All database table structures created/verified
2025-06-19 09:08:46 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:08:46 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:08:46 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 09:08:46 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:08:46 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:13:13 - data_import - INFO - All database table structures created/verified
2025-06-19 09:13:13 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:13:13 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:13:13 - data_import - INFO - Starting smart file processing: test_unicode_excel.xlsx, platform: IOT
2025-06-19 09:13:13 - data_import - INFO - Successfully read file: test_unicode_excel.xlsx, rows: 5
2025-06-19 09:13:13 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:13:13 - data_import - INFO - Data validation and cleaning completed, valid rows: 5
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 09:13:13 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 09:13:13 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 09:13:13 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 09:13:13 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 09:13:13 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 09:13:13 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 09:13:13 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:13:16 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:15:31 - data_import - INFO - All database table structures created/verified
2025-06-19 09:15:31 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:15:31 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:15:31 - data_import - INFO - Starting smart file processing: test_unicode_excel.xlsx, platform: IOT
2025-06-19 09:15:31 - data_import - INFO - Successfully read file: test_unicode_excel.xlsx, rows: 5
2025-06-19 09:15:31 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:15:31 - data_import - INFO - Data validation and cleaning completed, valid rows: 5
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 09:15:31 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 09:15:31 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 09:15:31 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 09:15:31 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 4, 'IOT_Sales_Refunding': 1}
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Payment - Success' -> IOT_Sales (category: FINISHED)
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Order "Complete"' -> IOT_Sales (category: FINISHED)
2025-06-19 09:15:31 - data_import - INFO - Smart match status 'Refund...' -> IOT_Sales_Refunding (category: REFUNDING)
2025-06-19 09:15:31 - data_import - WARNING - Unrecognized order status 'Processing', using default table: IOT_Sales
2025-06-19 09:15:31 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:15:35 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:38:08 - data_import - INFO - All database table structures created/verified
2025-06-19 09:38:08 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:38:08 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:38:08 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 09:38:08 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:38:08 - data_import - INFO - All database table structures created/verified
2025-06-19 09:38:08 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:38:08 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:38:08 - data_import - INFO - Added missing columns: ['Copartner_name', 'Order_No', 'Order_types', 'Order_status', 'Order_price', 'Payment', 'Order_time', 'Equipment_ID', 'Equipment_name', 'Branch_name', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 09:38:08 - data_import - INFO - All database table structures created/verified
2025-06-19 09:38:08 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:38:08 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 09:38:08 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 16:35:20 - data_import - INFO - All database table structures created/verified
2025-06-19 16:35:20 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:35:20 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:35:57 - data_import - INFO - All database table structures created/verified
2025-06-19 16:35:57 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:35:57 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:56:07 - data_import - INFO - All database table structures created/verified
2025-06-19 16:56:07 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:56:07 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:56:36 - data_import - INFO - All database table structures created/verified
2025-06-19 16:56:36 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:56:36 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:56:36 - data_import - INFO - Successfully read file: test_import.xlsx, rows: 2
2025-06-19 16:56:36 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 16:56:36 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 16:56:36 - data_import - INFO - Data validation and cleaning completed, valid rows: 2
2025-06-19 16:56:49 - data_import - INFO - All database table structures created/verified
2025-06-19 16:56:49 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:56:49 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:56:49 - data_import - INFO - Successfully read file: test_import.xlsx, rows: 2
2025-06-19 16:56:49 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 16:56:49 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 16:56:49 - data_import - INFO - Data validation and cleaning completed, valid rows: 2
2025-06-19 17:03:09 - data_import - INFO - All database table structures created/verified
2025-06-19 17:03:09 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:03:09 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:03:10 - data_import - INFO - Successfully read file: test_import.xlsx, rows: 2
2025-06-19 17:03:10 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:03:10 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 17:03:10 - data_import - INFO - Data validation and cleaning completed, valid rows: 2
2025-06-19 17:09:36 - data_import - INFO - All database table structures created/verified
2025-06-19 17:09:36 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:09:36 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:09:36 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:09:36 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:09:36 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:09:36 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:09:36 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:09:36 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:10:24 - data_import - INFO - All database table structures created/verified
2025-06-19 17:10:24 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:10:24 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:10:24 - data_import - INFO - Successfully read file: test_import.xlsx, rows: 2
2025-06-19 17:10:24 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:10:24 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 17:10:24 - data_import - INFO - Data validation and cleaning completed, valid rows: 2
2025-06-19 17:16:10 - data_import - INFO - All database table structures created/verified
2025-06-19 17:16:10 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:16:10 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:16:27 - data_import - INFO - All database table structures created/verified
2025-06-19 17:16:27 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:16:27 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:16:27 - data_import - INFO - Successfully read file: test_import.xlsx, rows: 2
2025-06-19 17:16:27 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:16:27 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 17:16:27 - data_import - INFO - Data validation and cleaning completed, valid rows: 2
2025-06-19 17:16:45 - data_import - INFO - All database table structures created/verified
2025-06-19 17:16:45 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:16:45 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:16:46 - data_import - INFO - Successfully read file: test_import.xlsx, rows: 2
2025-06-19 17:16:46 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:16:46 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 17:16:46 - data_import - INFO - Data validation and cleaning completed, valid rows: 2
2025-06-19 17:22:21 - data_import - INFO - All database table structures created/verified
2025-06-19 17:22:21 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:22:21 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:22:21 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:22:21 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO130625', 'LOG']
2025-06-19 17:22:21 - data_import - INFO - Found matching sheet for ZERO: ZERO130625
2025-06-19 17:22:22 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:22:22 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:22:22 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:22:22 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:22:22 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:22:22 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:22:22 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:22:22 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-19 17:22:22 - data_import - INFO - Data distribution analysis: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:22:22 - data_import - INFO - Table ZERO_Sales: inserted 99 records
2025-06-19 17:22:22 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 2 records
2025-06-19 17:22:22 - data_import - INFO - Smart insert completed, total inserted: 101 records
2025-06-19 17:22:22 - data_import - INFO - ZERO_Sales: inserted 99 records
2025-06-19 17:22:22 - data_import - INFO - ZERO_Sales_Refunding: inserted 2 records
2025-06-19 17:22:22 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx
2025-06-19 17:30:56 - data_import - INFO - All database table structures created/verified
2025-06-19 17:30:56 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:30:56 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:30:56 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:30:56 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO130625', 'LOG']
2025-06-19 17:30:56 - data_import - INFO - Found matching sheet for ZERO: ZERO130625
2025-06-19 17:30:56 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:30:56 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:30:56 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:30:56 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:30:56 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:30:56 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:30:56 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:30:56 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-19 17:30:56 - data_import - INFO - Regular data distribution analysis: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:30:56 - data_import - INFO - Table ZERO_Sales: inserted 99 records
2025-06-19 17:30:56 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 2 records
2025-06-19 17:30:56 - data_import - INFO - Smart insert completed, total inserted: 101 records (including 0 API orders)
2025-06-19 17:30:56 - data_import - INFO - ZERO_Sales: inserted 99 records
2025-06-19 17:30:56 - data_import - INFO - ZERO_Sales_Refunding: inserted 2 records
2025-06-19 17:30:56 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx
2025-06-19 17:31:50 - data_import - INFO - All database table structures created/verified
2025-06-19 17:31:50 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:31:50 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:31:50 - data_import - INFO - Starting smart file processing: test_smart_detection.xlsx, platform: ZERO
2025-06-19 17:31:50 - data_import - INFO - Available sheets: ['Sheet1']
2025-06-19 17:31:50 - data_import - WARNING - No specific sheet found for ZERO, using default: Sheet1
2025-06-19 17:31:50 - data_import - INFO - Successfully read file: test_smart_detection.xlsx, rows: 6
2025-06-19 17:31:50 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:31:50 - data_import - INFO - Added missing columns: ['Copartner_name', 'Payment', 'Payment_date', 'User_name', 'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num', 'Import_Date']
2025-06-19 17:31:50 - data_import - INFO - Data validation and cleaning completed, valid rows: 6
2025-06-19 17:31:50 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 2, 'ZERO_Sales_Refunding': 2, 'ZERO_Sales_Close': 2}
2025-06-19 17:31:50 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 6, new data: 4
2025-06-19 17:31:50 - data_import - INFO - Separated 1 API orders from 4 total records
2025-06-19 17:31:50 - data_import - INFO - Found 1 API orders, routing to APP_Sales table
2025-06-19 17:31:50 - data_import - INFO - APP_Sales: inserted 1 API order records
2025-06-19 17:31:50 - data_import - INFO - Regular data distribution analysis: {'ZERO_Sales_Close': 2, 'ZERO_Sales_Refunding': 1}
2025-06-19 17:31:50 - data_import - INFO - Table ZERO_Sales_Close: inserted 2 records
2025-06-19 17:31:50 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 1 records
2025-06-19 17:31:50 - data_import - INFO - Smart insert completed, total inserted: 4 records (including 1 API orders)
2025-06-19 17:31:50 - data_import - INFO - ZERO_Sales_Close: inserted 2 records
2025-06-19 17:31:50 - data_import - INFO - ZERO_Sales_Refunding: inserted 1 records
2025-06-19 17:31:50 - data_import - INFO - APP_Sales: inserted 1 records
2025-06-19 17:31:50 - data_import - WARNING - Found 6 partially duplicate records (different prices), manual review needed
2025-06-19 17:31:50 - data_import - INFO - Smart file processing completed: test_smart_detection.xlsx
