# -*- coding: utf-8 -*-
"""
手动创建PostgreSQL视图
基于SQLite原始视图，手动调整为PostgreSQL兼容格式
"""

import psycopg2
import sys

def create_postgresql_views():
    """手动创建PostgreSQL视图"""
    print("🚀 手动创建PostgreSQL视图")
    print("="*50)
    
    # PostgreSQL连接配置
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        # 连接PostgreSQL
        pg_conn = psycopg2.connect(**pg_config)
        pg_conn.autocommit = True
        cursor = pg_conn.cursor()
        
        print("✅ 连接PostgreSQL成功")
        
        # 手动定义的视图（按依赖顺序）
        views = [
            # 1. Equipment_Valid - 基础视图
            {
                'name': 'Equipment_Valid',
                'sql': '''
                CREATE VIEW "Equipment_Valid" AS 
                SELECT
                    "Chair_Serial_No",
                    "STATE",
                    "Location",
                    "Quantity",
                    "Layer",
                    "Sale_Date",
                    "IOT_Price",
                    "ZERO_Price",
                    "IOT_Count",
                    "ZERO_Count",
                    "Total_Price",
                    "Effective_Start_Date",
                    "Effective_End_Date",
                    "Last_Updated",
                    "Is_Active",
                    "Notes",
                    "Equipment_Type"
                FROM "Equipment_ID"
                WHERE "STATE" IS NOT NULL
                '''
            },
            
            # 2. IOT_Daily_Sales
            {
                'name': 'IOT_Daily_Sales',
                'sql': '''
                CREATE VIEW "IOT_Daily_Sales" AS
                SELECT
                    "Equipment_ID" as "Chair_Serial_No",
                    TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') as "Sale_Date",
                    SUM(CAST("Order_price" AS NUMERIC)) as "IOT_Price",
                    COUNT(*) as "Daily_IOT_Count"
                FROM "IOT_Sales"
                WHERE "Order_status" = 'Finished'
                GROUP BY "Equipment_ID", TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                '''
            },
            
            # 3. ZERO_Daily_Sales
            {
                'name': 'ZERO_Daily_Sales',
                'sql': '''
                CREATE VIEW "ZERO_Daily_Sales" AS
                SELECT
                    "Equipment_ID" as "Chair_Serial_No",
                    TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') as "Sale_Date",
                    SUM(CAST("Order_price" AS NUMERIC)) as "ZERO_Price",
                    COUNT(*) as "Daily_ZERO_Count"
                FROM "ZERO_Sales"
                WHERE "Order_status" = 'Finished'
                GROUP BY "Equipment_ID", TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                '''
            },
            
            # 4. APP_Daily_Sales
            {
                'name': 'APP_Daily_Sales',
                'sql': '''
                CREATE VIEW "APP_Daily_Sales" AS
                SELECT
                    "Equipment_ID" as "Chair_Serial_No",
                    TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') as "Sale_Date",
                    SUM(CAST("Order_price" AS NUMERIC)) as "APP_Price",
                    COUNT(*) as "Daily_APP_Count"
                FROM "APP_Sales"
                WHERE "Order_status" = 'Finished'
                GROUP BY "Equipment_ID", TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                '''
            },
            
            # 5. Sales_Combined
            {
                'name': 'Sales_Combined',
                'sql': '''
                CREATE VIEW "Sales_Combined" AS
                SELECT
                    e."Chair_Serial_No",
                    e."STATE",
                    e."Location",
                    e."Quantity",
                    e."Layer",
                    COALESCE(i."Sale_Date", z."Sale_Date", a."Sale_Date") as "Sale_Date",
                    COALESCE(i."IOT_Price", 0) as "IOT_Price",
                    COALESCE(z."ZERO_Price", 0) as "ZERO_Price",
                    COALESCE(a."APP_Price", 0) as "APP_Price",
                    COALESCE(i."Daily_IOT_Count", 0) as "IOT_Count",
                    COALESCE(z."Daily_ZERO_Count", 0) as "ZERO_Count",
                    COALESCE(a."Daily_APP_Count", 0) as "APP_Count",
                    (COALESCE(i."IOT_Price", 0) + COALESCE(z."ZERO_Price", 0) + COALESCE(a."APP_Price", 0)) as "Total_Price"
                FROM "Equipment_ID" e
                LEFT JOIN "IOT_Daily_Sales" i ON e."Chair_Serial_No" = i."Chair_Serial_No"
                LEFT JOIN "ZERO_Daily_Sales" z ON e."Chair_Serial_No" = z."Chair_Serial_No" AND COALESCE(i."Sale_Date", '1900-01-01') = COALESCE(z."Sale_Date", '1900-01-01')
                LEFT JOIN "APP_Daily_Sales" a ON e."Chair_Serial_No" = a."Chair_Serial_No" AND COALESCE(i."Sale_Date", z."Sale_Date", '1900-01-01') = COALESCE(a."Sale_Date", '1900-01-01')
                '''
            },
            
            # 6. Valid_Sales
            {
                'name': 'Valid_Sales',
                'sql': '''
                CREATE VIEW "Valid_Sales" AS
                SELECT
                    "Chair_Serial_No",
                    "STATE",
                    "Location",
                    "Quantity",
                    "Layer",
                    "Sale_Date",
                    "IOT_Price",
                    "ZERO_Price",
                    "APP_Price",
                    "IOT_Count",
                    "ZERO_Count",
                    "APP_Count",
                    "Total_Price"
                FROM "Sales_Combined"
                WHERE "STATE" IS NOT NULL
                AND ("IOT_Count" > 0 OR "ZERO_Count" > 0 OR "APP_Count" > 0)
                '''
            },
            
            # 7. Daily_Sales
            {
                'name': 'Daily_Sales',
                'sql': '''
                CREATE VIEW "Daily_Sales" AS 
                SELECT
                    "Sale_Date",
                    SUM("Total_Price") AS "Daily_Total_Price",
                    SUM("IOT_Price") AS "Daily_IOT_Price",
                    SUM("ZERO_Price") AS "Daily_ZERO_Price",
                    SUM("APP_Price") AS "Daily_APP_Price",
                    SUM("IOT_Count") AS "Daily_IOT_Count",
                    SUM("ZERO_Count") AS "Daily_ZERO_Count",
                    SUM("APP_Count") AS "Daily_APP_Count"
                FROM "Valid_Sales"
                WHERE "Sale_Date" IS NOT NULL
                GROUP BY "Sale_Date"
                ORDER BY "Sale_Date"
                '''
            },
            
            # 8. Current_Equipment
            {
                'name': 'Current_Equipment',
                'sql': '''
                CREATE VIEW "Current_Equipment" AS 
                SELECT
                    ev."Chair_Serial_No",
                    ev."STATE",
                    ev."Location",
                    ev."Quantity",
                    ev."Layer",
                    ev."Sale_Date",
                    ev."IOT_Price",
                    ev."ZERO_Price",
                    ev."IOT_Count",
                    ev."ZERO_Count",
                    ev."Total_Price",
                    ev."Effective_Start_Date",
                    ev."Effective_End_Date",
                    ev."Last_Updated",
                    ev."Is_Active",
                    ev."Notes",
                    ev."Equipment_Type"
                FROM "Equipment_Valid" AS ev
                WHERE ev."Is_Active" = '1'
                '''
            },
            
            # 9. Daily_Equipment_Sales
            {
                'name': 'Daily_Equipment_Sales',
                'sql': '''
                CREATE VIEW "Daily_Equipment_Sales" AS
                SELECT
                    "Chair_Serial_No",
                    "Sale_Date",
                    "IOT_Price" AS "Price",
                    "Daily_IOT_Count" AS "Count",
                    'IOT' AS "Source"
                FROM "IOT_Daily_Sales"
                UNION ALL
                SELECT
                    "Chair_Serial_No",
                    "Sale_Date",
                    "ZERO_Price" AS "Price",
                    "Daily_ZERO_Count" AS "Count",
                    'ZERO' AS "Source"
                FROM "ZERO_Daily_Sales"
                UNION ALL
                SELECT
                    "Chair_Serial_No",
                    "Sale_Date",
                    "APP_Price" AS "Price",
                    "Daily_APP_Count" AS "Count",
                    'APP' AS "Source"
                FROM "APP_Daily_Sales"
                '''
            },
            
            # 10. Refunding_Close_Summary
            {
                'name': 'Refunding_Close_Summary',
                'sql': '''
                CREATE VIEW "Refunding_Close_Summary" AS
                SELECT
                    "Equipment_ID" AS "Chair_Serial_No",
                    TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') as "Sale_Date",
                    SUM(CASE WHEN "Order_status" = 'Refunded' THEN CAST("Order_price" AS NUMERIC) ELSE 0 END) as "IOT_Price_Refund",
                    SUM(CASE WHEN "Order_status" = 'Closed' THEN CAST("Order_price" AS NUMERIC) ELSE 0 END) as "IOT_Price_Close",
                    COUNT(CASE WHEN "Order_status" = 'Refunded' THEN 1 END) as "IOT_Count_Refund",
                    COUNT(CASE WHEN "Order_status" = 'Closed' THEN 1 END) as "IOT_Count_Close",
                    'IOT_Refunding_Close' AS "Source"
                FROM "IOT_Sales_Refunding"
                GROUP BY "Equipment_ID", TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                
                UNION ALL
                
                SELECT
                    "Equipment_ID" AS "Chair_Serial_No",
                    TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') as "Sale_Date",
                    SUM(CASE WHEN "Order_status" = 'Refunded' THEN CAST("Order_price" AS NUMERIC) ELSE 0 END) as "ZERO_Price_Refund",
                    SUM(CASE WHEN "Order_status" = 'Closed' THEN CAST("Order_price" AS NUMERIC) ELSE 0 END) as "ZERO_Price_Close",
                    COUNT(CASE WHEN "Order_status" = 'Refunded' THEN 1 END) as "ZERO_Count_Refund",
                    COUNT(CASE WHEN "Order_status" = 'Closed' THEN 1 END) as "ZERO_Count_Close",
                    'ZERO_Refunding_Close' AS "Source"
                FROM "ZERO_Sales_Refunding"
                GROUP BY "Equipment_ID", TO_CHAR(TO_DATE("Order_time", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                '''
            }
        ]
        
        created_views = []
        failed_views = []
        
        # 创建视图
        for view in views:
            view_name = view['name']
            view_sql = view['sql']
            
            try:
                print(f"\n🔧 创建视图: {view_name}")
                
                # 删除视图如果存在
                cursor.execute(f'DROP VIEW IF EXISTS "{view_name}" CASCADE')
                
                # 创建视图
                cursor.execute(view_sql)
                
                print(f"✅ 视图 {view_name} 创建成功")
                created_views.append(view_name)
                
            except Exception as e:
                print(f"❌ 视图 {view_name} 创建失败: {e}")
                failed_views.append((view_name, str(e)))
        
        pg_conn.close()
        
        # 报告结果
        print(f"\n📊 视图创建结果:")
        print(f"✅ 成功创建: {len(created_views)} 个视图")
        for view_name in created_views:
            print(f"  ✅ {view_name}")
        
        if failed_views:
            print(f"\n❌ 创建失败: {len(failed_views)} 个视图")
            for view_name, error in failed_views:
                print(f"  ❌ {view_name}: {error}")
        
        return len(failed_views) == 0
        
    except Exception as e:
        print(f"❌ 创建PostgreSQL视图失败: {e}")
        return False

def verify_views():
    """验证视图是否正常工作"""
    print("\n🔍 验证视图功能...")
    
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        conn = psycopg2.connect(**pg_config)
        cursor = conn.cursor()
        
        # 获取所有视图
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        views = cursor.fetchall()
        
        print(f"验证 {len(views)} 个视图:")
        
        for (view_name,) in views:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{view_name}"')
                count = cursor.fetchone()[0]
                print(f"  ✅ {view_name}: {count:,} 行")
            except Exception as e:
                print(f"  ❌ {view_name}: 查询失败 - {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证视图失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 手动创建PostgreSQL视图")
    print("基于SQLite原始功能，确保完全兼容")
    print("="*50)
    
    # 创建视图
    success = create_postgresql_views()
    
    if success:
        print("\n✅ 所有视图创建成功！")
    else:
        print("\n⚠️ 部分视图创建失败")
    
    # 验证视图
    verify_views()
    
    print("\n📋 完成！")
    print("现在您可以在pgAdmin4中看到所有视图了：")
    print("服务器 > postgres > Schemas > public > Views")
    print("\n这些视图提供与SQLite版本相同的功能：")
    print("- 日销售统计")
    print("- 设备销售汇总")
    print("- 退款关闭汇总")
    print("- 有效销售数据")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
