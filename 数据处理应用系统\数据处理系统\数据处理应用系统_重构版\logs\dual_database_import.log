2025-06-19 16:56:36 - dual_database_import - INFO - 开始导入文件: test_import.xlsx
2025-06-19 16:56:36 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 16:56:36 - dual_database_import - INFO - 目标数据库: ['SQLite']
2025-06-19 16:56:36 - dual_database_import - INFO - 数据分析: 总行数=2, 新数据=2, 完全重复=0, 部分重复=0
2025-06-19 16:56:36 - dual_database_import - INFO - 表分配统计:
2025-06-19 16:56:36 - dual_database_import - INFO -   ZERO_Sales: 2 行
2025-06-19 16:56:36 - dual_database_import - INFO -   ZERO_Sales_Refunding: 0 行
2025-06-19 16:56:36 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 16:56:36 - dual_database_import - INFO - 导入到表 ZERO_Sales: 2 行
2025-06-19 16:56:36 - dual_database_import - INFO - 导入结果: 导入完成: 1/1 个数据库成功
2025-06-19 16:56:49 - dual_database_import - INFO - 开始导入文件: test_import.xlsx
2025-06-19 16:56:49 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 16:56:49 - dual_database_import - INFO - 目标数据库: ['SQLite', 'PostgreSQL']
2025-06-19 16:56:49 - dual_database_import - INFO - 数据分析: 总行数=2, 新数据=2, 完全重复=0, 部分重复=0
2025-06-19 16:56:49 - dual_database_import - INFO - 表分配统计:
2025-06-19 16:56:49 - dual_database_import - INFO -   ZERO_Sales: 2 行
2025-06-19 16:56:49 - dual_database_import - INFO -   ZERO_Sales_Refunding: 0 行
2025-06-19 16:56:49 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 16:56:49 - dual_database_import - INFO - 导入到表 ZERO_Sales: 2 行
2025-06-19 16:56:50 - dual_database_import - INFO - 导入结果: 导入完成: 1/2 个数据库成功
2025-06-19 17:03:09 - dual_database_import - INFO - 开始导入文件: test_import.xlsx
2025-06-19 17:03:09 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 17:03:09 - dual_database_import - INFO - 目标数据库: ['SQLite', 'PostgreSQL']
2025-06-19 17:03:10 - dual_database_import - INFO - 数据分析: 总行数=2, 新数据=2, 完全重复=0, 部分重复=0
2025-06-19 17:03:10 - dual_database_import - INFO - 表分配统计:
2025-06-19 17:03:10 - dual_database_import - INFO -   ZERO_Sales: 2 行
2025-06-19 17:03:10 - dual_database_import - INFO -   ZERO_Sales_Refunding: 0 行
2025-06-19 17:03:10 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 17:03:10 - dual_database_import - INFO - 导入到表 ZERO_Sales: 2 行
2025-06-19 17:03:11 - dual_database_import - INFO - 导入结果: 导入完成: 1/2 个数据库成功
2025-06-19 17:09:36 - dual_database_import - INFO - 开始导入文件: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx
2025-06-19 17:09:36 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 17:09:36 - dual_database_import - INFO - 目标数据库: ['SQLite']
2025-06-19 17:09:36 - dual_database_import - INFO - 数据分析: 总行数=101, 新数据=101, 完全重复=0, 部分重复=0
2025-06-19 17:09:36 - dual_database_import - INFO - 表分配统计:
2025-06-19 17:09:36 - dual_database_import - INFO -   ZERO_Sales: 0 行
2025-06-19 17:09:36 - dual_database_import - INFO -   ZERO_Sales_Refunding: 2 行
2025-06-19 17:09:36 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 17:09:36 - dual_database_import - INFO - 导入到表 ZERO_Sales_Refunding: 2 行
2025-06-19 17:09:36 - dual_database_import - INFO - 导入结果: 导入完成: 0/1 个数据库成功
2025-06-19 17:10:24 - dual_database_import - INFO - 开始导入文件: test_import.xlsx
2025-06-19 17:10:24 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 17:10:24 - dual_database_import - INFO - 目标数据库: ['SQLite']
2025-06-19 17:10:24 - dual_database_import - INFO - 数据分析: 总行数=2, 新数据=2, 完全重复=0, 部分重复=0
2025-06-19 17:10:24 - dual_database_import - INFO - 表分配统计:
2025-06-19 17:10:24 - dual_database_import - INFO -   ZERO_Sales: 2 行
2025-06-19 17:10:24 - dual_database_import - INFO -   ZERO_Sales_Refunding: 0 行
2025-06-19 17:10:24 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 17:10:24 - dual_database_import - INFO - 导入到表 ZERO_Sales: 2 行
2025-06-19 17:10:24 - dual_database_import - INFO - 导入结果: 导入完成: 1/1 个数据库成功
2025-06-19 17:16:27 - dual_database_import - INFO - 开始导入文件: test_import.xlsx
2025-06-19 17:16:27 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 17:16:27 - dual_database_import - INFO - 目标数据库: ['SQLite']
2025-06-19 17:16:27 - dual_database_import - INFO - 数据分析: 总行数=2, 新数据=2, 完全重复=0, 部分重复=0
2025-06-19 17:16:27 - dual_database_import - INFO - 表分配统计:
2025-06-19 17:16:27 - dual_database_import - INFO -   ZERO_Sales: 2 行
2025-06-19 17:16:27 - dual_database_import - INFO -   ZERO_Sales_Refunding: 0 行
2025-06-19 17:16:27 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 17:16:27 - dual_database_import - INFO - 导入到表 ZERO_Sales: 2 行
2025-06-19 17:16:28 - dual_database_import - INFO - 导入结果: 导入完成: 1/1 个数据库成功
2025-06-19 17:16:45 - dual_database_import - INFO - 开始导入文件: test_import.xlsx
2025-06-19 17:16:45 - dual_database_import - INFO - 平台: ZERO, 订单类型: all
2025-06-19 17:16:45 - dual_database_import - INFO - 目标数据库: ['SQLite', 'PostgreSQL']
2025-06-19 17:16:46 - dual_database_import - INFO - 数据分析: 总行数=2, 新数据=2, 完全重复=0, 部分重复=0
2025-06-19 17:16:46 - dual_database_import - INFO - 表分配统计:
2025-06-19 17:16:46 - dual_database_import - INFO -   ZERO_Sales: 2 行
2025-06-19 17:16:46 - dual_database_import - INFO -   ZERO_Sales_Refunding: 0 行
2025-06-19 17:16:46 - dual_database_import - INFO -   ZERO_Sales_Close: 0 行
2025-06-19 17:16:46 - dual_database_import - INFO - 导入到表 ZERO_Sales: 2 行
2025-06-19 17:16:47 - dual_database_import - INFO - 导入结果: 导入完成: 1/2 个数据库成功
