2025-06-19 16:56:36 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_165636.db
2025-06-19 16:56:36 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 16:56:49 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_165649.db
2025-06-19 16:56:49 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 16:56:50 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250619_165649.sql
2025-06-19 16:56:50 - database.dual_database_manager - ERROR - 导入数据到PostgreSQL失败: Execution failed on sql '
        SELECT
            name
        FROM
            sqlite_master
        WHERE
            type IN ('table', 'view')
            AND name=?;
        ': 错误:  语法错误 在 ";" 或附近的
LINE 8:             AND name=?;
                              ^

2025-06-19 16:56:50 - database.dual_database_manager - ERROR - 数据导入到PostgreSQL失败
2025-06-19 17:03:10 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_170310.db
2025-06-19 17:03:10 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 17:03:11 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250619_170310.sql
2025-06-19 17:03:11 - database.dual_database_manager - ERROR - 导入数据到PostgreSQL失败: 错误:  无效的类型 real 输入语法: ""
LINE 1: ...'10', 'Finished', 'Normal', '设备1', '分店1', '', '', NULL, ...
                                                             ^

2025-06-19 17:03:11 - database.dual_database_manager - ERROR - 数据导入到PostgreSQL失败
2025-06-19 17:04:47 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_170447.db
2025-06-19 17:04:47 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 17:04:48 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250619_170447.sql
2025-06-19 17:04:48 - database.dual_database_manager - INFO - 数据成功导入到PostgreSQL
2025-06-19 17:04:48 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_170448.db
2025-06-19 17:04:48 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 17:04:49 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250619_170448.sql
2025-06-19 17:04:49 - database.dual_database_manager - INFO - 数据成功导入到PostgreSQL
2025-06-19 17:09:36 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_170936.db
2025-06-19 17:09:36 - database.dual_database_manager - ERROR - 导入数据到SQLite失败: table ZERO_Sales_Refunding has no column named Serial number
2025-06-19 17:09:36 - database.dual_database_manager - ERROR - 数据导入到SQLite失败
2025-06-19 17:10:24 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_171024.db
2025-06-19 17:10:24 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 17:16:28 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_171627.db
2025-06-19 17:16:28 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 17:16:46 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250619_171646.db
2025-06-19 17:16:46 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-19 17:16:46 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250619_171646.sql
2025-06-19 17:16:47 - database.dual_database_manager - ERROR - 导入数据到PostgreSQL失败: 错误:  无效的类型 real 输入语法: ""
LINE 1: ...'10', 'Finished', 'Normal', '设备1', '分店1', '', '', NULL, ...
                                                             ^

2025-06-19 17:16:47 - database.dual_database_manager - ERROR - 数据导入到PostgreSQL失败
2025-06-20 10:48:24 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250620_104824.db
2025-06-20 10:48:24 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-20 10:48:24 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250620_104824.sql
2025-06-20 10:48:24 - database.dual_database_manager - INFO - 数据成功导入到PostgreSQL
2025-06-20 10:51:08 - database.dual_database_manager - INFO - SQLite数据库已备份到: sqlite_backup_20250620_105108.db
2025-06-20 10:51:08 - database.dual_database_manager - INFO - 数据成功导入到SQLite
2025-06-20 10:51:09 - database.dual_database_manager - INFO - PostgreSQL数据库已备份到: postgresql_backup_20250620_105108.sql
2025-06-20 10:51:09 - database.dual_database_manager - INFO - 数据成功导入到PostgreSQL
