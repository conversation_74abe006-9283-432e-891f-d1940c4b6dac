2025-06-18 17:08:51 - data_import - INFO - 所有数据库表结构创建/验证完成
2025-06-18 17:08:51 - data_import - INFO - ✅ 新增表: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-18 17:08:51 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-18 17:08:51 - data_import - INFO - 🚀 开始智能处理文件: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, 平台: IOT
2025-06-18 17:08:53 - data_import - INFO - 成功读取文件: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, 数据行数: 12332
2025-06-18 17:08:53 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_price, Order_time
2025-06-18 17:09:04 - data_import - ERROR - ❌ 智能处理文件失败: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, 错误: DataProcessingError: 加载数据失败: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-18 17:21:27 - data_import - INFO - All database table structures created/verified
2025-06-18 17:21:27 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-18 17:21:27 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-18 17:21:27 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-18 17:21:29 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-18 17:21:29 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_price, Order_time
2025-06-18 17:21:36 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:38:52 - data_import - INFO - All database table structures created/verified
2025-06-19 08:38:52 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:38:52 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 08:38:53 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-19 08:38:56 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-19 08:38:56 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_time, Order_price
2025-06-19 08:40:20 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:50:50 - data_import - INFO - All database table structures created/verified
2025-06-19 08:50:50 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:50:50 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 08:50:50 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-19 08:50:52 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-19 08:50:52 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_time, Order_price
2025-06-19 08:50:56 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:57:12 - data_import - INFO - All database table structures created/verified
2025-06-19 08:57:12 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 08:57:12 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 08:57:12 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-19 08:57:13 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-19 08:57:13 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_price, Order_time
2025-06-19 08:57:13 - data_import - INFO - Added missing columns: ['Order_No', 'Order_status', 'Equipment_ID', 'Equipment_name', 'Branch_name', 'Order_types', 'Transaction_Num', 'Transaction_ID', 'Payment_date', 'Order_time', 'Order_price']
2025-06-19 08:57:13 - data_import - INFO - Data validation and cleaning completed, valid rows: 12332
2025-06-19 08:57:14 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 12332}
2025-06-19 08:57:14 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 12332
2025-06-19 08:57:15 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 12332}
2025-06-19 08:57:15 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Copartner name
2025-06-19 08:57:20 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Copartner name
2025-06-19 09:03:30 - data_import - INFO - All database table structures created/verified
2025-06-19 09:03:30 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:03:30 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 09:03:30 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-19 09:03:31 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-19 09:03:31 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_time, Order_price
2025-06-19 09:03:32 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 09:03:32 - data_import - INFO - Added missing columns: ['Order_No', 'Transaction_ID']
2025-06-19 09:03:32 - data_import - INFO - Data validation and cleaning completed, valid rows: 12332
2025-06-19 09:03:32 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Close': 12332}
2025-06-19 09:03:36 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DatabaseError: Failed to check duplicate data: You are trying to merge on int64 and object columns for key 'Equipment_ID'. If you wish to proceed you should use pd.concat
2025-06-19 09:16:55 - data_import - INFO - All database table structures created/verified
2025-06-19 09:16:55 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:16:55 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 09:16:56 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-19 09:16:57 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-19 09:16:57 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_price, Order_time
2025-06-19 09:16:57 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 09:16:58 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:16:58 - data_import - INFO - Added missing columns: ['Transaction_ID']
2025-06-19 09:16:58 - data_import - INFO - Data validation and cleaning completed, valid rows: 12332
2025-06-19 09:16:58 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Close': 12332}
2025-06-19 09:16:59 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 1061, new data: 12169
2025-06-19 09:16:59 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Close': 12169}
2025-06-19 09:16:59 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named Transaction_ID
2025-06-19 09:17:03 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named Transaction_ID
2025-06-19 09:21:48 - data_import - INFO - All database table structures created/verified
2025-06-19 09:21:48 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:21:48 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 09:21:48 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, platform: IOT
2025-06-19 09:21:49 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, rows: 12332
2025-06-19 09:21:49 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618 (1).xlsx): Order_time, Order_price
2025-06-19 09:21:50 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 09:21:50 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:21:50 - data_import - INFO - Data validation and cleaning completed, valid rows: 12332
2025-06-19 09:21:50 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Close': 12332}
2025-06-19 09:21:51 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 1061, new data: 12169
2025-06-19 09:21:51 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Close': 12169}
2025-06-19 09:21:52 - data_import - INFO - Table IOT_Sales_Close: inserted 12169 records
2025-06-19 09:21:52 - data_import - INFO - Smart insert completed, total inserted: 12169 records
2025-06-19 09:21:52 - data_import - INFO - IOT_Sales_Close: inserted 12169 records
2025-06-19 09:21:52 - data_import - WARNING - Found 1061 partially duplicate records (different prices), manual review needed
2025-06-19 09:21:52 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx
2025-06-19 09:22:25 - data_import - INFO - All database table structures created/verified
2025-06-19 09:22:25 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 09:22:25 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 09:22:25 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250618.xlsx, platform: IOT
2025-06-19 09:22:26 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250618.xlsx, rows: 5635
2025-06-19 09:22:26 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250618.xlsx): Order_price, Order_time
2025-06-19 09:22:26 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 09:22:26 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 09:22:26 - data_import - INFO - Data validation and cleaning completed, valid rows: 5635
2025-06-19 09:22:26 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Refunding': 5635}
2025-06-19 09:22:27 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 16810, new data: 1703
2025-06-19 09:22:27 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Refunding': 1703}
2025-06-19 09:22:27 - data_import - INFO - Table IOT_Sales_Refunding: inserted 1703 records
2025-06-19 09:22:27 - data_import - INFO - Smart insert completed, total inserted: 1703 records
2025-06-19 09:22:27 - data_import - INFO - IOT_Sales_Refunding: inserted 1703 records
2025-06-19 09:22:27 - data_import - WARNING - Found 16810 partially duplicate records (different prices), manual review needed
2025-06-19 09:22:27 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Downloads/online order Flow20250618.xlsx
2025-06-19 12:08:02 - data_import - INFO - All database table structures created/verified
2025-06-19 12:08:02 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 12:08:02 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 12:08:03 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250619 (1).xlsx, platform: IOT
2025-06-19 12:08:03 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250619 (1).xlsx, rows: 70
2025-06-19 12:08:03 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250619 (1).xlsx): Order_price, Order_time
2025-06-19 12:08:03 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 12:08:03 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 12:08:03 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 12:08:03 - data_import - INFO - Data validation and cleaning completed, valid rows: 70
2025-06-19 12:08:03 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Refunding': 70}
2025-06-19 12:08:04 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 13, new data: 61
2025-06-19 12:08:04 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Refunding': 61}
2025-06-19 12:08:04 - data_import - INFO - Table IOT_Sales_Refunding: inserted 61 records
2025-06-19 12:08:04 - data_import - INFO - Smart insert completed, total inserted: 61 records
2025-06-19 12:08:04 - data_import - INFO - IOT_Sales_Refunding: inserted 61 records
2025-06-19 12:08:04 - data_import - WARNING - Found 13 partially duplicate records (different prices), manual review needed
2025-06-19 12:08:04 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Downloads/online order Flow20250619 (1).xlsx
2025-06-19 12:08:22 - data_import - INFO - All database table structures created/verified
2025-06-19 12:08:22 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 12:08:22 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 12:08:22 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250619.xlsx, platform: IOT
2025-06-19 12:08:23 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250619.xlsx, rows: 6567
2025-06-19 12:08:23 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250619.xlsx): Order_price, Order_time
2025-06-19 12:08:23 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 12:08:23 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 12:08:23 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 12:08:23 - data_import - INFO - Data validation and cleaning completed, valid rows: 6567
2025-06-19 12:08:23 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Close': 6567}
2025-06-19 12:08:24 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 584, new data: 6437
2025-06-19 12:08:24 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Close': 6437}
2025-06-19 12:08:24 - data_import - INFO - Table IOT_Sales_Close: inserted 6437 records
2025-06-19 12:08:24 - data_import - INFO - Smart insert completed, total inserted: 6437 records
2025-06-19 12:08:24 - data_import - INFO - IOT_Sales_Close: inserted 6437 records
2025-06-19 12:08:24 - data_import - WARNING - Found 584 partially duplicate records (different prices), manual review needed
2025-06-19 12:08:24 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Downloads/online order Flow20250619.xlsx
2025-06-19 12:09:40 - data_import - INFO - All database table structures created/verified
2025-06-19 12:09:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 12:09:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 12:09:40 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250619 (1).xlsx, platform: ZERO
2025-06-19 12:09:41 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250619 (1).xlsx, rows: 70
2025-06-19 12:09:41 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250619 (1).xlsx): Order_price, Order_time
2025-06-19 12:09:41 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 12:09:41 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 12:09:41 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 12:09:41 - data_import - INFO - Data validation and cleaning completed, valid rows: 70
2025-06-19 12:09:41 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales_Refunding': 70}
2025-06-19 12:09:41 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 278, new data: 20
2025-06-19 12:09:41 - data_import - INFO - Data distribution analysis: {'ZERO_Sales_Refunding': 20}
2025-06-19 12:09:41 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 20 records
2025-06-19 12:09:41 - data_import - INFO - Smart insert completed, total inserted: 20 records
2025-06-19 12:09:41 - data_import - INFO - ZERO_Sales_Refunding: inserted 20 records
2025-06-19 12:09:41 - data_import - WARNING - Found 278 partially duplicate records (different prices), manual review needed
2025-06-19 12:09:41 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Downloads/online order Flow20250619 (1).xlsx
2025-06-19 12:10:15 - data_import - INFO - All database table structures created/verified
2025-06-19 12:10:15 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 12:10:15 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 12:10:15 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Downloads/online order Flow20250619 (2).xlsx, platform: IOT
2025-06-19 12:10:16 - data_import - INFO - Successfully read file: C:/Users/<USER>/Downloads/online order Flow20250619 (2).xlsx, rows: 2534
2025-06-19 12:10:16 - data_import - WARNING - Data validation warning: 缺少必需的列 (online order Flow20250619 (2).xlsx): Order_price, Order_time
2025-06-19 12:10:16 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 12:10:16 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 12:10:16 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 12:10:16 - data_import - INFO - Data validation and cleaning completed, valid rows: 2534
2025-06-19 12:10:16 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Refunding': 2534}
2025-06-19 12:10:17 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 9772, new data: 797
2025-06-19 12:10:17 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Refunding': 797}
2025-06-19 12:10:17 - data_import - INFO - Table IOT_Sales_Refunding: inserted 797 records
2025-06-19 12:10:17 - data_import - INFO - Smart insert completed, total inserted: 797 records
2025-06-19 12:10:17 - data_import - INFO - IOT_Sales_Refunding: inserted 797 records
2025-06-19 12:10:17 - data_import - WARNING - Found 9772 partially duplicate records (different prices), manual review needed
2025-06-19 12:10:17 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Downloads/online order Flow20250619 (2).xlsx
2025-06-19 16:52:47 - data_import - INFO - All database table structures created/verified
2025-06-19 16:52:47 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:52:47 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:52:48 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 16:52:48 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 16:52:48 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 16:52:48 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 16:52:48 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 16:52:48 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 16:52:48 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-19 16:52:48 - data_import - INFO - All database table structures created/verified
2025-06-19 16:52:48 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:52:48 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:52:49 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, rows: 174
2025-06-19 16:52:49 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 16:52:49 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 16:52:49 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 16:52:49 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 16:52:49 - data_import - INFO - Data validation and cleaning completed, valid rows: 174
2025-06-19 16:52:49 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 174
2025-06-19 16:52:49 - data_import - INFO - All database table structures created/verified
2025-06-19 16:52:49 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 16:52:49 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 16:52:50 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, rows: 107
2025-06-19 16:52:50 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 16:52:50 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 16:52:50 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 16:52:50 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 16:52:50 - data_import - INFO - Data validation and cleaning completed, valid rows: 107
2025-06-19 16:52:50 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 107
2025-06-19 17:03:48 - data_import - INFO - All database table structures created/verified
2025-06-19 17:03:48 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:03:48 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:03:48 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:03:48 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 17:03:48 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:03:48 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:03:48 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:03:48 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:03:49 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-19 17:03:51 - data_import - INFO - All database table structures created/verified
2025-06-19 17:03:51 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:03:51 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:03:52 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, rows: 174
2025-06-19 17:03:52 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 17:03:52 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:03:52 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:03:52 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:03:52 - data_import - INFO - Data validation and cleaning completed, valid rows: 174
2025-06-19 17:03:52 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 174
2025-06-19 17:03:54 - data_import - INFO - All database table structures created/verified
2025-06-19 17:03:54 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:03:54 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:03:55 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, rows: 107
2025-06-19 17:03:55 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:03:55 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:03:55 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:03:55 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:03:55 - data_import - INFO - Data validation and cleaning completed, valid rows: 107
2025-06-19 17:03:55 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 107
2025-06-19 17:09:46 - data_import - INFO - All database table structures created/verified
2025-06-19 17:09:46 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:09:46 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:09:47 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:09:47 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 17:09:47 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:09:47 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:09:47 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:09:47 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:09:47 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-19 17:09:47 - data_import - INFO - All database table structures created/verified
2025-06-19 17:09:47 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:09:47 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:09:47 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, rows: 174
2025-06-19 17:09:47 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 17:09:47 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:09:48 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:09:48 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:09:48 - data_import - INFO - Data validation and cleaning completed, valid rows: 174
2025-06-19 17:09:48 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 174
2025-06-19 17:09:48 - data_import - INFO - All database table structures created/verified
2025-06-19 17:09:48 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:09:48 - data_import - INFO - Smart data import processor initialized, DB path: database/sales_reports.db
2025-06-19 17:09:48 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, rows: 107
2025-06-19 17:09:48 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:09:48 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:09:48 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:09:48 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:09:48 - data_import - INFO - Data validation and cleaning completed, valid rows: 107
2025-06-19 17:09:48 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 107
2025-06-19 17:16:44 - data_import - INFO - All database table structures created/verified
2025-06-19 17:16:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:16:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:16:44 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:16:45 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:16:45 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:16:45 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:16:45 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:16:45 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:16:45 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:16:45 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:16:45 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-19 17:16:45 - data_import - INFO - Data distribution analysis: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:16:45 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:49 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:51 - data_import - INFO - All database table structures created/verified
2025-06-19 17:16:51 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:16:51 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:16:51 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:16:51 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, rows: 174
2025-06-19 17:16:51 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:16:51 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:16:51 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:16:51 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:16:51 - data_import - INFO - Data validation and cleaning completed, valid rows: 174
2025-06-19 17:16:51 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 166, 'ZERO_Sales_Refunding': 6, 'ZERO_Sales_Close': 2}
2025-06-19 17:16:51 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 174
2025-06-19 17:16:51 - data_import - INFO - Data distribution analysis: {'ZERO_Sales': 166, 'ZERO_Sales_Refunding': 6, 'ZERO_Sales_Close': 2}
2025-06-19 17:16:51 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:54 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:56 - data_import - INFO - All database table structures created/verified
2025-06-19 17:16:56 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:16:56 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:16:56 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:16:56 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, rows: 107
2025-06-19 17:16:56 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:16:56 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num']
2025-06-19 17:16:56 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:16:56 - data_import - INFO - Added missing columns: ['Time', 'Matched_Order_ID', 'OrderTime_dt', 'Import_Date']
2025-06-19 17:16:56 - data_import - INFO - Data validation and cleaning completed, valid rows: 107
2025-06-19 17:16:56 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 101, 'ZERO_Sales_Refunding': 6}
2025-06-19 17:16:57 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 107
2025-06-19 17:16:57 - data_import - INFO - Data distribution analysis: {'ZERO_Sales': 101, 'ZERO_Sales_Refunding': 6}
2025-06-19 17:16:57 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:17:01 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:22:43 - data_import - INFO - All database table structures created/verified
2025-06-19 17:22:43 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:22:43 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:22:43 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:22:43 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO130625', 'LOG']
2025-06-19 17:22:43 - data_import - INFO - Found matching sheet for ZERO: ZERO130625
2025-06-19 17:22:43 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-19 17:22:43 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:22:43 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:22:43 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:22:43 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:22:43 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-19 17:22:43 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-19 17:22:44 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 554, new data: 1
2025-06-19 17:22:44 - data_import - INFO - Data distribution analysis: {'ZERO_Sales_Refunding': 1}
2025-06-19 17:22:44 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 1 records
2025-06-19 17:22:44 - data_import - INFO - Smart insert completed, total inserted: 1 records
2025-06-19 17:22:44 - data_import - INFO - ZERO_Sales_Refunding: inserted 1 records
2025-06-19 17:22:44 - data_import - WARNING - Found 554 partially duplicate records (different prices), manual review needed
2025-06-19 17:22:44 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx
2025-06-19 17:22:44 - data_import - INFO - All database table structures created/verified
2025-06-19 17:22:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:22:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:22:45 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:22:45 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO140625', 'LOG']
2025-06-19 17:22:45 - data_import - INFO - Found matching sheet for ZERO: ZERO140625
2025-06-19 17:22:45 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, rows: 173
2025-06-19 17:22:45 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-19 17:22:45 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:22:45 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:22:45 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:22:45 - data_import - INFO - Data validation and cleaning completed, valid rows: 173
2025-06-19 17:22:45 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 167, 'ZERO_Sales_Refunding': 6}
2025-06-19 17:22:45 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 173
2025-06-19 17:22:45 - data_import - INFO - Data distribution analysis: {'ZERO_Sales': 167, 'ZERO_Sales_Refunding': 6}
2025-06-19 17:22:45 - data_import - INFO - Table ZERO_Sales: inserted 167 records
2025-06-19 17:22:45 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 6 records
2025-06-19 17:22:45 - data_import - INFO - Smart insert completed, total inserted: 173 records
2025-06-19 17:22:45 - data_import - INFO - ZERO_Sales: inserted 167 records
2025-06-19 17:22:45 - data_import - INFO - ZERO_Sales_Refunding: inserted 6 records
2025-06-19 17:22:45 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx
2025-06-19 17:22:46 - data_import - INFO - All database table structures created/verified
2025-06-19 17:22:46 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:22:46 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:22:46 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, platform: ZERO
2025-06-19 17:22:47 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO150625', 'LOG']
2025-06-19 17:22:47 - data_import - INFO - Found matching sheet for ZERO: ZERO150625
2025-06-19 17:22:47 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, rows: 107
2025-06-19 17:22:47 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-19 17:22:47 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:22:47 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:22:47 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:22:47 - data_import - INFO - Data validation and cleaning completed, valid rows: 107
2025-06-19 17:22:47 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 101, 'ZERO_Sales_Refunding': 6}
2025-06-19 17:22:47 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 107
2025-06-19 17:22:47 - data_import - INFO - Data distribution analysis: {'ZERO_Sales': 101, 'ZERO_Sales_Refunding': 6}
2025-06-19 17:22:47 - data_import - INFO - Table ZERO_Sales: inserted 101 records
2025-06-19 17:22:47 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 6 records
2025-06-19 17:22:47 - data_import - INFO - Smart insert completed, total inserted: 107 records
2025-06-19 17:22:47 - data_import - INFO - ZERO_Sales: inserted 101 records
2025-06-19 17:22:47 - data_import - INFO - ZERO_Sales_Refunding: inserted 6 records
2025-06-19 17:22:47 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx
2025-06-19 17:23:10 - data_import - INFO - All database table structures created/verified
2025-06-19 17:23:10 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:23:10 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:23:10 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx, platform: IOT
2025-06-19 17:23:10 - data_import - INFO - Available sheets: ['Sheet0', 'IOT130625', 'LOG']
2025-06-19 17:23:10 - data_import - INFO - Found matching sheet for IOT: IOT130625
2025-06-19 17:23:11 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx, rows: 4197
2025-06-19 17:23:11 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA IOT.xlsx): Order_price, Order_time
2025-06-19 17:23:11 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:23:11 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:23:11 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:23:11 - data_import - INFO - Data validation and cleaning completed, valid rows: 4197
2025-06-19 17:23:11 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 3530, 'IOT_Sales_Refunding': 156, 'IOT_Sales_Close': 511}
2025-06-19 17:23:12 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 4197
2025-06-19 17:23:12 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 3530, 'IOT_Sales_Refunding': 156, 'IOT_Sales_Close': 511}
2025-06-19 17:23:12 - data_import - INFO - Table IOT_Sales: inserted 3530 records
2025-06-19 17:23:12 - data_import - INFO - Table IOT_Sales_Refunding: inserted 156 records
2025-06-19 17:23:12 - data_import - INFO - Table IOT_Sales_Close: inserted 511 records
2025-06-19 17:23:12 - data_import - INFO - Smart insert completed, total inserted: 4197 records
2025-06-19 17:23:12 - data_import - INFO - IOT_Sales: inserted 3530 records
2025-06-19 17:23:12 - data_import - INFO - IOT_Sales_Refunding: inserted 156 records
2025-06-19 17:23:12 - data_import - INFO - IOT_Sales_Close: inserted 511 records
2025-06-19 17:23:12 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx
2025-06-19 17:23:13 - data_import - INFO - All database table structures created/verified
2025-06-19 17:23:13 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:23:13 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:23:13 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx, platform: IOT
2025-06-19 17:23:14 - data_import - INFO - Available sheets: ['Sheet0', 'IOT140625', 'LOG']
2025-06-19 17:23:14 - data_import - INFO - Found matching sheet for IOT: IOT140625
2025-06-19 17:23:15 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx, rows: 6544
2025-06-19 17:23:15 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA IOT.xlsx): Order_price, Order_time
2025-06-19 17:23:15 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:23:15 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:23:15 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:23:15 - data_import - INFO - Data validation and cleaning completed, valid rows: 6544
2025-06-19 17:23:15 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Refunding': 349, 'IOT_Sales': 5540, 'IOT_Sales_Close': 655}
2025-06-19 17:23:16 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 6544
2025-06-19 17:23:16 - data_import - INFO - Data distribution analysis: {'IOT_Sales_Refunding': 349, 'IOT_Sales': 5540, 'IOT_Sales_Close': 655}
2025-06-19 17:23:16 - data_import - INFO - Table IOT_Sales_Refunding: inserted 349 records
2025-06-19 17:23:16 - data_import - INFO - Table IOT_Sales: inserted 5540 records
2025-06-19 17:23:16 - data_import - INFO - Table IOT_Sales_Close: inserted 655 records
2025-06-19 17:23:16 - data_import - INFO - Smart insert completed, total inserted: 6544 records
2025-06-19 17:23:16 - data_import - INFO - IOT_Sales_Refunding: inserted 349 records
2025-06-19 17:23:16 - data_import - INFO - IOT_Sales: inserted 5540 records
2025-06-19 17:23:16 - data_import - INFO - IOT_Sales_Close: inserted 655 records
2025-06-19 17:23:16 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx
2025-06-19 17:23:17 - data_import - INFO - All database table structures created/verified
2025-06-19 17:23:17 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-19 17:23:17 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-19 17:23:17 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx, platform: IOT
2025-06-19 17:23:18 - data_import - INFO - Available sheets: ['Sheet0', 'IOT150625', 'LOG']
2025-06-19 17:23:18 - data_import - INFO - Found matching sheet for IOT: IOT150625
2025-06-19 17:23:19 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx, rows: 7457
2025-06-19 17:23:19 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA IOT.xlsx): Order_time, Order_price
2025-06-19 17:23:19 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-19 17:23:19 - data_import - INFO - Data types standardized for database compatibility
2025-06-19 17:23:19 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-19 17:23:19 - data_import - INFO - Data validation and cleaning completed, valid rows: 7457
2025-06-19 17:23:20 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 6392, 'IOT_Sales_Refunding': 276, 'IOT_Sales_Close': 789}
2025-06-19 17:23:20 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 7457
2025-06-19 17:23:20 - data_import - INFO - Data distribution analysis: {'IOT_Sales': 6392, 'IOT_Sales_Refunding': 276, 'IOT_Sales_Close': 789}
2025-06-19 17:23:21 - data_import - INFO - Table IOT_Sales: inserted 6392 records
2025-06-19 17:23:21 - data_import - INFO - Table IOT_Sales_Refunding: inserted 276 records
2025-06-19 17:23:21 - data_import - INFO - Table IOT_Sales_Close: inserted 789 records
2025-06-19 17:23:21 - data_import - INFO - Smart insert completed, total inserted: 7457 records
2025-06-19 17:23:21 - data_import - INFO - IOT_Sales: inserted 6392 records
2025-06-19 17:23:21 - data_import - INFO - IOT_Sales_Refunding: inserted 276 records
2025-06-19 17:23:21 - data_import - INFO - IOT_Sales_Close: inserted 789 records
2025-06-19 17:23:21 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx
2025-06-20 08:42:55 - data_import - INFO - All database table structures created/verified
2025-06-20 08:42:55 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-20 08:42:55 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:42:56 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx, platform: IOT
2025-06-20 08:42:56 - data_import - INFO - Available sheets: ['Sheet0', 'IOT130625', 'LOG']
2025-06-20 08:42:56 - data_import - INFO - Found matching sheet for IOT: IOT130625
2025-06-20 08:42:57 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx, rows: 4197
2025-06-20 08:42:57 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA IOT.xlsx): Order_time, Order_price
2025-06-20 08:42:57 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-20 08:42:57 - data_import - INFO - Data types standardized for database compatibility
2025-06-20 08:42:57 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-20 08:42:57 - data_import - INFO - Data validation and cleaning completed, valid rows: 4197
2025-06-20 08:42:57 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 3530, 'IOT_Sales_Refunding': 156, 'IOT_Sales_Close': 511}
2025-06-20 08:42:58 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 4197
2025-06-20 08:42:58 - data_import - INFO - Separated 89 API orders from 4197 total records
2025-06-20 08:42:58 - data_import - INFO - Found 89 API orders, routing to APP_Sales table
2025-06-20 08:42:58 - data_import - INFO - APP_Sales: inserted 89 API order records
2025-06-20 08:42:58 - data_import - INFO - Regular data distribution analysis: {'IOT_Sales': 3441, 'IOT_Sales_Refunding': 156, 'IOT_Sales_Close': 511}
2025-06-20 08:42:58 - data_import - INFO - Table IOT_Sales: inserted 3441 records
2025-06-20 08:42:58 - data_import - INFO - Table IOT_Sales_Refunding: inserted 156 records
2025-06-20 08:42:58 - data_import - INFO - Table IOT_Sales_Close: inserted 511 records
2025-06-20 08:42:58 - data_import - INFO - Smart insert completed, total inserted: 4197 records (including 89 API orders)
2025-06-20 08:42:58 - data_import - INFO - IOT_Sales: inserted 3441 records
2025-06-20 08:42:58 - data_import - INFO - IOT_Sales_Refunding: inserted 156 records
2025-06-20 08:42:58 - data_import - INFO - IOT_Sales_Close: inserted 511 records
2025-06-20 08:42:58 - data_import - INFO - APP_Sales: inserted 89 records
2025-06-20 08:42:58 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/IOT/130625 CHINA IOT.xlsx
2025-06-20 08:43:47 - data_import - INFO - All database table structures created/verified
2025-06-20 08:43:47 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-20 08:43:47 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:43:48 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx, platform: IOT
2025-06-20 08:43:48 - data_import - INFO - Available sheets: ['Sheet0', 'IOT140625', 'LOG']
2025-06-20 08:43:48 - data_import - INFO - Found matching sheet for IOT: IOT140625
2025-06-20 08:43:49 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx, rows: 6544
2025-06-20 08:43:49 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA IOT.xlsx): Order_price, Order_time
2025-06-20 08:43:49 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-20 08:43:49 - data_import - INFO - Data types standardized for database compatibility
2025-06-20 08:43:49 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-20 08:43:49 - data_import - INFO - Data validation and cleaning completed, valid rows: 6544
2025-06-20 08:43:49 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales_Refunding': 349, 'IOT_Sales': 5540, 'IOT_Sales_Close': 655}
2025-06-20 08:43:50 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 6544
2025-06-20 08:43:50 - data_import - INFO - Separated 116 API orders from 6544 total records
2025-06-20 08:43:50 - data_import - INFO - Found 116 API orders, routing to APP_Sales table
2025-06-20 08:43:50 - data_import - INFO - APP_Sales: inserted 116 API order records
2025-06-20 08:43:50 - data_import - INFO - Regular data distribution analysis: {'IOT_Sales_Refunding': 349, 'IOT_Sales': 5424, 'IOT_Sales_Close': 655}
2025-06-20 08:43:50 - data_import - INFO - Table IOT_Sales_Refunding: inserted 349 records
2025-06-20 08:43:51 - data_import - INFO - Table IOT_Sales: inserted 5424 records
2025-06-20 08:43:51 - data_import - INFO - Table IOT_Sales_Close: inserted 655 records
2025-06-20 08:43:51 - data_import - INFO - Smart insert completed, total inserted: 6544 records (including 116 API orders)
2025-06-20 08:43:51 - data_import - INFO - IOT_Sales_Refunding: inserted 349 records
2025-06-20 08:43:51 - data_import - INFO - IOT_Sales: inserted 5424 records
2025-06-20 08:43:51 - data_import - INFO - IOT_Sales_Close: inserted 655 records
2025-06-20 08:43:51 - data_import - INFO - APP_Sales: inserted 116 records
2025-06-20 08:43:51 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/IOT/140625 CHINA IOT.xlsx
2025-06-20 08:43:51 - data_import - INFO - All database table structures created/verified
2025-06-20 08:43:51 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-20 08:43:51 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:43:52 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx, platform: IOT
2025-06-20 08:43:52 - data_import - INFO - Available sheets: ['Sheet0', 'IOT150625', 'LOG']
2025-06-20 08:43:52 - data_import - INFO - Found matching sheet for IOT: IOT150625
2025-06-20 08:43:53 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx, rows: 7457
2025-06-20 08:43:53 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA IOT.xlsx): Order_time, Order_price
2025-06-20 08:43:53 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-20 08:43:53 - data_import - INFO - Data types standardized for database compatibility
2025-06-20 08:43:53 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-20 08:43:53 - data_import - INFO - Data validation and cleaning completed, valid rows: 7457
2025-06-20 08:43:53 - data_import - INFO - Data will be distributed to tables: {'IOT_Sales': 6392, 'IOT_Sales_Refunding': 276, 'IOT_Sales_Close': 789}
2025-06-20 08:43:54 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 7457
2025-06-20 08:43:54 - data_import - INFO - Separated 150 API orders from 7457 total records
2025-06-20 08:43:54 - data_import - INFO - Found 150 API orders, routing to APP_Sales table
2025-06-20 08:43:54 - data_import - INFO - APP_Sales: inserted 150 API order records
2025-06-20 08:43:54 - data_import - INFO - Regular data distribution analysis: {'IOT_Sales': 6242, 'IOT_Sales_Refunding': 276, 'IOT_Sales_Close': 789}
2025-06-20 08:43:55 - data_import - INFO - Table IOT_Sales: inserted 6242 records
2025-06-20 08:43:55 - data_import - INFO - Table IOT_Sales_Refunding: inserted 276 records
2025-06-20 08:43:55 - data_import - INFO - Table IOT_Sales_Close: inserted 789 records
2025-06-20 08:43:55 - data_import - INFO - Smart insert completed, total inserted: 7457 records (including 150 API orders)
2025-06-20 08:43:55 - data_import - INFO - IOT_Sales: inserted 6242 records
2025-06-20 08:43:55 - data_import - INFO - IOT_Sales_Refunding: inserted 276 records
2025-06-20 08:43:55 - data_import - INFO - IOT_Sales_Close: inserted 789 records
2025-06-20 08:43:55 - data_import - INFO - APP_Sales: inserted 150 records
2025-06-20 08:43:55 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/IOT/150625 CHINA IOT.xlsx
2025-06-20 08:44:24 - data_import - INFO - All database table structures created/verified
2025-06-20 08:44:24 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-20 08:44:24 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:44:24 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, platform: ZERO
2025-06-20 08:44:24 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO130625', 'LOG']
2025-06-20 08:44:24 - data_import - INFO - Found matching sheet for ZERO: ZERO130625
2025-06-20 08:44:24 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, rows: 101
2025-06-20 08:44:24 - data_import - WARNING - Data validation warning: 缺少必需的列 (130625 CHINA ZERO.xlsx): Order_time, Order_price
2025-06-20 08:44:24 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-20 08:44:24 - data_import - INFO - Data types standardized for database compatibility
2025-06-20 08:44:24 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-20 08:44:24 - data_import - INFO - Data validation and cleaning completed, valid rows: 101
2025-06-20 08:44:24 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-20 08:44:24 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 101
2025-06-20 08:44:24 - data_import - INFO - Regular data distribution analysis: {'ZERO_Sales': 99, 'ZERO_Sales_Refunding': 2}
2025-06-20 08:44:24 - data_import - INFO - Table ZERO_Sales: inserted 99 records
2025-06-20 08:44:24 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 2 records
2025-06-20 08:44:24 - data_import - INFO - Smart insert completed, total inserted: 101 records (including 0 API orders)
2025-06-20 08:44:24 - data_import - INFO - ZERO_Sales: inserted 99 records
2025-06-20 08:44:24 - data_import - INFO - ZERO_Sales_Refunding: inserted 2 records
2025-06-20 08:44:24 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx
2025-06-20 08:44:25 - data_import - INFO - All database table structures created/verified
2025-06-20 08:44:25 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-20 08:44:25 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:44:25 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, platform: ZERO
2025-06-20 08:44:25 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO140625', 'LOG']
2025-06-20 08:44:25 - data_import - INFO - Found matching sheet for ZERO: ZERO140625
2025-06-20 08:44:25 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, rows: 173
2025-06-20 08:44:25 - data_import - WARNING - Data validation warning: 缺少必需的列 (140625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-20 08:44:25 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-20 08:44:25 - data_import - INFO - Data types standardized for database compatibility
2025-06-20 08:44:25 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-20 08:44:25 - data_import - INFO - Data validation and cleaning completed, valid rows: 173
2025-06-20 08:44:25 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 167, 'ZERO_Sales_Refunding': 6}
2025-06-20 08:44:25 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 173
2025-06-20 08:44:25 - data_import - INFO - Regular data distribution analysis: {'ZERO_Sales': 167, 'ZERO_Sales_Refunding': 6}
2025-06-20 08:44:25 - data_import - INFO - Table ZERO_Sales: inserted 167 records
2025-06-20 08:44:25 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 6 records
2025-06-20 08:44:25 - data_import - INFO - Smart insert completed, total inserted: 173 records (including 0 API orders)
2025-06-20 08:44:25 - data_import - INFO - ZERO_Sales: inserted 167 records
2025-06-20 08:44:25 - data_import - INFO - ZERO_Sales_Refunding: inserted 6 records
2025-06-20 08:44:25 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx
2025-06-20 08:44:26 - data_import - INFO - All database table structures created/verified
2025-06-20 08:44:26 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-06-20 08:44:26 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-20 08:44:26 - data_import - INFO - Starting smart file processing: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, platform: ZERO
2025-06-20 08:44:27 - data_import - INFO - Available sheets: ['Sheet0', 'ZERO150625', 'LOG']
2025-06-20 08:44:27 - data_import - INFO - Found matching sheet for ZERO: ZERO150625
2025-06-20 08:44:27 - data_import - INFO - Successfully read file: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, rows: 107
2025-06-20 08:44:27 - data_import - WARNING - Data validation warning: 缺少必需的列 (150625 CHINA ZERO.xlsx): Order_price, Order_time
2025-06-20 08:44:27 - data_import - INFO - Standardized column names: ['Copartner name -> Copartner_name', 'Order No. -> Order_No', 'Order types -> Order_types', 'Order status -> Order_status', 'Order price -> Order_price', 'Order time -> Order_time', 'Equipment ID -> Equipment_ID', 'Equipment name -> Equipment_name', 'Branch name -> Branch_name', 'Payment date -> Payment_date', 'User name -> User_name', 'Transaction Num -> Transaction_Num', 'Matched Order ID -> Matched_Order_ID']
2025-06-20 08:44:27 - data_import - INFO - Data types standardized for database compatibility
2025-06-20 08:44:27 - data_import - INFO - Added missing columns: ['Import_Date']
2025-06-20 08:44:27 - data_import - INFO - Data validation and cleaning completed, valid rows: 107
2025-06-20 08:44:27 - data_import - INFO - Data will be distributed to tables: {'ZERO_Sales': 101, 'ZERO_Sales_Refunding': 6}
2025-06-20 08:44:27 - data_import - INFO - Duplicate check completed - fully duplicate: 0, partially different: 0, new data: 107
2025-06-20 08:44:27 - data_import - INFO - Regular data distribution analysis: {'ZERO_Sales': 101, 'ZERO_Sales_Refunding': 6}
2025-06-20 08:44:27 - data_import - INFO - Table ZERO_Sales: inserted 101 records
2025-06-20 08:44:27 - data_import - INFO - Table ZERO_Sales_Refunding: inserted 6 records
2025-06-20 08:44:27 - data_import - INFO - Smart insert completed, total inserted: 107 records (including 0 API orders)
2025-06-20 08:44:27 - data_import - INFO - ZERO_Sales: inserted 101 records
2025-06-20 08:44:27 - data_import - INFO - ZERO_Sales_Refunding: inserted 6 records
2025-06-20 08:44:27 - data_import - INFO - Smart file processing completed: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx
