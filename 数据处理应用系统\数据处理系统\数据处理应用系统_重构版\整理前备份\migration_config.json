{"batch_size": 1000, "conflict_resolution": "overwrite", "backup_before_migration": true, "verify_after_migration": true, "log_level": "INFO", "excluded_columns": ["id"], "key_column": "Equipment_ID", "migration_settings": {"description": "智能迁移配置文件", "version": "1.0", "last_updated": "2024-06-19"}, "conflict_resolution_options": {"overwrite": "直接覆盖PostgreSQL中的数据（推荐）", "skip": "跳过冲突记录，保留PostgreSQL中的数据", "manual": "生成冲突报告，需要人工处理"}, "supported_tables": ["IOT_Sales", "IOT_Sales_Refunding", "IOT_Sales_Close", "ZERO_Sales", "ZERO_Sales_Refunding", "ZERO_Sales_Close", "APP_Sales", "APP_Sales_Refunding", "APP_Sales_Close", "Combined_Sales"], "performance_settings": {"connection_timeout": 30, "query_timeout": 300, "max_retries": 3, "retry_delay": 5}, "logging_settings": {"log_to_file": true, "log_file_path": "migration_logs/migration.log", "log_rotation": true, "max_log_size": "10MB", "backup_count": 5}}