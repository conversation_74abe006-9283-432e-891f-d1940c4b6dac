# -*- coding: utf-8 -*-
"""
验证修复结果
检查配置文件路径和UI简化是否成功
"""

import os
import sys
import configparser

def test_config_path_fix():
    """测试配置文件路径修复"""
    
    print("🔍 测试配置文件路径修复...")
    print("=" * 60)
    
    # 检查主程序中的配置文件路径
    main_app_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据处理与导入应用_完整版.py"
    
    if not os.path.exists(main_app_path):
        print(f"❌ 主程序文件不存在: {main_app_path}")
        return False
    
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查配置文件路径修复
        if 'self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_file)' in content:
            print("✅ 配置文件路径已修复为当前目录")
        else:
            print("❌ 配置文件路径未修复")
            return False
        
        # 检查硬编码配置修复
        if "'data_import_script_optimized': '数据导入脚本.py'" in content:
            print("✅ 硬编码配置已修复")
        else:
            print("❌ 硬编码配置未修复")
            return False
        
        print("\n✅ 配置文件路径修复检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查配置文件路径修复失败: {e}")
        return False

def test_ui_simplification():
    """测试UI简化"""
    
    print("\n🔍 测试UI简化...")
    print("=" * 60)
    
    main_app_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据处理与导入应用_完整版.py"
    
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查UI简化
        if 'text="📊 数据库选择"' in content:
            print("✅ 数据库选择标题已简化")
        else:
            print("❌ 数据库选择标题未简化")
            return False
        
        # 检查复杂状态显示是否被移除
        if 'status_frame = ttk.LabelFrame(db_frame, text="数据库连接状态"' not in content:
            print("✅ 复杂状态显示已移除")
        else:
            print("❌ 复杂状态显示未移除")
            return False
        
        # 检查简化的状态标签
        if 'self.db_status_label' in content:
            print("✅ 简化的状态标签已添加")
        else:
            print("❌ 简化的状态标签未添加")
            return False
        
        print("\n✅ UI简化检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查UI简化失败: {e}")
        return False

def test_config_file_content():
    """测试配置文件内容"""
    
    print("\n🔍 测试配置文件内容...")
    print("=" * 60)
    
    config_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\config.ini"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        # 检查关键配置
        if 'Scripts' in config:
            scripts = config['Scripts']
            
            # 检查data_import_script_optimized
            if 'data_import_script_optimized' in scripts:
                script_value = scripts['data_import_script_optimized']
                if script_value == '数据导入脚本.py':
                    print("✅ data_import_script_optimized 配置正确")
                    
                    # 检查脚本文件是否存在
                    script_path = os.path.join(
                        os.path.dirname(config_path),
                        script_value
                    )
                    if os.path.exists(script_path):
                        print("✅ 数据导入脚本文件存在")
                    else:
                        print(f"❌ 数据导入脚本文件不存在: {script_path}")
                        return False
                else:
                    print(f"❌ data_import_script_optimized 配置错误: {script_value}")
                    return False
            else:
                print("❌ data_import_script_optimized 配置缺失")
                return False
        else:
            print("❌ Scripts 配置段缺失")
            return False
        
        print("\n✅ 配置文件内容检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查配置文件内容失败: {e}")
        return False

def test_backup_path_fix():
    """测试备份路径修复"""
    
    print("\n🔍 测试备份路径修复...")
    print("=" * 60)
    
    main_app_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据处理与导入应用_完整版.py"
    
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查路径规范化
        if 'backup_path = os.path.normpath(' in content:
            print("✅ 备份路径规范化已添加")
        else:
            print("❌ 备份路径规范化未添加")
            return False
        
        # 检查绝对路径处理
        if 'if os.path.isabs(backup_filename):' in content:
            print("✅ 绝对路径处理已添加")
        else:
            print("❌ 绝对路径处理未添加")
            return False
        
        # 检查日志控件修复
        if 'self.gui_updater.safe_log(error_msg, "import")' in content:
            print("✅ 日志控件已修复")
        else:
            print("❌ 日志控件未修复")
            return False
        
        print("\n✅ 备份路径修复检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查备份路径修复失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🚀 修复结果验证")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("配置文件路径修复", test_config_path_fix),
        ("UI简化", test_ui_simplification),
        ("配置文件内容", test_config_file_content),
        ("备份路径修复", test_backup_path_fix)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试时出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n📊 验证结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📋 总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 所有修复验证通过！")
        print("\n📋 修复内容总结:")
        print("  ✅ 配置文件路径：从错误的03_配置文件目录修复为当前目录")
        print("  ✅ 硬编码配置：data_import_script_optimized 指向正确的脚本")
        print("  ✅ UI界面简化：移除复杂的数据库状态显示")
        print("  ✅ 备份路径处理：添加路径规范化和绝对路径支持")
        print("  ✅ 日志控件修复：使用正确的日志控件名称")
        
        print("\n🔧 现在可以:")
        print("  1. 重新启动主程序")
        print("  2. 配置文件将正确加载")
        print("  3. 数据导入脚本路径正确")
        print("  4. 备份恢复功能正常工作")
        print("  5. UI界面更加简洁")
        
    else:
        print(f"\n⚠️ 修复验证部分失败！")
        print(f"  需要修复 {total - passed} 个问题")
    
    return passed == total

if __name__ == "__main__":
    result = main()
    input(f"\n按回车键退出... (验证{'成功' if result else '失败'})")
