# -*- coding: utf-8 -*-
"""
检查PostgreSQL数据库中的数据
帮助用户在pgAdmin4中找到正确的数据
"""

import psycopg2
import sys

def check_postgresql_data():
    """检查PostgreSQL数据库中的数据"""
    print("🔍 检查PostgreSQL数据库中的数据")
    print("="*50)
    
    # 连接配置
    config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',  # 这是我们迁移到的数据库
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        # 连接数据库
        conn = psycopg2.connect(**config)
        cursor = conn.cursor()
        
        print(f"✅ 成功连接到数据库: {config['database']}")
        print(f"📍 pgAdmin4连接信息:")
        print(f"   主机: {config['host']}")
        print(f"   端口: {config['port']}")
        print(f"   数据库名: {config['database']}")
        print(f"   用户名: {config['user']}")
        print()
        
        # 检查所有表
        print("📊 检查数据库中的表:")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ 没有找到任何表！")
            print("可能的原因:")
            print("1. 数据迁移到了错误的数据库")
            print("2. 表在不同的schema中")
            print("3. pgAdmin4连接到了错误的数据库")
            return False
        
        print(f"找到 {len(tables)} 个表:")
        total_rows = 0
        
        for (table_name,) in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                row_count = cursor.fetchone()[0]
                total_rows += row_count
                print(f"  ✅ {table_name}: {row_count:,} 行")
            except Exception as e:
                print(f"  ❌ {table_name}: 查询失败 - {e}")
        
        print(f"\n📈 总数据量: {total_rows:,} 行")
        
        # 检查视图
        print("\n👁️ 检查视图:")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        views = cursor.fetchall()
        if views:
            print(f"找到 {len(views)} 个视图:")
            for (view_name,) in views:
                print(f"  👁️ {view_name}")
        else:
            print("❌ 没有找到任何视图")
        
        # 检查索引
        print("\n🔍 检查索引:")
        cursor.execute("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE schemaname = 'public'
            AND indexname NOT LIKE '%_pkey'
            ORDER BY indexname
        """)
        
        indexes = cursor.fetchall()
        if indexes:
            print(f"找到 {len(indexes)} 个自定义索引:")
            for (index_name,) in indexes:
                print(f"  🔍 {index_name}")
        else:
            print("❌ 没有找到自定义索引")
        
        # 显示示例数据
        print("\n📋 示例数据 (IOT_Sales表前5行):")
        try:
            cursor.execute('SELECT * FROM "IOT_Sales" LIMIT 5')
            rows = cursor.fetchall()
            
            # 获取列名
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'IOT_Sales' 
                ORDER BY ordinal_position
            """)
            columns = [row[0] for row in cursor.fetchall()]
            
            if rows:
                print(f"列名: {', '.join(columns[:5])}...")  # 只显示前5列
                for i, row in enumerate(rows):
                    print(f"  行{i+1}: {', '.join(str(row[j]) for j in range(min(5, len(row))))}...")
            else:
                print("  没有数据")
                
        except Exception as e:
            print(f"  查询示例数据失败: {e}")
        
        conn.close()
        
        print("\n📋 pgAdmin4使用指南:")
        print("1. 打开pgAdmin4")
        print("2. 创建服务器连接:")
        print(f"   - 名称: 任意名称 (如: Local PostgreSQL)")
        print(f"   - 主机: {config['host']}")
        print(f"   - 端口: {config['port']}")
        print(f"   - 维护数据库: {config['database']}")
        print(f"   - 用户名: {config['user']}")
        print(f"   - 密码: {config['password']}")
        print("3. 连接后展开:")
        print(f"   服务器 > {config['database']} > Schemas > public > Tables")
        print("4. 在Tables下可以看到所有迁移的表")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查PostgreSQL服务是否运行")
        print("2. 检查用户名和密码是否正确")
        print("3. 检查数据库名称是否正确")
        return False

def main():
    """主函数"""
    success = check_postgresql_data()
    
    if success:
        print("\n✅ 数据检查完成！")
        print("您应该能在pgAdmin4中看到所有迁移的数据了。")
    else:
        print("\n❌ 数据检查失败！")
        print("请检查连接配置或联系技术支持。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
