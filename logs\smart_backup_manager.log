2025-06-18 14:09:02 - smart_backup_manager - INFO - 智能备份管理器已初始化 - 数据库: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, 备份目录: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 14:09:02 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_创建Refunding和Close表系统_20250618_140902.db
2025-06-18 14:09:02 - smart_backup_manager - INFO - 🔄 开始执行操作: 创建Refunding和Close表系统
2025-06-18 16:28:50 - smart_backup_manager - INFO - 智能备份管理器已初始化 - 数据库: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, 备份目录: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 16:28:51 - smart_backup_manager - INFO - 智能备份管理器已初始化 - 数据库: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, 备份目录: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 16:30:05 - smart_backup_manager - INFO - 智能备份管理器已初始化 - 数据库: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, 备份目录: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 16:30:24 - smart_backup_manager - INFO - 智能备份管理器已初始化 - 数据库: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, 备份目录: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 16:43:28 - smart_backup_manager - INFO - 智能备份管理器已初始化 - 数据库: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, 备份目录: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 17:08:51 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 17:08:51 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_导入_IOT_online_order_Flow20250618_1xlsx_20250618_170851.db
2025-06-18 17:08:51 - smart_backup_manager - INFO - 🔄 开始执行操作: 导入_IOT_online order Flow20250618 (1).xlsx
2025-06-18 17:08:53 - smart_backup_manager - ERROR - ❌ 操作失败: 导入_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: 加载数据失败: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-18 17:09:02 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250618_170902.db
2025-06-18 17:09:03 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_导入_IOT_online_order_Flow20250618_1xlsx_20250618_170851.db
2025-06-18 17:21:27 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-18 17:21:27 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250618_172127.db
2025-06-18 17:21:27 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-18 17:21:29 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-18 17:21:35 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250618_172134.db
2025-06-18 17:21:35 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250618_172127.db
2025-06-19 08:38:52 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 08:38:53 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_083852.db
2025-06-19 08:38:53 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 08:38:57 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:40:18 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_084018.db
2025-06-19 08:40:19 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_083852.db
2025-06-19 08:50:50 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 08:50:50 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_085050.db
2025-06-19 08:50:50 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 08:50:52 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:50:55 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_085054.db
2025-06-19 08:50:55 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_085050.db
2025-06-19 08:57:12 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 08:57:12 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_085712.db
2025-06-19 08:57:12 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 08:57:15 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Copartner name
2025-06-19 08:57:19 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_085719.db
2025-06-19 08:57:19 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_085712.db
2025-06-19 09:03:30 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 09:03:30 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_090330.db
2025-06-19 09:03:30 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 09:03:32 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DatabaseError: Failed to check duplicate data: You are trying to merge on int64 and object columns for key 'Equipment_ID'. If you wish to proceed you should use pd.concat
2025-06-19 09:03:34 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_090334.db
2025-06-19 09:03:35 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_090330.db
2025-06-19 09:16:55 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 09:16:56 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_091655.db
2025-06-19 09:16:56 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 09:16:59 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named Transaction_ID
2025-06-19 09:17:02 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_091702.db
2025-06-19 09:17:02 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_091655.db
2025-06-19 09:21:48 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 09:21:48 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618_1xlsx_20250619_092148.db
2025-06-19 09:21:48 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 09:21:52 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_online order Flow20250618 (1).xlsx
2025-06-19 09:22:25 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 09:22:25 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250618xlsx_20250619_092225.db
2025-06-19 09:22:25 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250618.xlsx
2025-06-19 09:22:27 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_online order Flow20250618.xlsx
2025-06-19 12:08:02 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 12:08:03 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250619_1xlsx_20250619_120802.db
2025-06-19 12:08:03 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250619 (1).xlsx
2025-06-19 12:08:04 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_online order Flow20250619 (1).xlsx
2025-06-19 12:08:22 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 12:08:22 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250619xlsx_20250619_120822.db
2025-06-19 12:08:22 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250619.xlsx
2025-06-19 12:08:24 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_online order Flow20250619.xlsx
2025-06-19 12:09:40 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 12:09:40 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_online_order_Flow20250619_1xlsx_20250619_120940.db
2025-06-19 12:09:40 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_online order Flow20250619 (1).xlsx
2025-06-19 12:09:41 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_online order Flow20250619 (1).xlsx
2025-06-19 12:10:15 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 12:10:15 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_online_order_Flow20250619_2xlsx_20250619_121015.db
2025-06-19 12:10:15 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_online order Flow20250619 (2).xlsx
2025-06-19 12:10:17 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_online order Flow20250619 (2).xlsx
2025-06-19 16:52:47 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 16:52:48 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 16:52:49 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:03:48 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:03:51 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:03:54 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:09:46 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:09:47 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:09:48 - smart_backup_manager - INFO - Smart backup manager initialized - DB: database\sales_reports.db, Backup dir: database\backups
2025-06-19 17:16:44 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:16:44 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_130625_CHINA_ZEROxlsx_20250619_171644.db
2025-06-19 17:16:44 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_130625 CHINA ZERO.xlsx
2025-06-19 17:16:45 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_130625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:48 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_171647.db
2025-06-19 17:16:48 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_130625_CHINA_ZEROxlsx_20250619_171644.db
2025-06-19 17:16:51 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:16:51 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_140625_CHINA_ZEROxlsx_20250619_171651.db
2025-06-19 17:16:51 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_140625 CHINA ZERO.xlsx
2025-06-19 17:16:51 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_140625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:56 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:16:56 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_150625_CHINA_ZEROxlsx_20250619_171656.db
2025-06-19 17:16:56 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_150625 CHINA ZERO.xlsx
2025-06-19 17:16:57 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_150625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:59 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_恢复前备份_20250619_171659.db
2025-06-19 17:16:59 - smart_backup_manager - INFO - 数据库恢复成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_150625_CHINA_ZEROxlsx_20250619_171656.db
2025-06-19 17:22:43 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:22:43 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_130625_CHINA_ZEROxlsx_20250619_172243.db
2025-06-19 17:22:43 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_130625 CHINA ZERO.xlsx
2025-06-19 17:22:44 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_130625 CHINA ZERO.xlsx
2025-06-19 17:22:44 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:22:45 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_140625_CHINA_ZEROxlsx_20250619_172244.db
2025-06-19 17:22:45 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_140625 CHINA ZERO.xlsx
2025-06-19 17:22:45 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_140625 CHINA ZERO.xlsx
2025-06-19 17:22:46 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:22:46 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_150625_CHINA_ZEROxlsx_20250619_172246.db
2025-06-19 17:22:46 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_150625 CHINA ZERO.xlsx
2025-06-19 17:22:47 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_150625 CHINA ZERO.xlsx
2025-06-19 17:23:10 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:23:10 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_130625_CHINA_IOTxlsx_20250619_172310.db
2025-06-19 17:23:10 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_130625 CHINA IOT.xlsx
2025-06-19 17:23:12 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_130625 CHINA IOT.xlsx
2025-06-19 17:23:13 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:23:13 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_140625_CHINA_IOTxlsx_20250619_172313.db
2025-06-19 17:23:13 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_140625 CHINA IOT.xlsx
2025-06-19 17:23:16 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_140625 CHINA IOT.xlsx
2025-06-19 17:23:17 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-19 17:23:17 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_150625_CHINA_IOTxlsx_20250619_172317.db
2025-06-19 17:23:17 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_150625 CHINA IOT.xlsx
2025-06-19 17:23:21 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_150625 CHINA IOT.xlsx
2025-06-20 08:42:55 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-20 08:42:56 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_130625_CHINA_IOTxlsx_20250620_084255.db
2025-06-20 08:42:56 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_130625 CHINA IOT.xlsx
2025-06-20 08:42:58 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_130625 CHINA IOT.xlsx
2025-06-20 08:43:47 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-20 08:43:48 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_140625_CHINA_IOTxlsx_20250620_084347.db
2025-06-20 08:43:48 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_140625 CHINA IOT.xlsx
2025-06-20 08:43:51 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_140625 CHINA IOT.xlsx
2025-06-20 08:43:51 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-20 08:43:52 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_150625_CHINA_IOTxlsx_20250620_084351.db
2025-06-20 08:43:52 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_150625 CHINA IOT.xlsx
2025-06-20 08:43:55 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_150625 CHINA IOT.xlsx
2025-06-20 08:44:23 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-20 08:44:24 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_130625_CHINA_ZEROxlsx_20250620_084424.db
2025-06-20 08:44:24 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_130625 CHINA ZERO.xlsx
2025-06-20 08:44:24 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_130625 CHINA ZERO.xlsx
2025-06-20 08:44:25 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-20 08:44:25 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_140625_CHINA_ZEROxlsx_20250620_084425.db
2025-06-20 08:44:25 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_140625 CHINA ZERO.xlsx
2025-06-20 08:44:25 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_140625 CHINA ZERO.xlsx
2025-06-20 08:44:26 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-20 08:44:26 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_150625_CHINA_ZEROxlsx_20250620_084426.db
2025-06-20 08:44:26 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_150625 CHINA ZERO.xlsx
2025-06-20 08:44:27 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_150625 CHINA ZERO.xlsx
2025-06-23 09:26:34 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-23 09:26:35 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_160625_CHINA_IOTxlsx_20250623_092634.db
2025-06-23 09:26:35 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_160625 CHINA IOT.xlsx
2025-06-23 09:26:38 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_160625 CHINA IOT.xlsx
2025-06-23 16:33:11 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-23 16:33:12 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_170625_CHINA_ZEROxlsx_20250623_163311.db
2025-06-23 16:33:12 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_170625 CHINA ZERO.xlsx
2025-06-23 16:33:12 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_170625 CHINA ZERO.xlsx
2025-06-23 16:33:13 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-23 16:33:13 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_ZERO_180625_CHINA_ZEROxlsx_20250623_163313.db
2025-06-23 16:33:13 - smart_backup_manager - INFO - 🔄 开始执行操作: import_ZERO_180625 CHINA ZERO.xlsx
2025-06-23 16:33:14 - smart_backup_manager - INFO - ✅ 操作成功完成: import_ZERO_180625 CHINA ZERO.xlsx
2025-06-23 16:33:32 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-23 16:33:33 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_170625_CHINA_IOTxlsx_20250623_163332.db
2025-06-23 16:33:33 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_170625 CHINA IOT.xlsx
2025-06-23 16:33:35 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_170625 CHINA IOT.xlsx
2025-06-23 16:33:36 - smart_backup_manager - INFO - Smart backup manager initialized - DB: C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db, Backup dir: C:\Users\<USER>\Desktop\Day Report\database\backups
2025-06-23 16:33:37 - smart_backup_manager - INFO - 数据库备份成功: C:\Users\<USER>\Desktop\Day Report\database\backups\backup_操作前_import_IOT_180625_CHINA_IOTxlsx_20250623_163336.db
2025-06-23 16:33:37 - smart_backup_manager - INFO - 🔄 开始执行操作: import_IOT_180625 CHINA IOT.xlsx
2025-06-23 16:33:40 - smart_backup_manager - INFO - ✅ 操作成功完成: import_IOT_180625 CHINA IOT.xlsx
