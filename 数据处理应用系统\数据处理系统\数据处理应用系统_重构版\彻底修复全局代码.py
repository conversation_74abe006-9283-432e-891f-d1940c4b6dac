# -*- coding: utf-8 -*-
"""
彻底修复全局代码脚本
移除所有全局执行的代码，只保留函数和类定义
"""

import re
import os

def remove_all_global_code():
    """移除所有全局执行的代码"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分割成行
    lines = content.split('\n')
    new_lines = []
    
    # 标记是否在函数/类定义内
    in_function_or_class = False
    indent_level = 0
    
    for i, line in enumerate(lines):
        stripped = line.strip()
        
        # 检查是否是函数或类定义
        if (stripped.startswith('def ') or 
            stripped.startswith('class ') or
            stripped.startswith('if __name__ == "__main__"')):
            in_function_or_class = True
            indent_level = len(line) - len(line.lstrip())
            new_lines.append(line)
            continue
        
        # 如果在函数/类内，检查缩进
        if in_function_or_class:
            current_indent = len(line) - len(line.lstrip())
            # 如果缩进回到函数/类级别或更少，且不是空行或注释，则退出函数/类
            if (current_indent <= indent_level and 
                stripped and 
                not stripped.startswith('#') and
                not stripped.startswith('"""') and
                not stripped.startswith("'''")):
                in_function_or_class = False
            else:
                new_lines.append(line)
                continue
        
        # 如果不在函数/类内，检查是否是允许的全局代码
        if (stripped.startswith('#') or  # 注释
            stripped.startswith('import ') or  # 导入语句
            stripped.startswith('from ') or  # 导入语句
            stripped.startswith('"""') or  # 文档字符串
            stripped.startswith("'''") or  # 文档字符串
            stripped == '' or  # 空行
            'log_manager = LogManager()' in stripped or  # 全局日志管理器
            stripped.startswith('warnings.') or  # 警告设置
            stripped.startswith('pd.set_option') or  # pandas设置
            stripped.startswith('os.environ') or  # 环境变量设置
            stripped.startswith('locale.') or  # locale设置
            stripped.startswith('sys.') or  # sys设置
            stripped.startswith('builtins.') or  # builtins设置
            'setup_utf8_output()' in stripped):  # UTF8设置
            new_lines.append(line)
        else:
            # 这是需要移除的全局执行代码
            print(f"移除全局代码: {stripped[:50]}...")
    
    # 重新组合内容
    modified_content = '\n'.join(new_lines)
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("✅ 彻底移除全局执行代码完成！")

def test_script_loading():
    """测试脚本是否能正常加载"""
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    try:
        # 测试语法
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        import ast
        ast.parse(content)
        print("✅ 语法检查通过！")
        
        # 测试导入（这会执行全局代码）
        import subprocess
        import sys
        
        result = subprocess.run([
            sys.executable, '-c', f'import sys; sys.path.insert(0, r"{os.path.dirname(script_path)}"); import importlib.util; spec = importlib.util.spec_from_file_location("test_module", r"{script_path}"); module = importlib.util.module_from_spec(spec); print("导入成功")'
        ], capture_output=True, text=True, timeout=5, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 脚本导入测试通过！")
            return True
        else:
            print(f"❌ 脚本导入失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本导入超时（仍然卡住）")
        return False
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        return False

def test_script_execution():
    """测试脚本执行"""
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    try:
        import subprocess
        import sys
        
        # 测试不带参数运行
        result = subprocess.run([
            sys.executable, script_path
        ], capture_output=True, text=True, timeout=5, encoding='utf-8')
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if "请提供文件路径参数" in result.stdout:
            print("✅ 脚本执行测试通过！")
            return True
        else:
            print("❌ 脚本执行异常")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行测试错误: {e}")
        return False

if __name__ == "__main__":
    print("开始彻底修复全局代码...")
    
    # 1. 移除所有全局执行代码
    remove_all_global_code()
    
    # 2. 测试脚本加载
    print("\n测试脚本加载...")
    if test_script_loading():
        print("\n测试脚本执行...")
        if test_script_execution():
            print("\n🎉 修复完成！脚本现在可以正常运行了。")
        else:
            print("\n⚠️ 脚本执行仍有问题。")
    else:
        print("\n⚠️ 脚本加载仍有问题。")
