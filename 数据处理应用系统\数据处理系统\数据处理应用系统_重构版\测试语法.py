import ast
import sys

try:
    with open(r'数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    ast.parse(content)
    print("✅ 语法检查通过！")
    
except SyntaxError as e:
    print(f"❌ 语法错误:")
    print(f"   行号: {e.lineno}")
    print(f"   错误: {e.msg}")
    print(f"   代码: {e.text}")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")
