# -*- coding: utf-8 -*-
"""
直接执行PostgreSQL迁移脚本
先复制SQLite数据库，然后使用复制的数据库进行迁移
"""

import os
import sys
import shutil
import sqlite3
import psycopg2
import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DirectMigrationExecutor:
    """直接迁移执行器"""
    
    def __init__(self):
        # PostgreSQL配置（用户提供的）
        self.pg_config = {
            'host': 'localhost',
            'port': '5432',
            'database': 'sales_reports',
            'user': 'zerochon',
            'password': 'zerochon'
        }
        
        # 原始数据库路径
        self.original_db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        
        # 复制的数据库路径
        self.backup_db_path = f"sales_reports_migration_copy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        self.migration_log = []
    
    def execute_migration(self) -> bool:
        """执行完整迁移流程"""
        print("🚀 开始SQLite到PostgreSQL直接迁移")
        print("="*60)
        print(f"源数据库: {self.original_db_path}")
        print(f"目标数据库: {self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}")
        print("="*60)
        
        try:
            # 步骤1: 复制数据库
            if not self._copy_database():
                return False
            
            # 步骤2: 分析数据库结构
            analysis = self._analyze_database()
            if not analysis:
                return False
            
            # 步骤3: 连接PostgreSQL
            pg_conn = self._connect_postgresql()
            if not pg_conn:
                return False
            
            # 步骤4: 创建表结构
            if not self._create_tables(pg_conn, analysis['tables']):
                pg_conn.close()
                return False
            
            # 步骤5: 迁移数据
            if not self._migrate_data(pg_conn, analysis['tables']):
                pg_conn.close()
                return False
            
            # 步骤6: 创建视图
            if not self._create_views(pg_conn, analysis['views']):
                pg_conn.close()
                return False
            
            # 步骤7: 创建索引
            if not self._create_indexes(pg_conn, analysis['indexes']):
                pg_conn.close()
                return False
            
            # 步骤8: 验证迁移
            if not self._verify_migration(pg_conn, analysis):
                pg_conn.close()
                return False
            
            # 步骤9: 更新应用配置
            if not self._update_application_config():
                pg_conn.close()
                return False
            
            pg_conn.close()
            
            # 步骤10: 生成报告
            self._generate_final_report()
            
            print("\n🎉 迁移成功完成！")
            return True
            
        except Exception as e:
            logger.error(f"迁移过程中出错: {e}")
            print(f"❌ 迁移失败: {e}")
            return False
    
    def _copy_database(self) -> bool:
        """复制SQLite数据库"""
        print("📋 步骤1: 复制SQLite数据库...")
        
        try:
            if not os.path.exists(self.original_db_path):
                print(f"❌ 原始数据库不存在: {self.original_db_path}")
                return False
            
            # 复制数据库文件
            shutil.copy2(self.original_db_path, self.backup_db_path)
            
            # 验证复制
            if os.path.exists(self.backup_db_path):
                original_size = os.path.getsize(self.original_db_path)
                backup_size = os.path.getsize(self.backup_db_path)
                
                if original_size == backup_size:
                    print(f"✅ 数据库复制成功: {self.backup_db_path}")
                    print(f"   文件大小: {original_size:,} 字节")
                    self.migration_log.append(f"数据库复制成功: {self.backup_db_path}")
                    return True
                else:
                    print(f"❌ 复制文件大小不匹配")
                    return False
            else:
                print(f"❌ 复制文件不存在")
                return False
                
        except Exception as e:
            print(f"❌ 复制数据库失败: {e}")
            return False
    
    def _analyze_database(self) -> Dict[str, Any]:
        """分析数据库结构"""
        print("📊 步骤2: 分析数据库结构...")
        
        try:
            conn = sqlite3.connect(self.backup_db_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            table_names = [row[0] for row in cursor.fetchall()]
            
            tables = {}
            for table_name in table_names:
                print(f"  分析表: {table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                # 获取行数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                
                tables[table_name] = {
                    'columns': columns,
                    'row_count': row_count,
                    'postgresql_ddl': self._generate_postgresql_ddl(table_name, columns)
                }
                
                print(f"    {row_count:,} 行, {len(columns)} 列")
            
            # 获取视图
            cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='view'")
            views_data = cursor.fetchall()
            views = {}
            for view_name, view_sql in views_data:
                views[view_name] = {
                    'original_sql': view_sql,
                    'postgresql_sql': self._convert_view_sql(view_sql)
                }
            
            # 获取索引
            cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND sql IS NOT NULL")
            indexes_data = cursor.fetchall()
            indexes = {}
            for index_name, index_sql in indexes_data:
                if not index_name.startswith('sqlite_'):
                    indexes[index_name] = {
                        'original_sql': index_sql,
                        'postgresql_sql': index_sql  # 索引语法基本兼容
                    }
            
            conn.close()
            
            analysis = {
                'tables': tables,
                'views': views,
                'indexes': indexes
            }
            
            print(f"✅ 分析完成: {len(tables)} 表, {len(views)} 视图, {len(indexes)} 索引")
            return analysis
            
        except Exception as e:
            print(f"❌ 分析数据库失败: {e}")
            return {}
    
    def _generate_postgresql_ddl(self, table_name: str, columns: List) -> str:
        """生成PostgreSQL DDL"""
        pg_columns = []
        
        for col in columns:
            col_name = col[1]
            col_type = col[2].upper()
            not_null = bool(col[3])
            default_value = col[4]
            is_primary_key = bool(col[5])
            
            # 数据类型映射
            if col_type in ['INTEGER', 'INT']:
                pg_type = 'INTEGER'
            elif col_type in ['TEXT', 'VARCHAR']:
                pg_type = 'TEXT'
            elif col_type in ['REAL', 'FLOAT']:
                pg_type = 'REAL'
            elif col_type == 'BLOB':
                pg_type = 'BYTEA'
            else:
                pg_type = 'TEXT'  # 默认
            
            # 构建列定义
            col_def = f'    "{col_name}" {pg_type}'
            
            if is_primary_key:
                col_def += ' PRIMARY KEY'
            elif not_null:
                col_def += ' NOT NULL'
            
            if default_value is not None:
                if isinstance(default_value, str):
                    col_def += f" DEFAULT '{default_value}'"
                else:
                    col_def += f' DEFAULT {default_value}'
            
            pg_columns.append(col_def)
        
        return f'CREATE TABLE "{table_name}" (\n' + ',\n'.join(pg_columns) + '\n);'
    
    def _convert_view_sql(self, view_sql: str) -> str:
        """转换视图SQL"""
        # 基本的SQL转换
        pg_sql = view_sql
        
        # SQLite函数到PostgreSQL的映射
        replacements = {
            'SUBSTR(': 'SUBSTRING(',
            'LENGTH(': 'CHAR_LENGTH(',
            'DATETIME(': 'TO_TIMESTAMP(',
        }
        
        for sqlite_func, pg_func in replacements.items():
            pg_sql = pg_sql.replace(sqlite_func, pg_func)
        
        return pg_sql
    
    def _connect_postgresql(self) -> psycopg2.extensions.connection:
        """连接PostgreSQL"""
        print("🔌 步骤3: 连接PostgreSQL...")
        
        try:
            conn = psycopg2.connect(**self.pg_config)
            conn.autocommit = True
            print("✅ PostgreSQL连接成功")
            return conn
        except Exception as e:
            print(f"❌ PostgreSQL连接失败: {e}")
            return None
    
    def _create_tables(self, pg_conn, tables: Dict[str, Any]) -> bool:
        """创建表结构"""
        print("📊 步骤4: 创建表结构...")
        
        try:
            cursor = pg_conn.cursor()
            
            for table_name, table_info in tables.items():
                print(f"  创建表: {table_name}")
                
                # 删除表如果存在
                cursor.execute(f'DROP TABLE IF EXISTS "{table_name}" CASCADE')
                
                # 创建表
                ddl = table_info['postgresql_ddl']
                cursor.execute(ddl)
                
                print(f"    ✅ 表 {table_name} 创建成功")
            
            print("✅ 所有表创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建表失败: {e}")
            return False
    
    def _migrate_data(self, pg_conn, tables: Dict[str, Any]) -> bool:
        """迁移数据"""
        print("📦 步骤5: 迁移数据...")
        
        try:
            sqlite_conn = sqlite3.connect(self.backup_db_path)
            pg_cursor = pg_conn.cursor()
            
            total_rows = 0
            for table_name, table_info in tables.items():
                row_count = table_info['row_count']
                if row_count == 0:
                    print(f"  跳过空表: {table_name}")
                    continue
                
                print(f"  迁移表: {table_name} ({row_count:,} 行)")
                
                # 读取数据
                df = pd.read_sql_query(f'SELECT * FROM "{table_name}"', sqlite_conn)
                
                # 准备插入语句
                columns = [col[1] for col in table_info['columns']]
                quoted_columns = [f'"{col}"' for col in columns]
                placeholders = ', '.join(['%s'] * len(columns))
                insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'
                
                # 批量插入
                batch_size = 1000
                for i in range(0, len(df), batch_size):
                    batch_df = df.iloc[i:i+batch_size]
                    
                    batch_data = []
                    for _, row in batch_df.iterrows():
                        row_data = []
                        for col_name in columns:
                            value = row[col_name] if col_name in row else None
                            if pd.isna(value):
                                value = None
                            row_data.append(value)
                        batch_data.append(tuple(row_data))
                    
                    pg_cursor.executemany(insert_sql, batch_data)
                    
                    current_batch = min(i + batch_size, len(df))
                    print(f"    进度: {current_batch:,}/{len(df):,} 行")
                
                total_rows += len(df)
                print(f"    ✅ 表 {table_name} 迁移完成")
            
            sqlite_conn.close()
            print(f"✅ 数据迁移完成，总计: {total_rows:,} 行")
            return True
            
        except Exception as e:
            print(f"❌ 数据迁移失败: {e}")
            return False
    
    def _create_views(self, pg_conn, views: Dict[str, Any]) -> bool:
        """创建视图"""
        if not views:
            print("👁️ 步骤6: 无视图需要创建")
            return True
        
        print("👁️ 步骤6: 创建视图...")
        
        try:
            cursor = pg_conn.cursor()
            
            for view_name, view_info in views.items():
                print(f"  创建视图: {view_name}")
                
                try:
                    # 删除视图如果存在
                    cursor.execute(f'DROP VIEW IF EXISTS "{view_name}" CASCADE')
                    
                    # 创建视图
                    cursor.execute(view_info['postgresql_sql'])
                    print(f"    ✅ 视图 {view_name} 创建成功")
                except Exception as e:
                    print(f"    ⚠️ 视图 {view_name} 创建失败: {e}")
                    # 继续处理其他视图
            
            print("✅ 视图创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建视图失败: {e}")
            return False
    
    def _create_indexes(self, pg_conn, indexes: Dict[str, Any]) -> bool:
        """创建索引"""
        if not indexes:
            print("🔍 步骤7: 无索引需要创建")
            return True
        
        print("🔍 步骤7: 创建索引...")
        
        try:
            cursor = pg_conn.cursor()
            
            for index_name, index_info in indexes.items():
                print(f"  创建索引: {index_name}")
                
                try:
                    # 删除索引如果存在
                    cursor.execute(f'DROP INDEX IF EXISTS "{index_name}"')
                    
                    # 创建索引
                    cursor.execute(index_info['postgresql_sql'])
                    print(f"    ✅ 索引 {index_name} 创建成功")
                except Exception as e:
                    print(f"    ⚠️ 索引 {index_name} 创建失败: {e}")
                    # 继续处理其他索引
            
            print("✅ 索引创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建索引失败: {e}")
            return False
    
    def _verify_migration(self, pg_conn, analysis: Dict[str, Any]) -> bool:
        """验证迁移结果"""
        print("🔍 步骤8: 验证迁移结果...")
        
        try:
            cursor = pg_conn.cursor()
            sqlite_conn = sqlite3.connect(self.backup_db_path)
            
            verification_passed = True
            
            for table_name, table_info in analysis['tables'].items():
                # 检查行数
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                pg_count = cursor.fetchone()[0]
                
                sqlite_count = table_info['row_count']
                
                if pg_count == sqlite_count:
                    print(f"  ✅ 表 {table_name}: {pg_count:,} 行 (匹配)")
                else:
                    print(f"  ❌ 表 {table_name}: PostgreSQL={pg_count:,}, SQLite={sqlite_count:,}")
                    verification_passed = False
            
            sqlite_conn.close()
            
            if verification_passed:
                print("✅ 迁移验证通过")
            else:
                print("❌ 迁移验证失败")
            
            return verification_passed
            
        except Exception as e:
            print(f"❌ 验证迁移失败: {e}")
            return False
    
    def _update_application_config(self) -> bool:
        """更新应用配置"""
        print("🔧 步骤9: 更新应用配置...")
        
        try:
            config_content = f"""[Database]
db_type = postgresql
db_host = {self.pg_config['host']}
db_port = {self.pg_config['port']}
db_name = {self.pg_config['database']}
db_user = {self.pg_config['user']}
db_password = {self.pg_config['password']}

[Scripts]
intelligent_processor = report 脚本 3.0.py
modular_processor = report 模块化设计 7.0.py
refund_script = Refund_process_修复版.py
refund_script_optimized = scripts/refund_process_optimized.py
data_import_script = 数据导入脚本.py
data_import_script_optimized = scripts/data_import_optimized.py

[UI]
window_width = 900
window_height = 700
theme = iphone_style

[Files]
file_separator = ||
temp_dir = temp_data
backup_dir = backups
"""
            
            # 备份原配置
            if os.path.exists("config.ini"):
                backup_config = f"config_sqlite_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ini"
                shutil.copy2("config.ini", backup_config)
                print(f"  ✅ 原配置已备份: {backup_config}")
            
            # 写入新配置
            with open("config.ini", 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print("✅ 应用配置已更新")
            return True
            
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
            return False
    
    def _generate_final_report(self):
        """生成最终报告"""
        print("📋 步骤10: 生成迁移报告...")
        
        report = {
            'migration_date': datetime.now().isoformat(),
            'source_database': self.original_db_path,
            'backup_database': self.backup_db_path,
            'target_database': f"{self.pg_config['host']}:{self.pg_config['port']}/{self.pg_config['database']}",
            'migration_log': self.migration_log,
            'postgresql_config': {k: v for k, v in self.pg_config.items() if k != 'password'}
        }
        
        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"✅ 迁移报告已生成: {report_file}")
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")

def main():
    """主函数"""
    print("🚀 SQLite到PostgreSQL直接迁移执行器")
    print("配置信息:")
    print("- 数据库类型: PostgreSQL")
    print("- 主机: localhost:5432")
    print("- 数据库: sales_reports")
    print("- 用户: zerochon")
    print()
    
    executor = DirectMigrationExecutor()
    success = executor.execute_migration()
    
    if success:
        print("\n🎉 迁移成功完成！")
        print("\n📋 后续步骤:")
        print("1. 重启应用程序以使用PostgreSQL")
        print("2. 测试所有功能是否正常")
        print("3. 设置PostgreSQL定期备份")
        print("4. 监控应用性能")
        return 0
    else:
        print("\n❌ 迁移失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
