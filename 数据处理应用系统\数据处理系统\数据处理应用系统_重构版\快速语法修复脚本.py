# -*- coding: utf-8 -*-
"""
快速语法修复脚本
修复所有不完整的log_manager调用
"""

import re
import os

def fix_incomplete_log_calls():
    """修复不完整的log_manager调用"""
    
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    # 读取文件内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复不完整的log_manager.log_detailed调用
    patterns_to_fix = [
        # 单独的log_manager.log_detailed行
        (r'^(\s*)log_manager\.log_detailed$', r'\1log_manager.log_detailed("详细信息已简化")'),
        
        # 不完整的调用后面跟着注释
        (r'^(\s*)log_manager\.log_detailed\s*$', r'\1log_manager.log_detailed("详细信息已简化")'),
        
        # 修复特定的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*#', r'log_manager.log_detailed("详细信息已简化")\n    #'),
        
        # 修复在条件语句中的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*print', r'log_manager.log_detailed("详细信息已简化")\n    print'),
        
        # 修复在函数中的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*return', r'log_manager.log_detailed("详细信息已简化")\n    return'),
        
        # 修复在循环中的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*for', r'log_manager.log_detailed("详细信息已简化")\n    for'),
        
        # 修复在条件语句中的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*if', r'log_manager.log_detailed("详细信息已简化")\n    if'),
        
        # 修复在else语句中的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*else', r'log_manager.log_detailed("详细信息已简化")\n    else'),
        
        # 修复在函数定义中的不完整调用
        (r'log_manager\.log_detailed\s*\n\s*def', r'log_manager.log_detailed("详细信息已简化")\n\ndef'),
    ]
    
    modified_content = content
    fix_count = 0
    
    for pattern, replacement in patterns_to_fix:
        matches = re.findall(pattern, modified_content, re.MULTILINE)
        if matches:
            modified_content = re.sub(pattern, replacement, modified_content, flags=re.MULTILINE)
            fix_count += len(matches)
            print(f"修复了 {len(matches)} 个不完整的log_manager调用")
    
    # 特殊处理：修复行末的不完整调用
    lines = modified_content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # 检查是否是不完整的log_manager.log_detailed调用
        if line.strip() == 'log_manager.log_detailed':
            # 查看下一行来确定合适的消息
            next_line = lines[i+1] if i+1 < len(lines) else ""
            
            if "# 详细日志已简化" in next_line:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("详细日志已简化")'))
            elif "print(" in next_line:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("处理信息")'))
            elif "return" in next_line:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("函数返回")'))
            elif "if" in next_line:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("条件检查")'))
            elif "else" in next_line:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("其他情况")'))
            elif "def" in next_line:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("函数定义")'))
            else:
                fixed_lines.append(line.replace('log_manager.log_detailed', 'log_manager.log_detailed("详细信息")'))
            
            fix_count += 1
        else:
            fixed_lines.append(line)
    
    modified_content = '\n'.join(fixed_lines)
    
    # 写回文件
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"\n✅ 语法修复完成！")
    print(f"总共修复了 {fix_count} 个不完整的log_manager调用")
    print(f"文件已更新: {script_path}")

def check_syntax_errors():
    """检查语法错误"""
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试编译代码检查语法
        compile(content, script_path, 'exec')
        print("✅ 语法检查通过！")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"行号: {e.lineno}")
        print(f"错误位置: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("开始快速语法修复...")
    
    # 1. 修复不完整的log_manager调用
    fix_incomplete_log_calls()
    
    # 2. 检查语法错误
    if check_syntax_errors():
        print("\n🎉 所有语法错误已修复！")
        print("现在可以正常运行应用程序了。")
    else:
        print("\n⚠️ 仍有语法错误需要手动修复。")
