2025-06-19 08:54:43 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Notes
2025-06-19 08:54:45 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Notes
2025-06-19 08:55:43 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 08:55:45 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:07:52 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: utils.exceptions.AppException.__init__() got multiple values for keyword argument 'details'
2025-06-19 09:13:13 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:13:16 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:15:31 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
2025-06-19 09:15:35 - data_import - ERROR - Smart file processing failed: test_unicode_excel.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Transaction_ID
