# -*- coding: utf-8 -*-
"""
测试列名映射和数据类型标准化功能
"""

import sys
import os
import pandas as pd

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_column_mapping():
    """测试列名映射功能"""
    print("Testing column name mapping...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含用户提到的列名的测试数据
        test_data = {
            'Copartner name': ['Partner1', 'Partner2', 'Partner3'],
            'Order No.': ['ORD001', 'ORD002', 'ORD003'],
            'Transaction Num': ['T001', 'T002', 'T003'],
            'Order types': ['Normal', 'VIP', 'Express'],
            'Order status': ['Finished', 'Refunded', 'Closed'],
            'Order price': [100.0, 200.0, 300.0],
            'Payment': ['Paid', 'Paid', 'Refunded'],
            'Order time': ['2024-12-19 10:00:00', '2024-12-19 10:01:00', '2024-12-19 10:02:00'],
            'Equipment ID': [1001, 1002, 1003],  # 数字类型
            'Equipment name': ['Equipment A', 'Equipment B', 'Equipment C'],
            'Branch name': ['Branch 1', 'Branch 2', 'Branch 3'],
            'Payment date': ['2024-12-19', '2024-12-19', '2024-12-19'],
            'User name': ['User1', 'User2', 'User3']
        }
        
        df = pd.DataFrame(test_data)
        print("Original DataFrame columns:")
        print(list(df.columns))
        print("\nOriginal data types:")
        print(df.dtypes)
        
        # 测试列名标准化
        df_standardized = processor._standardize_column_names(df)
        print("\nStandardized DataFrame columns:")
        print(list(df_standardized.columns))
        
        # 测试数据类型标准化
        df_final = processor._standardize_data_types(df_standardized)
        print("\nFinal data types:")
        print(df_final.dtypes)
        
        print("\nSample data:")
        print(df_final.head())
        
        return True
    except Exception as e:
        print(f"ERROR: Column mapping test failed - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processed_data():
    """测试已处理数据的列名映射"""
    print("\n" + "="*50)
    print("Testing processed data column mapping...")
    
    try:
        from scripts.data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含已处理数据列名的测试数据
        test_data = {
            'Copartner name': ['Partner1', 'Partner2'],
            'Order No.': ['ORD001', 'ORD002'],
            'Transaction Num': ['T001', 'T002'],
            'Order types': ['Normal', 'VIP'],
            'Order status': ['Finished', 'Refunded'],
            'Order price': [100.0, 200.0],
            'Payment': ['Paid', 'Refunded'],
            'Order time': ['2024-12-19 10:00:00', '2024-12-19 10:01:00'],
            'Equipment ID': [1001, 1002],
            'Equipment name': ['Equipment A', 'Equipment B'],
            'Branch name': ['Branch 1', 'Branch 2'],
            'Payment date': ['2024-12-19', '2024-12-19'],
            'User name': ['User1', 'User2'],
            'Time': ['10:00:00', '10:01:00'],  # 已处理数据的额外列
            'Matched Order ID': ['M001', 'M002'],  # 已处理数据的额外列
            'OrderTime_dt': ['2024-12-19 10:00:00', '2024-12-19 10:01:00']  # 已处理数据的额外列
        }
        
        df = pd.DataFrame(test_data)
        print("Original processed data columns:")
        print(list(df.columns))
        
        # 测试列名标准化
        df_standardized = processor._standardize_column_names(df)
        print("\nStandardized processed data columns:")
        print(list(df_standardized.columns))
        
        # 测试数据类型标准化
        df_final = processor._standardize_data_types(df_standardized)
        print("\nFinal processed data types:")
        print(df_final.dtypes)
        
        print("\nSample processed data:")
        print(df_final.head())
        
        return True
    except Exception as e:
        print(f"ERROR: Processed data test failed - {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== Column Mapping and Data Type Test ===")
    print("Testing column name standardization and data type conversion...")
    
    tests = [
        ("Raw Data Column Mapping", test_column_mapping),
        ("Processed Data Column Mapping", test_processed_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                print(f"PASSED: {test_name}")
                passed += 1
            else:
                print(f"FAILED: {test_name}")
        except Exception as e:
            print(f"ERROR in {test_name}: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("SUCCESS: All column mapping and data type fixes are working!")
        print("The system should now handle both raw and processed Excel data.")
        return 0
    else:
        print("WARNING: Some tests failed. Check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
