# coding: utf-8
import os
import shutil
import sqlite3
import datetime
import pandas as pd
import traceback
# from glob import glob  # 未使用，已注释
import sys

# 设置控制台输出编码为UTF-8
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# 从配置文件获取数据库路径
def get_db_path_from_config(cli_db_path=None):
    if cli_db_path:
        return cli_db_path
    import configparser
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
    default_db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
    
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        if 'Database' in config and 'db_path' in config['Database']:
            return config['Database']['db_path']
    
    return default_db_path

# 数据库路径 - 立即初始化以确保全局可用
DB_PATH = get_db_path_from_config()

# REFUND_DIRS, SUCCESS_LOG, FAIL_LOG, NOTE_LOG 的定义可以保留在这里，因为它们不直接依赖 DB_PATH
REFUND_DIRS = {
    "IOT": os.path.join(os.path.dirname(os.path.abspath(__file__)), "Refunding", "IOT"),
    "ZERO": os.path.join(os.path.dirname(os.path.abspath(__file__)), "Refunding", "ZERO")
}
# BACKUP_DIR 的定义需要移动到 main 函数中，因为它可能依赖于 DB_PATH
# SUCCESS_LOG, FAIL_LOG, NOTE_LOG 的定义也需要移动，因为它们依赖 BACKUP_DIR

REFUND_LIST_COLUMNS = [
    'Transaction Date','Settlement Date','Refund Date','Merchant Ref ID','Transaction ID','Channel','Order ID','Currency','Billing','Actual','Refund','MDR','GST','Status','Refund Fee','Quantity','Reference1','Reference2','PROCESS'
]


def move_file(src, dst):
    # 确保目标目录存在
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    
    # 尝试多次移动文件
    max_attempts = 3
    attempt = 0
    success = False
    
    while attempt < max_attempts and not success:
        try:
            # 如果目标文件已存在，先尝试删除
            if os.path.exists(dst):
                try:
                    os.remove(dst)
                except PermissionError:
                    # 如果无法删除，使用随机后缀创建新文件名
                    import random
                    base, ext = os.path.splitext(dst)
                    dst = f"{base}_{random.randint(1000, 9999)}{ext}"
            
            # 尝试使用shutil.copy2代替move，然后手动删除源文件
            shutil.copy2(src, dst)
            success = True
            
            # 尝试删除源文件，但如果失败也不阻止程序继续
            try:
                os.unlink(src)
            except PermissionError:
                print(f"警告：无法删除源文件 {src}，但已成功复制到 {dst}")
                
        except PermissionError as e:
            attempt += 1
            print(f"移动文件尝试 {attempt}/{max_attempts} 失败: {e}")
            # 等待一段时间再重试
            import time
            time.sleep(2)
    
    if not success:
        # 所有尝试都失败，记录错误但不中断程序
        print(f"警告：无法移动文件 {src} 到 {dst}，将继续处理其他文件")
        return False
    return True

class LogManager:
    def __init__(self, success_log, fail_log, note_log):
        self.success_log = success_log
        self.fail_log = fail_log
        self.note_log = note_log
        # 初始化csv失败日志
        if not os.path.exists(self.fail_log):
            with open(self.fail_log, 'w', encoding='utf-8') as f:
                f.write('Transaction Date,Order ID,Refund,报错原因\n')
    def log_success(self, msg):
        with open(self.success_log, "a", encoding="utf-8") as f:
            # 添加时间戳作为分隔符
            f.write(f"===== {datetime.datetime.now()} =====\n")
            # 写入详细信息（可能包含多行）
            f.write(f"{msg}\n")
            # 添加分隔线
            f.write("-" * 50 + "\n")
    def log_fail(self, row, reason):
        with open(self.fail_log, "a", encoding="utf-8") as f:
            # 保持CSV格式的第一行，但添加详细信息作为附加内容
            f.write(f"{row.get('Transaction Date','')},{row.get('Order ID','')},{row.get('Refund','')},处理失败\n")
            # 添加时间戳作为分隔符
            f.write(f"===== {datetime.datetime.now()} =====\n")
            # 写入详细原因（可能包含多行）
            f.write(f"{reason}\n")
            # 添加分隔线
            f.write("-" * 50 + "\n")
    def log_note(self, msg):
        with open(self.note_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")
    def clear_logs(self):
        for log in [self.success_log, self.fail_log, self.note_log]:
            if os.path.exists(log):
                os.remove(log)
    def archive_logs(self):
        today = datetime.datetime.now().strftime('%Y%m')
        for log in [self.success_log, self.fail_log, self.note_log]:
            if os.path.exists(log):
                base, ext = os.path.splitext(log)
                archive_name = f"{base}_{today}{ext}"
                shutil.move(log, archive_name)

class BackupManager:
    def __init__(self, backup_dir):
        self.backup_dir = backup_dir
        os.makedirs(self.backup_dir, exist_ok=True)
    def backup_table(self, table_name):
        backup_file = os.path.join(self.backup_dir, f"{table_name}_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        with sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES) as conn:
            # 设置数据库连接为UTF-8编码
            conn.text_factory = str
            # 关闭外键约束
            conn.execute("PRAGMA foreign_keys = OFF")
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            df.to_excel(backup_file, index=False)  # 移除 encoding 参数，新版 pandas 不支持
        return backup_file
    def restore_table_from_backup(self, table_name):
        files = [f for f in os.listdir(self.backup_dir) if f.startswith(table_name+"_backup") and f.endswith(".xlsx")]
        if not files:
            print(f"未找到{table_name}的备份文件")
            return
        latest = max(files, key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)))
        backup_file = os.path.join(self.backup_dir, latest)
        try:
            df = pd.read_excel(backup_file, engine='openpyxl')
            with sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES) as conn:
                # 设置数据库连接为UTF-8编码
                conn.text_factory = str
                # 关闭外键约束
                conn.execute("PRAGMA foreign_keys = OFF")
                # 清空表
                conn.execute(f"DELETE FROM {table_name}")
                
                # 处理数据类型
                if table_name == 'REFUND_LIST':
                    # 确保数值类型正确
                    for col in ['Billing', 'Actual', 'Refund', 'MDR', 'GST', 'Refund Fee']:
                        if col in df.columns:
                            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                    # 确保日期类型正确
                    for col in ['Transaction Date', 'Settlement Date', 'Refund Date']:
                        if col in df.columns:
                            df[col] = df[col].astype(str)
                
                # 恢复数据
                df.to_sql(table_name, conn, if_exists='append', index=False)
                conn.commit()
            print(f"{table_name}已恢复自{latest}")
        except Exception as e:
            print(f"恢复失败: {e}")
            traceback.print_exc()
    def archive_db(self):
        today = datetime.datetime.now().strftime('%Y%m')
        db_archive = os.path.join(self.backup_dir, f"sales_reports_{today}.db")
        if os.path.exists(DB_PATH):
            shutil.copy(DB_PATH, db_archive)

class RefundProcessor:
    def __init__(self, log_mgr, backup_mgr):
        self.log_mgr = log_mgr
        self.backup_mgr = backup_mgr
        
    def log_message(self, message, level='info'):
        """根据日志级别将消息转发到适当的LogManager方法"""
        if level == 'info' or level == 'note':
            self.log_mgr.log_note(message)
        elif level == 'warning' or level == 'warn':
            self.log_mgr.log_note(f"警告: {message}")
        elif level == 'error' or level == 'err':
            self.log_mgr.log_note(f"错误: {message}")
        elif level == 'popup_request':
            self.log_mgr.log_note(f"弹窗请求: {message}")
        else:
            self.log_mgr.log_note(message)
            
    def check_database_amount(self, conn, table_name, operation_desc="操作"):
        """检查数据库中的总金额并记录"""
        db = conn.cursor()
        try:
            db.execute(f"SELECT SUM(Order_price) FROM {table_name}")
            result = db.fetchone()
            total_amount = result[0] if result[0] is not None else 0
            self.log_message(f"数据库验证: {operation_desc}后 {table_name} 表总金额为 {total_amount}", level='info')
            return total_amount
        except Exception as e:
            self.log_message(f"数据库验证: 计算 {table_name} 表总金额时出错: {str(e)}", level='error')
            return None
    def ensure_folder(self, folder):
        os.makedirs(folder, exist_ok=True)
    def create_refund_list_table(self):
        with sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES) as conn:
            # 设置数据库连接为UTF-8编码
            conn.text_factory = str
            cur = conn.cursor()
            cur.execute('''CREATE TABLE IF NOT EXISTS REFUND_LIST (
                [id] INTEGER PRIMARY KEY AUTOINCREMENT,
                [Transaction Date] TEXT,
                [Settlement Date] TEXT,
                [Refund Date] TEXT,
                [Merchant Ref ID] TEXT,
                [Transaction ID] TEXT,
                [Channel] TEXT,
                [Order ID] TEXT,
                [Currency] TEXT,
                [Billing] REAL,
                [Actual] REAL,
                [Refund] REAL,
                [MDR] REAL,
                [GST] REAL,
                [Status] TEXT,
                [Refund Fee] REAL,
                [Quantity] INTEGER,
                [Reference1] TEXT,
                [Reference2] TEXT,
                [PROCESS] TEXT
            )''')
            cur.close()
            conn.commit()
    def standardize_date(self, date_str):
        if pd.isna(date_str) or not str(date_str).strip():
            return None
        s = str(date_str).strip()
        try:
            return pd.to_datetime(s).strftime("%Y-%m-%d")
        except Exception:
            return s
    def detect_sheet(self, file_path, platform):
        try:
            xl = pd.ExcelFile(file_path, engine='openpyxl')
            print(f"调试: 文件 {os.path.basename(file_path)} 包含的工作表: {xl.sheet_names}")

            # 优先查找 REFUND_LIST 工作表
            if 'REFUND_LIST' in xl.sheet_names:
                print(f"调试: 找到 REFUND_LIST 工作表")
                return 'REFUND_LIST'

            # 其次查找平台名称的工作表
            if platform in xl.sheet_names:
                print(f"调试: 找到平台工作表 {platform}")
                return platform

            # 查找包含 REFUND 的工作表
            for sheet_name in xl.sheet_names:
                if 'REFUND' in sheet_name.upper():
                    print(f"调试: 找到包含REFUND的工作表 {sheet_name}")
                    return sheet_name

            # 自动检测第一个sheet
            print(f"调试: 使用第一个工作表 {xl.sheet_names[0]}")
            return xl.sheet_names[0]
        except Exception as e:
            print(f"调试: 检测工作表时出错: {e}")
            return None
    def process_refund_file(self, platform, file_path):
        filename = os.path.basename(file_path)
        print(f"开始处理文件: {filename}")
        sheet_name = self.detect_sheet(file_path, platform)
        if not sheet_name:
            self.log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{filename} 无法检测sheet名")
            print(f"错误: {filename} 无法检测sheet名")
            return False
        try:
            print(f"正在读取Excel文件: {filename}, 工作表: {sheet_name}...")
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            print(f"成功读取文件，共有{len(df)}行数据需要处理")
            print(f"调试: 工作表 '{sheet_name}' 的列名: {df.columns.tolist()}")

            # 检查是否有数据
            if len(df) == 0:
                print(f"警告: 工作表 '{sheet_name}' 没有数据行")
                return False

            # 检查必需的列
            required_columns = ['Transaction Date', 'Order ID', 'Refund']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"错误: 工作表 '{sheet_name}' 缺少必需的列: {missing_columns}")
                print(f"可用的列: {df.columns.tolist()}")
                return False

            # 显示前几行数据用于调试
            print(f"调试: 前3行数据预览:")
            for i in range(min(3, len(df))):
                row_data = df.iloc[i]
                print(f"  行{i+1}: Order ID={row_data.get('Order ID', 'N/A')}, Refund={row_data.get('Refund', 'N/A')}, Transaction Date={row_data.get('Transaction Date', 'N/A')}")

        except Exception as e:
            self.log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{filename} 读取失败: {e}\n{traceback.format_exc()}")
            print(f"错误: {filename} 读取失败: {e}")
            return False
        df = df.rename(columns=lambda x: x.strip())
        df['PROCESS'] = ''
        
        # 处理Reference1和Reference2字段
        if 'Reference1' not in df.columns:
            # 如果没有Reference1字段，但有Reference字段，则拆分Reference字段
            if 'Reference' in df.columns:
                # 尝试拆分Reference字段为Reference1和Reference2
                df['Reference1'] = df['Reference']
                df['Reference2'] = ''
            else:
                # 如果没有任何Reference相关字段，添加空的Reference1和Reference2字段
                df['Reference1'] = ''
                df['Reference2'] = ''
        
        # 确保Reference2字段存在
        if 'Reference2' not in df.columns:
            df['Reference2'] = ''
            
        # 标准化日期字段
        for date_col in ['Transaction Date', 'Settlement Date', 'Refund Date']:
            if date_col in df.columns:
                df[date_col] = df[date_col].apply(self.standardize_date)
        
        # 确保所有必要的列都存在
        for col in REFUND_LIST_COLUMNS:
            if col not in df.columns:
                df[col] = ''
        
        # 只保留需要的列
        df = df[REFUND_LIST_COLUMNS]
        
        # 处理数据类型
        # 确保数值类型正确
        for col in ['Billing', 'Actual', 'Refund', 'MDR', 'GST', 'Refund Fee']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        # 确保日期类型正确
        for col in ['Transaction Date', 'Settlement Date', 'Refund Date']:
            if col in df.columns:
                df[col] = df[col].astype(str)
        
        # 验证数据库路径和连接
        print(f"数据库路径验证: {DB_PATH}")
        self.log_mgr.log_note(f"数据库路径验证: {DB_PATH}")

        if not os.path.exists(DB_PATH):
            error_msg = f"数据库文件不存在: {DB_PATH}"
            print(f"错误: {error_msg}")
            self.log_mgr.log_note(f"错误: {error_msg}")
            return False

        # 测试数据库连接
        try:
            test_conn = sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES)
            test_cursor = test_conn.cursor()
            test_cursor.execute("SELECT 1")
            test_result = test_cursor.fetchone()
            test_conn.close()
            print(f"数据库连接测试成功: {test_result[0] == 1}")
            self.log_mgr.log_note(f"数据库连接测试成功")
        except Exception as test_error:
            error_msg = f"数据库连接测试失败: {test_error}"
            print(f"错误: {error_msg}")
            self.log_mgr.log_note(f"错误: {error_msg}")
            return False

        # 将处理后的数据插入REFUND_LIST表
        with sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES) as conn:
            # 设置数据库连接为UTF-8编码
            conn.text_factory = str
            # 关闭外键约束，避免外键问题
            conn.execute("PRAGMA foreign_keys = OFF")
            db = conn.cursor()
            total_rows = len(df)
            print(f"开始处理{total_rows}行数据...")

            # 添加处理统计
            processed_count = 0
            success_count = 0
            error_count = 0

            for idx, row in df.iterrows():
                try:
                    # 显示行处理进度
                    row_progress = (idx + 1) / total_rows * 100
                    row_progress_bar = "[" + "#" * int(row_progress / 2) + "-" * (50 - int(row_progress / 2)) + "]"
                    print(f"处理第{idx+1}/{total_rows}行 {row_progress_bar} {row_progress:.1f}%")
                    
                    # 根据Merchant Ref ID判断平台类型
                    merchant_ref_id = str(row.get('Merchant Ref ID', '')).strip().lower()
                    if 'iot' in merchant_ref_id:
                        table_name = "IOT_Sales"
                    elif 'zeropowerstatio' in merchant_ref_id:
                        table_name = "ZERO_Sales"
                    else:
                        # 使用传入的平台参数作为默认值
                        table_name = f"{platform}_Sales"
                    print(f"使用数据表: {table_name}")

                    
                    refund_date = self.standardize_date(row.get('Transaction Date',''))
                    order_id = str(row.get('Order ID','')).strip()
                    transaction_id = str(row.get('Transaction ID','')).strip()  # 新增：获取Transaction ID
                    try:
                        refund_amt = float(row.get('Refund',0)) if str(row.get('Refund','')).strip() else 0
                    except Exception:
                        refund_amt = 0

                    candidates = []
                    found = False

                    print(f"调试: 处理记录 - Order ID: {order_id}, Transaction ID: {transaction_id}, Refund: {refund_amt}")

                    # 根据Order ID的长度判断是Equipment_ID还是Order No.（提前定义，避免变量未定义错误）
                    is_equipment_id_search = len(order_id) <= 9

                    # 优先级1: 使用Transaction ID匹配Transaction_Num
                    if transaction_id:
                        print(f"调试: 尝试Transaction ID匹配 - {transaction_id}")
                        db.execute(f"SELECT * FROM {table_name} WHERE Transaction_Num = ?", (transaction_id,))
                        transaction_candidates = db.fetchall()
                        print(f"调试: Transaction ID匹配结果 - {len(transaction_candidates)} 条")

                        if len(transaction_candidates) == 1:
                            # 找到唯一匹配，直接使用
                            candidates = transaction_candidates
                            found = True
                            print(f"调试: Transaction ID唯一匹配成功")
                        elif len(transaction_candidates) > 1:
                            # 找到多个匹配，记录候选但继续用其他条件筛选
                            candidates = transaction_candidates
                            print(f"调试: Transaction ID找到多个匹配，需要进一步筛选")
                        else:
                            print(f"调试: Transaction ID未找到匹配，将使用传统匹配方式")

                    # 如果Transaction ID没有找到唯一匹配，使用传统的日期时间匹配
                    if not found:
                        # 1. 精确日期时间匹配 (如果退款单有时间)
                        # refund_date_to_match 是从 Transaction Date 解析得到的 YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD
                        # Order_time 在数据库中是 YYYY-MM-DD HH:MM:SS
                        # 如果 refund_date_to_match 只有日期，那么这里的精确匹配会寻找当天 00:00:00 的记录，可能不符合预期
                        # 因此，精确匹配应该基于 refund_date (日期部分) 和原始 Transaction Date 的时间 (如果存在)

                        # 使用 refund_date (YYYY-MM-DD) 和 original_transaction_date_str (可能含时间) 进行查询
                        # original_transaction_date_str 来自于表格的 'Transaction Date' 列
                        transaction_date_for_query = str(row.get('Transaction Date','')).strip()
                        exact_query_condition = ""
                        query_params = []

                        if is_equipment_id_search:
                            # Equipment_ID 搜索时，如果 Transaction Date 有时间，则精确匹配时间，否则匹配当天
                            try:
                                  # 尝试解析包含时间的日期字符串
                                  try:
                                      # 先尝试带时间的格式
                                      dt_obj = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d %H:%M:%S")
                                  except ValueError:
                                      # 如果失败，尝试不带时间的格式
                                      dt_obj = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d")
                                      # 将日期转换为带时间的格式，时间设为00:00:00
                                      dt_obj = datetime.datetime.combine(dt_obj.date(), datetime.time(0, 0, 0))
                                  exact_query_condition = f"Equipment_ID = ? AND Order_time = ?"
                                  query_params = [order_id, transaction_date_for_query]
                                  self.log_message(f"信息: Equipment_ID {order_id} 使用 Transaction Date '{transaction_date_for_query}' (含时间) 进行精确匹配。", level='info')
                            except ValueError:
                                # Transaction Date 只有日期或格式不符，则匹配当天
                                exact_query_condition = f"Equipment_ID = ? AND substr(Order_time, 1, 10) = ?"
                                query_params = [order_id, refund_date] # refund_date 是 YYYY-MM-DD
                                self.log_message(f"信息: Equipment_ID {order_id} 使用 Transaction Date '{refund_date}' (仅日期) 进行当天匹配。", level='info')
                        else:
                             # Order No. 搜索时，也类似处理 Transaction Date
                             try:
                                 try:
                                     # 先尝试带时间的格式
                                     dt_obj = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d %H:%M:%S")
                                 except ValueError:
                                     # 如果失败，尝试不带时间的格式
                                     dt_obj = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d")
                                     # 将日期转换为带时间的格式，时间设为00:00:00
                                     dt_obj = datetime.datetime.combine(dt_obj.date(), datetime.time(0, 0, 0))
                                 exact_query_condition = f"Order_No = ? AND Order_time = ?"
                                 query_params = [order_id, transaction_date_for_query]
                                 self.log_message(f"信息: Order No. {order_id} 使用 Transaction Date '{transaction_date_for_query}' (含时间) 进行精确匹配。", level='info')
                             except ValueError:
                                exact_query_condition = f"Order_No = ? AND substr(Order_time, 1, 10) = ?"
                                query_params = [order_id, refund_date]
                                self.log_message(f"信息: Order No. {order_id} 使用 Transaction Date '{refund_date}' (仅日期) 进行当天匹配。", level='info')

                        if exact_query_condition:
                            print(f"调试: 执行查询 - {exact_query_condition}")
                            print(f"调试: 查询参数 - {query_params}")
                            db.execute(f"SELECT * FROM {table_name} WHERE {exact_query_condition}", query_params)
                            exact_date_candidates = db.fetchall()
                            print(f"调试: 查询结果数量 - {len(exact_date_candidates)}")
                            if exact_date_candidates:
                                for i, candidate in enumerate(exact_date_candidates):
                                    print(f"调试: 候选记录{i+1} - {candidate}")
                        else:
                            exact_date_candidates = [] # 如果没有形成有效查询条件，则无候选
                            print(f"调试: 没有形成有效查询条件")

                        # 过滤候选记录，匹配Order ID (这一步其实在上面的SQL查询中已经完成了，但保留以防万一或逻辑调整)
                        # candidates = [] # 重置 candidates
                        # for c_row_data in exact_date_candidates:
                        #     col_names_exact = [d[0] for d in db.description]
                        #     equipment_id_db = str(c_row_data[col_names_exact.index('Equipment_ID')]) if 'Equipment_ID' in col_names_exact else ''
                        #     order_number_db = str(c_row_data[col_names_exact.index('Order_No')]) if 'Order_No' in col_names_exact else ''

                        #     if is_equipment_id_search and order_id == equipment_id_db:
                        #         candidates.append(c_row_data)
                        #     elif not is_equipment_id_search and order_id == order_number_db:
                        #         candidates.append(c_row_data)
                        candidates = list(exact_date_candidates) # 直接使用查询结果

                        # 2. 如果是Equipment_ID搜索且精确日期未找到，则进行特殊处理
                        if is_equipment_id_search and not candidates and refund_date: # refund_date 确保日期有效
                            found_for_equipment_id_special_handling = False # 初始化found标记变量
                            # original_transaction_date_str = str(row.get('Transaction Date','')).strip() # 已在上面获取为 transaction_date_for_query
                            has_time_in_refund_doc = False
                            parsed_transaction_datetime = None
                            try:
                                parsed_transaction_datetime = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d %H:%M:%S")
                                has_time_in_refund_doc = True
                            except ValueError:
                                try:
                                    # 尝试解析只包含日期的字符串，确保 refund_date 是有效的日期部分
                                    parsed_transaction_datetime = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d")
                                    # 将日期转换为带时间的格式，时间设为00:00:00
                                    parsed_transaction_datetime = datetime.datetime.combine(parsed_transaction_datetime.date(), datetime.time(0, 0, 0))
                                    has_time_in_refund_doc = False # 明确只有日期
                                except ValueError:
                                    self.log_message(f"警告: Equipment_ID {order_id} 的退款单 Transaction Date '{transaction_date_for_query}' 格式无法解析。", level='warning')
                                    # 日期格式无效，后续逻辑不应继续依赖此日期进行扩展搜索
                                    # refund_date 此时可能来自 filename_date，如果 Transaction Date 无效

                            # 情况A: Transaction Date 只有日期或解析失败，按当天Equipment_ID和金额匹配
                            # refund_date 此时是 Transaction Date 的日期部分 (YYYY-MM-DD) 或从文件名提取的日期
                            if not has_time_in_refund_doc and refund_date: # 确保 refund_date 有效
                                self.log_message(f"信息: Equipment_ID {order_id} 在 Transaction Date '{refund_date}' 无精确时间，尝试匹配当天所有记录。", level='info')
                                # 使用 refund_date (YYYY-MM-DD) 进行查询
                                db.execute(f"SELECT * FROM {table_name} WHERE substr(Order_time,1,10) = ? AND Equipment_ID = ?", (refund_date, order_id))
                                day_candidates = db.fetchall()
                                if day_candidates:
                                    for c_row_data in day_candidates:
                                        col_names_day = [d[0] for d in db.description]
                                        current_amt_db = float(c_row_data[col_names_day.index('Order_price')])
                                        if refund_amt == 0 or abs(current_amt_db - refund_amt) < 0.001 or abs(current_amt_db + refund_amt) < 0.001:
                                            candidates.append(c_row_data)
                                            self.log_message(f"信息: Equipment_ID {order_id} 在日期 {refund_date} 找到金额匹配记录 (忽略时间)。退款金额: {refund_amt}, 数据库金额: {current_amt_db}", level='info')
                                            found_for_equipment_id_special_handling = True
                                            break
                                    if not found_for_equipment_id_special_handling and day_candidates:
                                        self.log_message(f"警告: Equipment_ID {order_id} 在日期 {refund_date} 未找到金额匹配的记录 (已忽略时间)。退款金额: {refund_amt}", level='warning')
                                        self.log_message(f"弹窗提示需求: Equipment_ID {order_id} 在日期 {refund_date} 未找到可操作记录 (金额不匹配)。", level='popup_request')
                                if not day_candidates:
                                    self.log_message(f"警告: Equipment_ID {order_id} 在日期 {refund_date} 未找到任何记录。", level='warning')
                                    self.log_message(f"弹窗提示需求: Equipment_ID {order_id} 在日期 {refund_date} 未找到任何记录。", level='popup_request')
                        
                            # 情况B: Transaction Date 有时间，且精确匹配失败 (candidates为空)，则在当天内扩大时间范围 (前后三小时)
                            # 只有在 refund_date 有效，Transaction Date 包含时间 (has_time_in_refund_doc is True)，
                            # 且上面没有通过忽略时间找到记录 (found_for_equipment_id_special_handling is False) 时才执行
                            if refund_date and has_time_in_refund_doc and parsed_transaction_datetime and not found_for_equipment_id_special_handling:
                                self.log_message(f"信息: Equipment_ID {order_id} 在 Transaction Date '{transaction_date_for_query}' (含时间) 精确匹配失败，尝试在当天扩展 ±3 小时搜索。", level='info')
                                try:
                                    # original_refund_datetime 就是 parsed_transaction_datetime
                                    center_hour = parsed_transaction_datetime.hour
                                    start_h = max(0, center_hour - 3)
                                    end_h = min(23, center_hour + 3)

                                    # refund_date 是 Transaction Date 的日期部分 (YYYY-MM-DD)
                                    query = f"SELECT * FROM {table_name} WHERE Equipment_ID = ? AND substr(Order_time, 1, 10) = ? AND CAST(strftime('%H', Order_time) AS INTEGER) BETWEEN ? AND ?"
                                    db.execute(query, (order_id, refund_date, str(start_h), str(end_h)))
                                    hourly_candidates = db.fetchall()
                                    for c_row_data in hourly_candidates:
                                        # 检查是否与已有的忽略时间匹配的记录重复，理论上不会，因为执行条件是 found_for_equipment_id_special_handling is False
                                        candidates.append(c_row_data)
                                    if hourly_candidates: # 注意这里用 hourly_candidates 判断是否有新找到的
                                        self.log_message(f"信息: Equipment_ID {order_id} 在日期 {refund_date} 的 {start_h}-{end_h}点时间范围内找到 {len(hourly_candidates)} 条记录。", level='info')
                                    else:
                                        self.log_message(f"警告: Equipment_ID {order_id} 在日期 {refund_date} 的 {start_h}-{end_h}点时间范围内未找到记录。", level='warning')
                                except Exception as e_hour_ext:
                                    self.log_message(f"错误: Equipment_ID {order_id} 在进行小时扩展搜索时发生错误: {e_hour_ext}", level='error')
                                    self.error_occurred = True

                    # 3. 处理找到的候选记录
                    if candidates:
                        if not is_equipment_id_search: # Order No. 搜索逻辑
                            if len(candidates) == 1:
                                # 只有一个Order No.匹配，直接处理
                                selected_candidate = candidates[0]
                                print(f"Order No. 精确匹配到唯一记录，忽略时间进行处理: {order_id}")
                            else:
                                # 多个Order No.匹配，选择时间最接近或金额最准确的
                                print(f"Order No. 精确匹配到多条记录 ({len(candidates)}条)，选择最优记录: {order_id}")
                                # 优先选择金额完全匹配的
                                best_candidate = None
                                min_amount_diff = float('inf')
                                
                                for cand_row_data in candidates:
                                    cand_col_names = [d[0] for d in db.description]
                                    cand_order_price = float(cand_row_data[cand_col_names.index('Order_price')]) if 'Order_price' in cand_col_names else 0
                                    if cand_order_price == refund_amt: # 如果退款金额等于订单金额，优先选择这条（全额退款）
                                        best_candidate = cand_row_data
                                        print(f"  找到金额完全匹配的记录 (退款金额: {refund_amt}, 订单金额: {cand_order_price})")
                                        break 
                                    amount_diff = abs(cand_order_price - refund_amt) # 否则，找差额最小的
                                    if amount_diff < min_amount_diff:
                                        min_amount_diff = amount_diff
                                        best_candidate = cand_row_data
                                
                                if best_candidate:
                                    selected_candidate = best_candidate
                                    cand_col_names_sc = [d[0] for d in db.description]
                                    sc_order_price = float(selected_candidate[cand_col_names_sc.index('Order_price')]) if 'Order_price' in cand_col_names_sc else 0
                                    sc_order_time = selected_candidate[cand_col_names_sc.index('Order_time')] if 'Order_time' in cand_col_names_sc else ''
                                    print(f"  选择的最优记录: Order_price={sc_order_price}, Order_time={sc_order_time}")
                                else: # 理论上不会到这里，因为candidates不为空
                                    selected_candidate = candidates[0] 
                                    print("  未找到金额完全匹配或差额最小的记录，默认选择第一条")
                        else: # Equipment_ID 搜索逻辑 (日期已扩大或精确匹配)
                            # Equipment_ID 通常期望唯一或少数几个，这里简单取第一个
                            # 如果有更复杂的选择逻辑，可以在这里添加
                            selected_candidate = candidates[0]
                            print(f"Equipment_ID 搜索到 {len(candidates)} 条记录, 选择第一条进行处理: {order_id}") 

                        if selected_candidate:
                            col_names = [d[0] for d in db.description]
                            # 从 selected_candidate 获取数据，而不是 c (c 在之前的循环中)
                            equipment_id_db_sel = str(selected_candidate[col_names.index('Equipment_ID')]) if 'Equipment_ID' in col_names else ''
                            order_number_db_sel = str(selected_candidate[col_names.index('Order_No')]) if 'Order_No' in col_names else ''
                            
                            # 双重确认，虽然前面已经筛选过，但这里可以再加一层保险
                            if (is_equipment_id_search and order_id == equipment_id_db_sel) or \
                               (not is_equipment_id_search and order_id == order_number_db_sel):
                                found = True
                                orig_amt = float(selected_candidate[col_names.index('Order_price')]) if 'Order_price' in col_names else 0
                                new_amt = orig_amt - refund_amt
                                
                                record_data = {}
                                for i, col_name_sel in enumerate(col_names):
                                    record_data[col_name_sel] = selected_candidate[i]
                                
                                if new_amt <= 0:
                                    # 删除记录前先获取关键信息用于后续验证
                                    order_no_for_verify = str(selected_candidate[col_names.index('Order_No')]) if 'Order_No' in col_names else ''
                                    equipment_id_for_verify = str(selected_candidate[col_names.index('Equipment_ID')]) if 'Equipment_ID' in col_names else ''
                                    order_price_for_verify = float(selected_candidate[col_names.index('Order_price')]) if 'Order_price' in col_names else 0
                                    rowid_for_verify = selected_candidate[0]
                                    
                                    # 删除前检查数据库总金额
                                    before_amount = self.check_database_amount(conn, table_name, "删除前")
                                    
                                    # 执行删除操作
                                    delete_result = db.execute(f"DELETE FROM {table_name} WHERE rowid=?", (rowid_for_verify,))
                                    rows_affected = delete_result.rowcount
                                    process_status = '已删除'

                                    # 立即提交删除操作
                                    conn.commit()

                                    # 验证删除操作是否影响了行数
                                    self.log_message(f"删除操作影响行数: {rows_affected}", level='info')
                                    if rows_affected == 0:
                                        self.log_message(f"警告: 删除操作没有影响任何行，可能删除失败", level='warning')
                                    
                                    # 删除后检查数据库总金额
                                    after_amount = self.check_database_amount(conn, table_name, "删除后")
                                    
                                    # 验证金额变化
                                    if before_amount is not None and after_amount is not None:
                                        expected_change = order_price_for_verify
                                        actual_change = before_amount - after_amount
                                        self.log_message(f"数据库验证: 预期金额变化 {expected_change}, 实际金额变化 {actual_change}", level='info')
                                        
                                        if abs(actual_change - expected_change) < 0.001:
                                            self.log_message(f"数据库验证: 金额变化符合预期，删除操作成功影响数据库", level='info')
                                        else:
                                            self.log_message(f"数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行", level='warning')
                                    
                                    # 验证删除操作结果 - 首先通过rowid验证
                                    db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (rowid_for_verify,))
                                    exists_by_rowid = db.fetchone()[0]
                                    
                                    # 通过Order_No或Equipment_ID进行二次验证
                                    exists_by_other_keys = False
                                    verification_queries = []
                                    
                                    if order_no_for_verify:
                                        db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE Order_No=? AND Order_price=?", 
                                                  (order_no_for_verify, order_price_for_verify))
                                        exists_by_order_no = db.fetchone()[0]
                                        verification_queries.append(f"Order_No={order_no_for_verify}")
                                        if exists_by_order_no > 0:
                                            exists_by_other_keys = True
                                    
                                    if equipment_id_for_verify and not exists_by_other_keys:
                                        db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE Equipment_ID=? AND Order_price=?", 
                                                  (equipment_id_for_verify, order_price_for_verify))
                                        exists_by_equipment_id = db.fetchone()[0]
                                        verification_queries.append(f"Equipment_ID={equipment_id_for_verify}")
                                        if exists_by_equipment_id > 0:
                                            exists_by_other_keys = True
                                    
                                    # 综合验证结果
                                    if exists_by_rowid == 0 and not exists_by_other_keys:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 删除成功验证: 记录已不存在 (验证条件: rowid={rowid_for_verify} 和 {', '.join(verification_queries)})")
                                    else:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 删除验证失败: 记录仍存在 (rowid验证:{exists_by_rowid>0}, 其他键验证:{exists_by_other_keys})")
                                        
                                        # 如果通过rowid删除失败，尝试通过Order_No或Equipment_ID删除
                                        delete_retry_success = False
                                        
                                        if order_no_for_verify:
                                            db.execute(f"DELETE FROM {table_name} WHERE Order_No=? AND Order_price=?", 
                                                      (order_no_for_verify, order_price_for_verify))
                                            conn.commit()
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 尝试通过Order_No={order_no_for_verify}删除记录")
                                            delete_retry_success = True
                                        
                                        if equipment_id_for_verify and not delete_retry_success:
                                            db.execute(f"DELETE FROM {table_name} WHERE Equipment_ID=? AND Order_price=?", 
                                                      (equipment_id_for_verify, order_price_for_verify))
                                            conn.commit()
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 尝试通过Equipment_ID={equipment_id_for_verify}删除记录")
                                            delete_retry_success = True
                                        
                                        # 最终验证
                                        final_verification_passed = True
                                        
                                        if order_no_for_verify:
                                            db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE Order_No=? AND Order_price=?", 
                                                      (order_no_for_verify, order_price_for_verify))
                                            if db.fetchone()[0] > 0:
                                                final_verification_passed = False
                                        
                                        if equipment_id_for_verify and final_verification_passed:
                                            db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE Equipment_ID=? AND Order_price=?", 
                                                      (equipment_id_for_verify, order_price_for_verify))
                                            if db.fetchone()[0] > 0:
                                                final_verification_passed = False
                                        
                                        if final_verification_passed:
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终删除验证通过: 记录已成功删除")
                                        else:
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 严重警告: 多次尝试后记录仍未被删除！")
                                else:
                                    # 更新前检查数据库总金额
                                    before_amount = self.check_database_amount(conn, table_name, f"更新前 - {filename} 第{idx+1}行")
                                    
                                    # 保存原始金额用于验证
                                    db.execute(f"SELECT Order_price FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                    original_price_result = db.fetchone()
                                    original_price = float(original_price_result[0]) if original_price_result else 0
                                    expected_change = original_price - new_amt
                                    
                                    # 更新金额
                                    update_result = db.execute(f"UPDATE {table_name} SET Order_price=? WHERE rowid=?", (new_amt, selected_candidate[0]))
                                    rows_affected = update_result.rowcount
                                    process_status = '已退款'

                                    # 立即提交以确保更新生效
                                    conn.commit()

                                    # 验证更新操作是否影响了行数
                                    self.log_message(f"更新操作影响行数: {rows_affected}", level='info')
                                    if rows_affected == 0:
                                        self.log_message(f"警告: 更新操作没有影响任何行，可能更新失败", level='warning')
                                    
                                    # 更新后检查数据库总金额
                                    after_amount = self.check_database_amount(conn, table_name, f"更新后 - {filename} 第{idx+1}行")
                                    
                                    # 验证金额变化
                                    if before_amount is not None and after_amount is not None:
                                        actual_change = before_amount - after_amount
                                        self.log_message(f"数据库验证: 预期金额变化 {expected_change}, 实际金额变化 {actual_change}", level='info')
                                        
                                        if abs(actual_change - expected_change) < 0.001:
                                            self.log_message(f"数据库验证: 金额变化符合预期，更新操作成功影响数据库", level='info')
                                        else:
                                            self.log_message(f"数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行", level='warning')
                                    
                                    # 验证更新操作结果
                                    db.execute(f"SELECT Order_price FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                    current_price = db.fetchone()
                                    if current_price and abs(float(current_price[0]) - new_amt) < 0.01:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 更新成功验证: 新金额={current_price[0]}")
                                    else:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 更新验证失败: 期望={new_amt}, 实际={current_price[0] if current_price else 'NULL'}")
                                        # 如果更新失败，尝试再次更新
                                        db.execute(f"UPDATE {table_name} SET Order_price=? WHERE rowid=?", (new_amt, selected_candidate[0]))
                                        conn.commit()
                                
                                # 立即提交事务
                                try:
                                    conn.commit()
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 事务已提交，状态: {process_status}")
                                    
                                    # 增强验证删除操作是否成功的代码
                                    if process_status == '已删除':
                                        # 再次检查记录是否真的被删除
                                        db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                        still_exists = db.fetchone()[0]
                                        if still_exists > 0:
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 警告：记录标记为已删除，但在数据库中仍然存在！尝试再次删除...")
                                            # 再次尝试删除
                                            db.execute(f"DELETE FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                            conn.commit()
                                            
                                            # 再次验证
                                            db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                            still_exists_after_retry = db.fetchone()[0]
                                            if still_exists_after_retry > 0:
                                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 严重警告：即使重试后，记录仍然存在于数据库中！")
                                                # 最后一次尝试删除，使用不同的方式
                                                try:
                                                    # 使用更强制的方式删除
                                                    col_names = [d[0] for d in db.description]
                                                    order_no_idx = col_names.index('Order_No') if 'Order_No' in col_names else -1
                                                    equipment_id_idx = col_names.index('Equipment_ID') if 'Equipment_ID' in col_names else -1
                                                    
                                                    if order_no_idx >= 0 and selected_candidate[order_no_idx]:
                                                        db.execute(f"DELETE FROM {table_name} WHERE Order_No=?", (selected_candidate[order_no_idx],))
                                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终尝试：通过Order_No删除记录")
                                                    elif equipment_id_idx >= 0 and selected_candidate[equipment_id_idx]:
                                                        db.execute(f"DELETE FROM {table_name} WHERE Equipment_ID=?", (selected_candidate[equipment_id_idx],))
                                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终尝试：通过Equipment_ID删除记录")
                                                    else:
                                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 无法找到Order_No或Equipment_ID进行最终删除尝试")
                                                    
                                                    conn.commit()
                                                    
                                                    # 最终验证
                                                    db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                                    final_check = db.fetchone()[0]
                                                    if final_check > 0:
                                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终警告：所有尝试后，记录仍然存在于数据库中！")
                                                    else:
                                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终删除成功：记录已从数据库中移除")
                                                except Exception as final_error:
                                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终删除尝试失败: {final_error}")
                                            else:
                                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 重试删除成功：记录已从数据库中移除")
                                        else:
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 验证成功：记录已从数据库中移除")
                                            
                                except Exception as commit_error:
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 事务提交失败: {commit_error}")
                                    print(f"警告: 事务提交失败: {commit_error}")
                                    # 尝试再次提交
                                    try:
                                        conn.commit()
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 事务重试提交成功")
                                        
                                        # 在重试提交成功后也验证删除操作
                                        if process_status == '已删除':
                                            db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                            still_exists = db.fetchone()[0]
                                            if still_exists > 0:
                                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 警告：重试提交后，记录标记为已删除，但在数据库中仍然存在！")
                                                # 再次尝试删除
                                                db.execute(f"DELETE FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                                conn.commit()
                                                
                                                # 最终验证
                                                db.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (selected_candidate[0],))
                                                final_check = db.fetchone()[0]
                                                if final_check > 0:
                                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 严重警告：多次尝试后，记录仍然存在于数据库中！")
                                                else:
                                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终删除成功：记录已从数据库中移除")
                                            else:
                                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 重试提交后验证成功：记录已从数据库中移除")
                                    except Exception as retry_error:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 事务重试提交失败: {retry_error}")
                                        print(f"严重警告: 事务重试提交失败: {retry_error}")
                                
                                df.at[idx, 'PROCESS'] = process_status
                                
                                log_detail = f"{filename} 第{idx+1}行 退款成功，状态:{process_status}\n"
                                log_detail += f"退款金额: {refund_amt}\n"
                                log_detail += f"原始金额: {orig_amt}\n"
                                log_detail += f"新金额: {new_amt}\n"
                                log_detail += "处理的数据行:\n"
                                for k, v_sel in record_data.items():
                                    log_detail += f"  {k}: {v_sel}\n"
                                
                                self.log_mgr.log_success(log_detail)
                            else:
                                # 这个分支理论上不应该进入，因为 selected_candidate 已经是筛选过的
                                print(f"警告: selected_candidate ({order_id}) 与原始查询条件不符，跳过处理。")
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 警告: selected_candidate ({order_id}) 与原始查询条件不符，跳过处理。")

                    if not found:
                        df.at[idx, 'PROCESS'] = '未找到'
                        
                        # 记录详细的未匹配信息
                        log_detail = f"{filename} 第{idx+1}行 未找到匹配数据\n"
                        log_detail += "查询条件:\n"
                        log_detail += f"  Transaction Date: {refund_date}\n"
                        log_detail += f"  Order ID: {order_id}\n"
                        log_detail += f"  Refund Amount: {refund_amt}\n"
                        log_detail += "原始行数据:\n"
                        for k, v in row.items():
                            log_detail += f"  {k}: {v}\n"
                        
                        # 确保在数据库中也标记为未找到
                        # 这里使用INSERT而不是UPDATE，因为记录可能还不存在于REFUND_LIST表中
                        # 在后面的代码中会插入完整记录
                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 标记为未找到")
                        self.log_mgr.log_fail(row, log_detail)
                        
                        # 确保未找到的记录也被正确提交到数据库
                        try:
                            conn.commit()
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 '未找到'状态已更新到数据库")
                        except Exception as commit_error:
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 '未找到'状态更新失败: {commit_error}")
                            print(f"警告: '未找到'状态更新失败: {commit_error}")
                    
                    # 使用DataFrame中更新后的数据，而不是原始的row数据
                    insert_row = []
                    for col in REFUND_LIST_COLUMNS:
                        if col in df.columns:
                            # 确保数值类型正确
                            if col in ['Billing', 'Actual', 'Refund', 'MDR', 'GST', 'Refund Fee']:
                                try:
                                    val = float(df.at[idx, col]) if pd.notna(df.at[idx, col]) and str(df.at[idx, col]).strip() else 0.0
                                except (ValueError, TypeError):
                                    val = 0.0
                            # 确保日期类型正确
                            elif col in ['Transaction Date', 'Settlement Date', 'Refund Date']:
                                val = str(df.at[idx, col]) if pd.notna(df.at[idx, col]) else ''
                            # 其他字符串类型
                            else:
                                val = str(df.at[idx, col]) if pd.notna(df.at[idx, col]) else ''
                        else:
                            # 列不存在，使用默认值
                            if col in ['Billing', 'Actual', 'Refund', 'MDR', 'GST', 'Refund Fee']:
                                val = 0.0
                            else:
                                val = ''
                        insert_row.append(val)
                    
                    # 插入记录到REFUND_LIST表
                    db.execute(f"INSERT INTO REFUND_LIST ({','.join(['['+col+']' for col in REFUND_LIST_COLUMNS])}) VALUES ({','.join(['?']*len(REFUND_LIST_COLUMNS))})", insert_row)
                    
                    # 立即提交事务，确保数据写入
                    try:
                        conn.commit()
                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 REFUND_LIST插入记录已提交")
                        
                        # 增强验证REFUND_LIST记录是否成功插入的机制
                        verify_query = "SELECT COUNT(*) FROM REFUND_LIST WHERE [Transaction Date]=? AND [Order ID]=? AND [Refund]=?"
                        db.execute(verify_query, (insert_row[0], insert_row[6], insert_row[10]))
                        record_exists = db.fetchone()[0]
                        
                        if record_exists > 0:
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 验证成功：REFUND_LIST记录已成功插入数据库")
                        else:
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 警告：REFUND_LIST记录可能未成功插入，尝试重新插入...")
                            # 重新尝试插入前先检查是否已存在类似记录
                            similar_query = "SELECT COUNT(*) FROM REFUND_LIST WHERE [Order ID]=? AND [Refund]=?"
                            db.execute(similar_query, (insert_row[6], insert_row[10]))
                            similar_exists = db.fetchone()[0]
                            
                            if similar_exists > 0:
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 发现类似记录：相同Order ID和Refund金额的记录已存在，可能是日期格式问题")
                                # 尝试更新而不是插入
                                try:
                                    update_query = f"UPDATE REFUND_LIST SET [Transaction Date]=? WHERE [Order ID]=? AND [Refund]=?"
                                    db.execute(update_query, (insert_row[0], insert_row[6], insert_row[10]))
                                    conn.commit()
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 已更新现有记录的Transaction Date")
                                except Exception as update_error:
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 更新现有记录失败: {update_error}")
                            else:
                                # 重新尝试插入
                                db.execute(f"INSERT INTO REFUND_LIST ({','.join(['['+col+']' for col in REFUND_LIST_COLUMNS])}) VALUES ({','.join(['?']*len(REFUND_LIST_COLUMNS))})", insert_row)
                                conn.commit()
                            
                            # 再次验证
                            db.execute(verify_query, (insert_row[0], insert_row[6], insert_row[10]))
                            recheck = db.fetchone()[0]
                            if recheck > 0:
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 重试成功：REFUND_LIST记录已成功插入数据库")
                            else:
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 严重警告：即使重试后，REFUND_LIST记录仍未成功插入数据库！")
                                # 尝试使用不同的方式插入
                                try:
                                    # 使用事务隔离级别
                                    conn.isolation_level = None  # 自动提交模式
                                    db.execute(f"INSERT INTO REFUND_LIST ({','.join(['['+col+']' for col in REFUND_LIST_COLUMNS])}) VALUES ({','.join(['?']*len(REFUND_LIST_COLUMNS))})", insert_row)
                                    conn.isolation_level = ''  # 恢复默认隔离级别
                                    
                                    # 最终验证
                                    db.execute(verify_query, (insert_row[0], insert_row[6], insert_row[10]))
                                    final_check = db.fetchone()[0]
                                    if final_check > 0:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终插入成功：使用自动提交模式成功插入REFUND_LIST记录")
                                    else:
                                        # 记录详细的插入失败信息，帮助诊断问题
                                        error_detail = "插入失败详情:\n"
                                        error_detail += f"表名: REFUND_LIST\n"
                                        error_detail += f"列数: {len(REFUND_LIST_COLUMNS)}\n"
                                        error_detail += f"值数: {len(insert_row)}\n"
                                        for i, (col, val) in enumerate(zip(REFUND_LIST_COLUMNS, insert_row)):
                                            error_detail += f"  {i}. {col}: {val} (类型: {type(val).__name__})\n"
                                        self.log_mgr.log_note(error_detail)
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终警告：所有尝试后，REFUND_LIST记录仍未成功插入数据库！")
                                except Exception as final_error:
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终插入尝试失败: {final_error}")
                    except Exception as commit_error:
                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 REFUND_LIST插入记录提交失败: {commit_error}")
                        print(f"警告: REFUND_LIST插入记录提交失败: {commit_error}")
                        # 尝试再次提交
                        try:
                            conn.commit()
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 REFUND_LIST插入记录重试提交成功")
                            
                            # 验证重试提交后的记录
                            verify_query = "SELECT COUNT(*) FROM REFUND_LIST WHERE [Transaction Date]=? AND [Order ID]=? AND [Refund]=?"
                            db.execute(verify_query, (insert_row[0], insert_row[6], insert_row[10]))
                            record_exists = db.fetchone()[0]
                            
                            if record_exists > 0:
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 重试验证成功：REFUND_LIST记录已成功插入数据库")
                            else:
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行 重试验证失败：REFUND_LIST记录仍未成功插入，尝试最后一次插入...")
                                # 最后一次尝试插入
                                db.execute(f"INSERT INTO REFUND_LIST ({','.join(['['+col+']' for col in REFUND_LIST_COLUMNS])}) VALUES ({','.join(['?']*len(REFUND_LIST_COLUMNS))})", insert_row)
                                conn.commit()
                                
                                # 最终验证
                                db.execute(verify_query, (insert_row[0], insert_row[6], insert_row[10]))
                                final_check = db.fetchone()[0]
                                if final_check > 0:
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终验证成功：REFUND_LIST记录已成功插入数据库")
                                else:
                                    self.log_mgr.log_note(f"{filename} 第{idx+1}行 最终验证失败：所有尝试后，REFUND_LIST记录仍未成功插入数据库！")
                        except Exception as retry_error:
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行 REFUND_LIST插入记录重试提交失败: {retry_error}")
                            print(f"严重警告: REFUND_LIST插入记录重试提交失败: {retry_error}")
                
                except Exception as e:
                    df.at[idx, 'PROCESS'] = '失败'
                    error_msg = f"{filename} 第{idx+1}行 处理异常: {e}\n{traceback.format_exc()}"
                    self.log_mgr.log_fail(row, error_msg)
                    self.log_mgr.log_note(error_msg)
                    print(f"错误: {error_msg}")
                    error_count += 1

                    # 尝试回滚事务
                    try:
                        conn.rollback()
                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 事务已回滚")
                    except Exception as rollback_error:
                        self.log_mgr.log_note(f"{filename} 第{idx+1}行 事务回滚失败: {rollback_error}")
                        print(f"警告: 事务回滚失败: {rollback_error}")

                # 更新处理统计
                processed_count += 1
                if df.at[idx, 'PROCESS'] in ['已退款', '已删除']:
                    success_count += 1
            
            conn.commit()
            db.close()

        # 显示实时处理统计
        print(f"\n实时处理统计:")
        print(f"已处理: {processed_count}/{total_rows} 行")
        print(f"成功: {success_count}, 错误: {error_count}")

        # 统计处理结果
        success_count = len(df[df['PROCESS'] == '已退款']) + len(df[df['PROCESS'] == '已删除'])
        not_found_count = len(df[df['PROCESS'] == '未找到'])
        failed_count = len(df[df['PROCESS'] == '失败'])
        empty_count = len(df[df['PROCESS'] == ''])
        
        print("\n处理结果统计:")
        print(f"总记录数: {len(df)}")
        print(f"成功处理: {success_count} ({success_count/len(df)*100:.1f}%)")
        print(f"  - 已退款: {len(df[df['PROCESS'] == '已退款'])}")
        print(f"  - 已删除: {len(df[df['PROCESS'] == '已删除'])}")
        print(f"未找到匹配记录: {not_found_count} ({not_found_count/len(df)*100:.1f}%)")
        print(f"处理失败: {failed_count} ({failed_count/len(df)*100:.1f}%)")
        print(f"未处理: {empty_count} ({empty_count/len(df)*100:.1f}%)")
        print("\n文件处理完成!\n" + "-"*50)
        
        return True
    
    def move_file(self, src, dst):
        return move_file(src, dst)
    
    def clean_old_logs(self):
        self.log_mgr.clear_logs()
    
    def archive_logs_and_db(self):
        self.log_mgr.archive_logs()
        self.backup_mgr.archive_db()
    
    def compress_and_delete_old_refund_list(self):
        # 按月归档REFUND_LIST表数据
        month_str = input("请输入要归档的月份(格式YYYY-MM): ").strip()
        with sqlite3.connect(DB_PATH) as conn:
            df = pd.read_sql(f"SELECT * FROM REFUND_LIST WHERE substr([Transaction Date],1,7)=?", conn, params=(month_str,))
            if not df.empty:
                export_path = os.path.join(BACKUP_DIR, f"REFUND_LIST_{month_str}.xlsx")
                df.to_excel(export_path, index=False)  # 确保不使用 encoding 参数
                conn.execute(f"DELETE FROM REFUND_LIST WHERE substr([Transaction Date],1,7)=?", (month_str,))
                conn.commit()
                self.log_mgr.log_note(f"已归档并删除{month_str}的REFUND_LIST数据")

def main():
    global DB_PATH, BACKUP_DIR, SUCCESS_LOG, FAIL_LOG, NOTE_LOG
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="退款处理脚本")
    parser.add_argument("--file", help="要处理的退款文件路径")
    parser.add_argument("--platform", help="平台类型 (IOT 或 ZERO)")
    parser.add_argument("--mode", help="操作模式 (1-退款处理, 2-恢复备份, 3-清理日志, 4-归档, 5-按月归档REFUND_LIST)")
    parser.add_argument("--db_path", help="数据库文件路径")
    args = parser.parse_args()

    # 如果命令行提供了数据库路径，更新全局变量
    if args.db_path:
        DB_PATH = get_db_path_from_config(args.db_path)
        print(f"使用命令行指定的数据库路径: {DB_PATH}")
    else:
        print(f"使用默认数据库路径: {DB_PATH}")

    # 确保数据库目录存在
    db_directory = os.path.dirname(DB_PATH)
    if db_directory and not os.path.exists(db_directory):
        os.makedirs(db_directory)
        print(f"创建数据库目录: {db_directory}")

    # 在 DB_PATH 确定后定义 BACKUP_DIR 和日志文件路径
    BACKUP_DIR = os.path.join(os.path.dirname(DB_PATH), "Refunding_backup") # 将备份目录放在数据库同级的 Refunding_backup
    SUCCESS_LOG = os.path.join(BACKUP_DIR, "refund_success.log")
    FAIL_LOG = os.path.join(BACKUP_DIR, "refund_fail.csv")
    NOTE_LOG = os.path.join(BACKUP_DIR, "refund_note.log")
    
    # 确保目录存在
    os.makedirs(BACKUP_DIR, exist_ok=True)
    for plat_dir in REFUND_DIRS.values():
        os.makedirs(plat_dir, exist_ok=True)

    log_mgr = LogManager(SUCCESS_LOG, FAIL_LOG, NOTE_LOG)
    backup_mgr = BackupManager(BACKUP_DIR)
    processor = RefundProcessor(log_mgr, backup_mgr)

    # 如果指定了文件和平台参数，则直接处理
    if args.file and args.platform:
        print(f"使用命令行参数处理文件: {args.file}, 平台: {args.platform}")
        processor.create_refund_list_table()
        # 备份功能已移除：GUI应用已提供完整的数据库备份
        
        try:
            print(f"开始处理文件: {os.path.basename(args.file)}")
            ok = processor.process_refund_file(args.platform, args.file)
            if ok:
                print(f"文件 {os.path.basename(args.file)} 处理成功")
            else:
                print(f"文件 {os.path.basename(args.file)} 处理失败")
        except Exception as e:
            print(f"处理文件时出错: {e}")
            log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, 
                           f"{os.path.basename(args.file)} 处理异常: {e}\n{traceback.format_exc()}")
        return
    
    # 如果指定了模式参数
    if args.mode:
        mode = args.mode
    else:
        try:
            print("请选择操作模式：1-退款处理  2-[已禁用]恢复Excel备份  3-清理日志  4-归档日志和数据库  5-按月归档REFUND_LIST")
            print("注意：模式2已禁用，因为Excel备份功能已移除。请使用GUI应用的数据库恢复功能。")
            mode = input("输入1/3/4/5：").strip()
        except Exception as e:
            print(f"输入异常: {e}")
            return
    
    try:
        if mode == '2':
            print("错误：Excel备份恢复功能已禁用。")
            print("原因：Excel备份功能已移除，GUI应用提供更可靠的SQLite数据库备份。")
            print("请使用GUI应用的'恢复备份'功能来恢复数据库。")
            return
        elif mode == '3':
            processor.clean_old_logs()
            print("日志已清理")
            return
        elif mode == '4':
            processor.archive_logs_and_db()
            print("日志和数据库已归档")
            return
        elif mode == '5':
            processor.compress_and_delete_old_refund_list()
            print("REFUND_LIST已按月归档并删除")
            return
        elif mode != '1':
            print("无效输入")
            return
        
        processor.create_refund_list_table()
        for plat, folder in REFUND_DIRS.items():
            # 备份功能已移除：GUI应用已提供完整的数据库备份
            processor.ensure_folder(os.path.join(folder, "已处理"))
            processor.ensure_folder(os.path.join(folder, "需人工检查"))
            
            if not os.path.exists(folder):
                print(f"警告：{folder} 目录不存在，跳过处理")
                continue
                
            files = [f for f in os.listdir(folder) if f.lower().endswith(('.xls','.xlsx'))]
            total = len(files)  # 添加总文件数变量
            
            if total == 0:
                print(f"{plat}：没有找到需要处理的文件")
                continue
                
            for idx, fname in enumerate(files):
                fpath = os.path.join(folder, fname)
                try:
                    # 显示进度条
                    progress = (idx + 1) / total * 100
                    progress_bar = "[" + "#" * int(progress / 2) + "-" * (50 - int(progress / 2)) + "]"
                    print(f"{plat}：正在处理第{idx+1}/{total}个文件：{fname} {progress_bar} {progress:.1f}%")
                    
                    ok = processor.process_refund_file(plat, fpath)
                    target_dir = "已处理" if ok else "需人工检查"
                    dst = os.path.join(folder, target_dir, fname)
                    move_success = processor.move_file(fpath, dst)
                    if not move_success:
                        # 即使移动失败，也标记为需要人工检查，但继续处理其他文件
                        log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, 
                                        f"{fname} 文件移动失败，可能被其他程序占用，请手动移动到{target_dir}文件夹")
                except Exception as e:
                    log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{fname} 主流程异常: {e}\n{traceback.format_exc()}")
                    try:
                        dst = os.path.join(folder, "需人工检查", fname)
                        processor.move_file(fpath, dst)
                    except Exception:
                        pass

        # 备份功能已移除：GUI应用已提供完整的数据库备份

        # 显示总体处理结果
        print("\n" + "="*50)
        print("退款处理总结")
        print("="*50)

        # 读取成功和失败日志，统计处理结果
        success_count = 0
        fail_count = 0

        if os.path.exists(SUCCESS_LOG):
            with open(SUCCESS_LOG, 'r', encoding='utf-8') as f:
                success_count = len(f.readlines())

        if os.path.exists(FAIL_LOG):
            with open(FAIL_LOG, 'r', encoding='utf-8') as f:
                # 跳过标题行
                lines = f.readlines()
                fail_count = len(lines) - 1 if len(lines) > 0 else 0

        total_count = success_count + fail_count

        print(f"总处理记录数: {total_count}")
        if total_count > 0:
            print(f"成功处理记录: {success_count} ({success_count/total_count*100:.1f}%)")
            print(f"失败记录数: {fail_count} ({fail_count/total_count*100:.1f}%)")

        # 最终数据库验证 - 检查所有平台的数据库总金额
        print("\n" + "="*30)
        print("数据库最终验证")
        print("="*30)

        try:
            with sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES) as conn:
                db = conn.cursor()

                # 检查各平台表的总金额
                for platform in ['IOT', 'ZERO', 'APP']:
                    table_name = f"{platform}_Sales"
                    try:
                        db.execute(f"SELECT COUNT(*), SUM(Order_price) FROM {table_name}")
                        result = db.fetchone()
                        record_count = result[0] if result[0] is not None else 0
                        total_amount = result[1] if result[1] is not None else 0
                        print(f"{platform}_Sales 表: {record_count} 条记录, 总金额: RM{total_amount:.2f}")
                    except Exception as e:
                        print(f"{platform}_Sales 表: 查询失败 - {str(e)}")

                # 检查REFUND_LIST表
                try:
                    db.execute("SELECT COUNT(*), SUM([Refund]) FROM REFUND_LIST")
                    result = db.fetchone()
                    refund_count = result[0] if result[0] is not None else 0
                    total_refund = result[1] if result[1] is not None else 0
                    print(f"REFUND_LIST 表: {refund_count} 条退款记录, 总退款金额: RM{total_refund:.2f}")
                except Exception as e:
                    print(f"REFUND_LIST 表: 查询失败 - {str(e)}")

                # 检查今日退款记录
                try:
                    today = datetime.now().strftime('%Y-%m-%d')
                    db.execute("SELECT COUNT(*), SUM([Refund]) FROM REFUND_LIST WHERE date([Transaction Date]) = ?", (today,))
                    result = db.fetchone()
                    today_refund_count = result[0] if result[0] is not None else 0
                    today_refund_amount = result[1] if result[1] is not None else 0
                    print(f"今日退款记录: {today_refund_count} 条, 今日退款金额: RM{today_refund_amount:.2f}")
                except Exception as e:
                    print(f"今日退款统计: 查询失败 - {str(e)}")

        except Exception as e:
            print(f"数据库验证失败: {str(e)}")

        print("\n详细日志:")
        print(f"成功日志: {SUCCESS_LOG}")
        print(f"失败日志: {FAIL_LOG}")
        print(f"备注日志: {NOTE_LOG}")
        print("="*50)
        print("全部处理完成！")  # 添加完成提示
    except Exception as e:
        log_mgr.log_note(f"主流程异常: {e}\n{traceback.format_exc()}")
        print(f"主流程异常: {e}")

if __name__ == "__main__":
    main()