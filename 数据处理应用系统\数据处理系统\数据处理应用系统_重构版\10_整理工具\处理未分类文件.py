# -*- coding: utf-8 -*-
"""
处理未分类文件脚本
将剩余的未分类文件归类到合适的目录
"""

import os
import shutil
from pathlib import Path

def move_remaining_files():
    """移动剩余的未分类文件"""
    base_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    
    # 定义剩余文件的分类
    remaining_files = {
        # 测试文件
        "04_测试文件": [
            "check_postgresql_data.py",
            "comprehensive_verification.py", 
            "simple_postgresql_test.py",
            "test_dual_database.py",
            "test_dual_import.py",
            "test_existing_postgresql.py",
            "test_migration_system.py",
            "测试Unicode修复.py",
            "测试修复结果.py",
            "测试列名映射.py"
        ],
        # 数据库工具
        "06_数据库工具": [
            "create_tables_direct.py",
            "create_views_manual.py",
            "fix_remaining_views.py",
            "recreate_views.py"
        ],
        # 备份文件
        "08_备份文件": [
            "config_before_fix_20250619_154002.ini"
        ],
        # 临时文件
        "09_临时文件": [
            "complete_migration_guide.py"
        ]
    }
    
    moved_count = 0
    
    print("📋 处理剩余未分类文件...")
    
    for category, files in remaining_files.items():
        category_dir = base_dir / category
        
        for filename in files:
            source_file = base_dir / filename
            target_file = category_dir / filename
            
            if source_file.exists():
                try:
                    shutil.move(str(source_file), str(target_file))
                    print(f"📁 {filename} → {category}")
                    moved_count += 1
                except Exception as e:
                    print(f"❌ 移动失败 {filename}: {e}")
            else:
                print(f"⚠️ 文件不存在: {filename}")
    
    print(f"\n✅ 处理完成，移动了 {moved_count} 个文件")
    
    # 检查是否还有未分类文件
    remaining = []
    for item in base_dir.iterdir():
        if item.is_file() and not item.name.startswith(("文件整理", "处理未分类", "整理报告")):
            remaining.append(item.name)
    
    if remaining:
        print(f"\n⚠️ 仍有 {len(remaining)} 个未分类文件:")
        for filename in remaining:
            print(f"  - {filename}")
    else:
        print("\n🎉 所有文件已分类完成！")

def update_directory_descriptions():
    """更新目录说明文件"""
    base_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    
    # 更新各目录的说明文件
    categories = {
        "01_主程序": "主要应用程序文件",
        "02_PostgreSQL管理": "PostgreSQL安装、管理和迁移工具", 
        "03_配置文件": "系统配置文件",
        "04_测试文件": "各种测试和验证脚本",
        "05_迁移工具": "数据迁移相关工具",
        "06_数据库工具": "数据库创建和修复工具",
        "07_文档说明": "文档和说明文件",
        "08_备份文件": "数据库备份文件",
        "09_临时文件": "临时和分析文件"
    }
    
    print("\n📝 更新目录说明...")
    
    for category, description in categories.items():
        category_dir = base_dir / category
        if category_dir.exists():
            readme_file = category_dir / "说明.txt"
            
            # 统计文件数量
            files = [f for f in category_dir.iterdir() if f.is_file() and f.name != "说明.txt"]
            
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(f"{category}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"描述: {description}\n")
                f.write(f"文件数量: {len(files)} 个\n\n")
                f.write("包含文件:\n")
                for file in sorted(files, key=lambda x: x.name):
                    f.write(f"- {file.name}\n")
                f.write(f"\n最后更新: {os.path.basename(__file__)}\n")
            
            print(f"✅ 更新 {category}: {len(files)} 个文件")

def main():
    """主函数"""
    print("🚀 处理剩余未分类文件...")
    print("=" * 50)
    
    move_remaining_files()
    update_directory_descriptions()
    
    print("\n" + "=" * 50)
    print("🎉 文件分类完全完成！")

if __name__ == "__main__":
    main()
