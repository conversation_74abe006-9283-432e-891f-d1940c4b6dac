# -*- coding: utf-8 -*-
"""
Transaction ID匹配调试脚本
分析为什么Transaction ID匹配查询失败
"""

import pandas as pd
import os

def debug_transaction_id_matching():
    """调试Transaction ID匹配问题"""
    
    print("🔍 Transaction ID匹配调试")
    print("=" * 60)
    
    # 模拟用户反馈的数据
    print("📋 用户反馈分析:")
    print("  • 匹配率: 38.8% (26个共同Transaction ID)")
    print("  • 第一文件Transaction ID数量: 67")
    print("  • 第二文件Transaction Num数量: 28")
    print("  • 但所有记录都显示'未找到匹配'")
    print("  • 所有67条记录都被插入，0条匹配")
    
    print("\n🔍 问题分析:")
    print("  1. 检测阶段能找到26个匹配的Transaction ID")
    print("  2. 但实际匹配时查询失败")
    print("  3. 可能的原因：数据类型不一致")
    
    print("\n🔧 可能的问题:")
    
    # 模拟数据类型问题
    print("📊 数据类型分析:")
    
    # 第一文件Transaction ID (通常是数字)
    first_file_trans_ids = [
        '2936647600', '2936662760', '2936728235', 
        '2936768998', '2937162972', '2937665684'
    ]
    
    # 第二文件Transaction Num (可能是不同类型)
    second_file_trans_nums = [
        2936768998.0,  # 浮点数
        2937162972,    # 整数
        '2937665684',  # 字符串
        2937802660.0,  # 浮点数
        '2937779767'   # 字符串
    ]
    
    print(f"  第一文件Transaction ID类型: {[type(x) for x in first_file_trans_ids[:3]]}")
    print(f"  第二文件Transaction Num类型: {[type(x) for x in second_file_trans_nums[:3]]}")
    
    print("\n🔍 匹配测试:")
    
    # 测试不同的匹配方式
    test_id = '2936768998'
    
    # 方式1：直接比较 (会失败)
    direct_match = test_id in [str(x) for x in second_file_trans_nums]
    print(f"  直接字符串匹配: {direct_match}")
    
    # 方式2：pandas查询 (可能失败)
    df_test = pd.DataFrame({'Transaction Num': second_file_trans_nums})
    
    # 原始查询方式
    original_query = df_test[df_test["Transaction Num"].astype(str).str.strip() == test_id]
    print(f"  原始查询结果: {len(original_query)} 条")
    
    # 显示详细的类型转换过程
    print("\n📋 详细类型转换过程:")
    for i, val in enumerate(second_file_trans_nums[:3]):
        str_val = str(val)
        stripped_val = str_val.strip()
        print(f"  {i+1}. 原值: {val} ({type(val)}) -> str: '{str_val}' -> strip: '{stripped_val}'")
        print(f"     匹配 '{test_id}': {stripped_val == test_id}")
    
    print("\n🔧 修复方案:")
    
    # 统一格式化函数
    def clean_transaction_format(value):
        """统一Transaction格式"""
        try:
            if pd.isna(value):
                return None
            str_val = str(value).strip()
            if not str_val or str_val.lower() == 'nan':
                return None
            # 如果是数字格式（包括浮点数），转换为整数字符串
            if str_val.replace('.', '').replace('-', '').isdigit():
                return str(int(float(str_val)))
            else:
                return str_val
        except:
            return None
    
    # 测试修复后的匹配
    cleaned_trans_nums = [clean_transaction_format(x) for x in second_file_trans_nums]
    cleaned_trans_nums = [x for x in cleaned_trans_nums if x is not None]
    
    print(f"  清理后的Transaction Num: {cleaned_trans_nums}")
    
    # 修复后的查询
    df_test['Transaction Num Clean'] = df_test['Transaction Num'].apply(clean_transaction_format)
    fixed_query = df_test[df_test['Transaction Num Clean'] == test_id]
    print(f"  修复后查询结果: {len(fixed_query)} 条")
    
    print("\n🎯 根本问题:")
    print("  1. 第二文件Transaction Num可能包含浮点数 (如 2936768998.0)")
    print("  2. astype(str) 会将浮点数转换为 '2936768998.0'")
    print("  3. 与第一文件的 '2936768998' 不匹配")
    print("  4. 需要统一格式化为整数字符串")
    
    print("\n✅ 解决方案:")
    print("  1. 在查询前统一格式化Transaction Num")
    print("  2. 将浮点数转换为整数字符串")
    print("  3. 确保两边格式完全一致")
    
    return True

def test_actual_data_types():
    """测试实际数据类型问题"""
    
    print("\n🔍 实际数据类型测试")
    print("=" * 60)
    
    # 模拟实际的pandas DataFrame查询
    print("📊 模拟实际查询过程:")
    
    # 创建测试数据
    test_data = {
        'Transaction Num': [
            2936768998.0,  # 浮点数
            2937162972,    # 整数  
            '2937665684',  # 字符串
            None,          # 空值
            2937802660.0   # 浮点数
        ],
        'Equipment ID': ['603010580', '603010581', '603010582', '603010583', '603010584'],
        'Order price': [5.0, 10.0, 15.0, 20.0, 25.0]
    }
    
    df2 = pd.DataFrame(test_data)
    matched_indices = set()
    
    print("📋 测试数据:")
    print(df2[['Transaction Num', 'Equipment ID']])
    
    # 测试查询
    test_trans_id = '2936768998'
    
    print(f"\n🔍 查询Transaction ID: {test_trans_id}")
    
    # 原始查询方式 (会失败)
    print("\n1. 原始查询方式:")
    try:
        trans_matches_original = df2[
            (df2["Transaction Num"].astype(str).str.strip() == test_trans_id) &
            (df2.index.map(lambda x: x not in matched_indices))
        ]
        print(f"   结果: {len(trans_matches_original)} 条匹配")
        if not trans_matches_original.empty:
            print(f"   匹配记录: {trans_matches_original.index.tolist()}")
        else:
            print("   ❌ 无匹配记录")
            
        # 显示转换过程
        print("   转换过程:")
        for idx, val in df2['Transaction Num'].items():
            str_val = str(val) if pd.notna(val) else 'NaN'
            stripped_val = str_val.strip() if pd.notna(val) else 'NaN'
            match_result = stripped_val == test_trans_id if pd.notna(val) else False
            print(f"     索引{idx}: {val} -> '{str_val}' -> '{stripped_val}' -> 匹配: {match_result}")
            
    except Exception as e:
        print(f"   ❌ 查询失败: {e}")
    
    # 修复后的查询方式
    print("\n2. 修复后查询方式:")
    try:
        # 统一格式化函数
        def clean_transaction_format(value):
            try:
                if pd.isna(value):
                    return None
                str_val = str(value).strip()
                if not str_val or str_val.lower() == 'nan':
                    return None
                if str_val.replace('.', '').replace('-', '').isdigit():
                    return str(int(float(str_val)))
                else:
                    return str_val
            except:
                return None
        
        # 应用格式化
        df2['Transaction Num Clean'] = df2['Transaction Num'].apply(clean_transaction_format)
        
        print("   格式化结果:")
        for idx, (original, cleaned) in enumerate(zip(df2['Transaction Num'], df2['Transaction Num Clean'])):
            print(f"     索引{idx}: {original} -> '{cleaned}'")
        
        # 修复后的查询
        trans_matches_fixed = df2[
            (df2['Transaction Num Clean'] == test_trans_id) &
            (df2.index.map(lambda x: x not in matched_indices))
        ]
        
        print(f"   结果: {len(trans_matches_fixed)} 条匹配")
        if not trans_matches_fixed.empty:
            print(f"   匹配记录: {trans_matches_fixed.index.tolist()}")
            print("   ✅ 匹配成功！")
        else:
            print("   ❌ 仍无匹配记录")
            
    except Exception as e:
        print(f"   ❌ 查询失败: {e}")
    
    return True

def main():
    """主函数"""
    
    print("🚀 Transaction ID匹配问题调试")
    print("=" * 80)
    
    # 执行调试
    debug_transaction_id_matching()
    test_actual_data_types()
    
    print("\n📊 调试总结:")
    print("=" * 80)
    
    print("🔍 问题确认:")
    print("  ✅ Transaction ID匹配查询失败的根本原因是数据类型不一致")
    print("  ✅ 第二文件Transaction Num包含浮点数 (如 2936768998.0)")
    print("  ✅ astype(str) 将其转换为 '2936768998.0'，与 '2936768998' 不匹配")
    
    print("\n🔧 修复方案:")
    print("  1. 在查询前统一格式化Transaction Num")
    print("  2. 使用 clean_transaction_format 函数")
    print("  3. 将浮点数转换为整数字符串")
    print("  4. 确保查询时格式完全一致")
    
    print("\n✅ 预期效果:")
    print("  • Transaction ID匹配将正常工作")
    print("  • 26个匹配的Transaction ID将被正确识别")
    print("  • 匹配/插入比例将从 0/67 改善为 ~26/41")
    
    return True

if __name__ == "__main__":
    result = main()
    input(f"\n按回车键退出... (调试{'成功' if result else '失败'})")
