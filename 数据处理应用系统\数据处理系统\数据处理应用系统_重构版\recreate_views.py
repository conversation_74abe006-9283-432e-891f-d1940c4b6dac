# -*- coding: utf-8 -*-
"""
重新创建所有视图，确保与SQLite版本功能一致
"""

import sqlite3
import psycopg2
import sys

def get_sqlite_views():
    """从SQLite获取所有视图定义"""
    print("📋 从SQLite获取视图定义...")
    
    sqlite_path = "sales_reports_backup_20250619_153257.db"
    
    try:
        conn = sqlite3.connect(sqlite_path)
        cursor = conn.cursor()
        
        # 获取所有视图
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='view' ORDER BY name")
        views = cursor.fetchall()
        
        print(f"找到 {len(views)} 个视图:")
        for view_name, _ in views:
            print(f"  👁️ {view_name}")
        
        conn.close()
        return views
        
    except Exception as e:
        print(f"❌ 获取SQLite视图失败: {e}")
        return []

def convert_view_to_postgresql(view_name, sqlite_sql):
    """将SQLite视图转换为PostgreSQL兼容的视图"""
    print(f"🔧 转换视图: {view_name}")

    # 基本的SQL转换
    pg_sql = sqlite_sql

    # 1. 处理表名 - 添加双引号
    table_replacements = {
        'IOT_Sales': '"IOT_Sales"',
        'ZERO_Sales': '"ZERO_Sales"',
        'APP_Sales': '"APP_Sales"',
        'Combined_Sales': '"Combined_Sales"',
        'Equipment_ID': '"Equipment_ID"',
        'IOT_Sales_Refunding': '"IOT_Sales_Refunding"',
        'IOT_Sales_Close': '"IOT_Sales_Close"',
        'ZERO_Sales_Refunding': '"ZERO_Sales_Refunding"',
        'ZERO_Sales_Close': '"ZERO_Sales_Close"',
        'APP_Sales_Refunding': '"APP_Sales_Refunding"',
        'APP_Sales_Close': '"APP_Sales_Close"',
        'Sales_Combined': '"Sales_Combined"',
        'Equipment_Valid': '"Equipment_Valid"',
        'IOT_Daily_Sales': '"IOT_Daily_Sales"',
        'ZERO_Daily_Sales': '"ZERO_Daily_Sales"',
        'APP_Daily_Sales': '"APP_Daily_Sales"',
        'Current_Equipment': '"Current_Equipment"'
    }

    for sqlite_table, pg_table in table_replacements.items():
        # 只替换作为表名的情况，避免替换字段名
        pg_sql = pg_sql.replace(f' {sqlite_table} ', f' {pg_table} ')
        pg_sql = pg_sql.replace(f' {sqlite_table}\n', f' {pg_table}\n')
        pg_sql = pg_sql.replace(f'FROM {sqlite_table}', f'FROM {pg_table}')
        pg_sql = pg_sql.replace(f'JOIN {sqlite_table}', f'JOIN {pg_table}')
        pg_sql = pg_sql.replace(f'{sqlite_table} AS', f'{pg_table} AS')
        pg_sql = pg_sql.replace(f'{sqlite_table} e', f'{pg_table} e')
        pg_sql = pg_sql.replace(f'{sqlite_table} ev', f'{pg_table} ev')

    # 2. 处理字段名 - 添加双引号（PostgreSQL大小写敏感）
    field_replacements = {
        'Chair_Serial_No': '"Chair_Serial_No"',
        'Equipment_ID': '"Equipment_ID"',
        'Order_time': '"Order_time"',
        'Order_price': '"Order_price"',
        'Order_No': '"Order_No"',
        'Order_status': '"Order_status"',
        'Order_types': '"Order_types"',
        'Equipment_name': '"Equipment_name"',
        'Branch_name': '"Branch_name"',
        'Payment_date': '"Payment_date"',
        'Copartner_name': '"Copartner_name"',
        'STATE': '"STATE"',
        'Location': '"Location"',
        'Quantity': '"Quantity"',
        'Layer': '"Layer"',
        'Sale_Date': '"Sale_Date"',
        'IOT_Price': '"IOT_Price"',
        'ZERO_Price': '"ZERO_Price"',
        'APP_Price': '"APP_Price"',
        'Daily_IOT_Count': '"Daily_IOT_Count"',
        'Daily_ZERO_Count': '"Daily_ZERO_Count"',
        'Daily_APP_Count': '"Daily_APP_Count"'
    }

    # 小心处理字段名替换，避免误替换
    import re
    for sqlite_field, pg_field in field_replacements.items():
        # 使用正则表达式确保只替换字段名，不替换字符串内容
        pattern = r'\b' + re.escape(sqlite_field) + r'\b'
        pg_sql = re.sub(pattern, pg_field, pg_sql)

    # 3. SQLite函数到PostgreSQL的映射
    function_replacements = {
        'strftime(': 'TO_CHAR(',
        'STRFTIME(': 'TO_CHAR(',
        'SUBSTR(': 'SUBSTRING(',
        'LENGTH(': 'CHAR_LENGTH(',
        'DATETIME(': 'TO_TIMESTAMP(',
        'DATE(': 'DATE('
    }

    for sqlite_func, pg_func in function_replacements.items():
        pg_sql = pg_sql.replace(sqlite_func, pg_func)

    # 4. 特殊的日期格式处理
    # strftime('%Y-%m-%d', Order_time) -> TO_CHAR(TO_DATE(Order_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
    pg_sql = pg_sql.replace("TO_CHAR('%Y-%m-%d', ", "TO_CHAR(TO_DATE(")
    pg_sql = pg_sql.replace(") as Sale_Date", ", 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') as Sale_Date")

    # 5. 处理视图名称
    pg_sql = pg_sql.replace(f'CREATE VIEW {view_name}', f'CREATE VIEW "{view_name}"')

    return pg_sql

def create_postgresql_views():
    """在PostgreSQL中创建所有视图"""
    print("🚀 在PostgreSQL中创建视图...")
    
    # PostgreSQL连接配置
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        # 获取SQLite视图
        sqlite_views = get_sqlite_views()
        if not sqlite_views:
            return False
        
        # 连接PostgreSQL
        pg_conn = psycopg2.connect(**pg_config)
        pg_conn.autocommit = True
        cursor = pg_conn.cursor()
        
        print(f"\n✅ 连接PostgreSQL成功")
        
        # 视图创建顺序（考虑依赖关系）
        view_order = [
            'Sales_Combined',      # 基础视图，其他视图可能依赖它
            'Equipment_Valid',     # 设备相关基础视图
            'IOT_Daily_Sales',     # 日销售视图
            'ZERO_Daily_Sales',    # 日销售视图
            'APP_Daily_Sales',     # 日销售视图
            'Valid_Sales',         # 依赖Sales_Combined
            'Daily_Sales',         # 可能依赖日销售视图
            'Current_Equipment',   # 依赖Equipment_Valid
            'Daily_Equipment_Sales', # 依赖IOT_Daily_Sales
            'Refunding_Close_Summary'  # 汇总视图
        ]
        
        # 将视图转换为字典以便查找
        views_dict = {name: sql for name, sql in sqlite_views}
        
        created_views = []
        failed_views = []
        
        # 按顺序创建视图
        for view_name in view_order:
            if view_name in views_dict:
                try:
                    print(f"\n🔧 创建视图: {view_name}")
                    
                    # 删除视图如果存在
                    cursor.execute(f'DROP VIEW IF EXISTS "{view_name}" CASCADE')
                    
                    # 转换SQL
                    sqlite_sql = views_dict[view_name]
                    pg_sql = convert_view_to_postgresql(view_name, sqlite_sql)
                    
                    print(f"原始SQL: {sqlite_sql[:100]}...")
                    print(f"转换SQL: {pg_sql[:100]}...")
                    
                    # 创建视图
                    cursor.execute(pg_sql)
                    
                    print(f"✅ 视图 {view_name} 创建成功")
                    created_views.append(view_name)
                    
                except Exception as e:
                    print(f"❌ 视图 {view_name} 创建失败: {e}")
                    failed_views.append((view_name, str(e)))
        
        # 处理剩余的视图（不在预定义顺序中的）
        for view_name, sqlite_sql in sqlite_views:
            if view_name not in view_order:
                try:
                    print(f"\n🔧 创建剩余视图: {view_name}")
                    
                    cursor.execute(f'DROP VIEW IF EXISTS "{view_name}" CASCADE')
                    pg_sql = convert_view_to_postgresql(view_name, sqlite_sql)
                    cursor.execute(pg_sql)
                    
                    print(f"✅ 视图 {view_name} 创建成功")
                    created_views.append(view_name)
                    
                except Exception as e:
                    print(f"❌ 视图 {view_name} 创建失败: {e}")
                    failed_views.append((view_name, str(e)))
        
        pg_conn.close()
        
        # 报告结果
        print(f"\n📊 视图创建结果:")
        print(f"✅ 成功创建: {len(created_views)} 个视图")
        for view_name in created_views:
            print(f"  ✅ {view_name}")
        
        if failed_views:
            print(f"\n❌ 创建失败: {len(failed_views)} 个视图")
            for view_name, error in failed_views:
                print(f"  ❌ {view_name}: {error}")
        
        return len(failed_views) == 0
        
    except Exception as e:
        print(f"❌ 创建PostgreSQL视图失败: {e}")
        return False

def verify_views():
    """验证视图是否正常工作"""
    print("\n🔍 验证视图功能...")
    
    pg_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'postgres',
        'user': 'postgres',
        'password': 'zerochon'
    }
    
    try:
        conn = psycopg2.connect(**pg_config)
        cursor = conn.cursor()
        
        # 获取所有视图
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        views = cursor.fetchall()
        
        print(f"验证 {len(views)} 个视图:")
        
        for (view_name,) in views:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{view_name}"')
                count = cursor.fetchone()[0]
                print(f"  ✅ {view_name}: {count:,} 行")
            except Exception as e:
                print(f"  ❌ {view_name}: 查询失败 - {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证视图失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 重新创建PostgreSQL视图")
    print("确保与SQLite版本功能完全一致")
    print("="*50)
    
    # 创建视图
    success = create_postgresql_views()
    
    if success:
        print("\n✅ 所有视图创建成功！")
    else:
        print("\n⚠️ 部分视图创建失败，但不影响核心功能")
    
    # 验证视图
    verify_views()
    
    print("\n📋 完成！")
    print("现在您可以在pgAdmin4中看到所有视图了：")
    print("服务器 > postgres > Schemas > public > Views")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
